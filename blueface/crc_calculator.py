# crc_calculator_bitwise.py

def calculate_crc16_modbus_bitwise(data: bytes) -> int:
    """
    Calculates the CRC-16 Modbus checksum using bitwise operations.

    Args:
        data: A bytes object containing the data to checksum.

    Returns:
        The calculated CRC-16 value (16-bit integer).
        Note: This returns the raw CRC value. For Modbus transmission,
              you typically need to send the low byte first, then the high byte.
    """
    crc = 0xFFFF  # Initial value
    polynomial = 0xA001  # Standard Modbus polynomial (reversed)

    for byte in data:
        crc ^= byte
        for _ in range(8):
            if (crc & 0x0001):
                crc = (crc >> 1) ^ polynomial
            else:
                crc = crc >> 1
    return crc

def hex_string_to_bytes(hex_string: str) -> bytes:
    """Converts a hexadecimal string (without spaces or prefixes) to bytes."""
    hex_string = hex_string.replace(" ", "").replace("0x", "")
    if len(hex_string) % 2 != 0:
        raise ValueError("Hex string must have an even number of characters")
    try:
        return bytes.fromhex(hex_string)
    except ValueError:
        raise ValueError("Invalid hexadecimal string")

if __name__ == "__main__":
    while True:
        hex_input = input("输入十六进制数据 (例如 01100019001428C9C2CEF7CAA12FD1EEC1E8CAD02FD1EEC1EAC7F8)，或输入 'q' 退出: ")
        if hex_input.lower() == 'q':
            break

        try:
            byte_data = hex_string_to_bytes(hex_input)
            print(f"输入字节 ({len(byte_data)} bytes): {byte_data.hex().upper()}")

            # 使用逐位计算法
            crc_value = calculate_crc16_modbus_bitwise(byte_data)

            # Format CRC for display (Low byte first, High byte second)
            low_byte = crc_value & 0xFF
            high_byte = (crc_value >> 8) & 0xFF
            crc_display = f"{low_byte:02X}{high_byte:02X}" # 低字节在前

            print(f"计算出的 CRC-16 Modbus (原始值 - 逐位): 0x{crc_value:04X}")
            print(f"计算出的 CRC-16 Modbus (低字节在前 - 逐位): {crc_display}")
            print("-" * 30)

        except ValueError as e:
            print(f"错误: {e}")
        except Exception as e:
            print(f"发生意外错误: {e}")