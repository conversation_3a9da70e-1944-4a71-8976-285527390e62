# 160字节焊机数据解析功能演示

## 功能概述

在焊机配置页面中，我们实现了完整的160字节焊机数据解析和显示功能。

## 主要特性

### 1. 📊 数据获取
- **获取数据按钮**: 发送Modbus查询命令获取实时焊机数据
- **测试解析按钮**: 使用模拟数据测试解析功能
- **自动重组**: 处理蓝牙MTU限制导致的数据分片

### 2. 🔍 智能解析
- **数据验证**: 检查数据长度和格式
- **字段解析**: 按照VW地址映射解析各个参数
- **错误处理**: 解析失败时显示详细错误信息

### 3. 📱 分类显示

#### 数据信息卡片 (蓝色)
- 数据长度: 160字节
- 数据格式: 十六进制
- 解析时间: 当前时间戳

#### 基础参数卡片 (绿色) - VW2530-VW2540
- 管材直径 (mm)
- 管材SDR
- 管材厚度 (mm)
- 环境温度 (°C)
- 热板温度 (°C)
- 拖动压力 (bar)

#### 工艺参数卡片 (橙色) - VW2544-VW2568
- 卷边设定/实际压力 (bar)
- 卷边设定/实际时间 (s)
- 吸热设定/实际压力 (bar)
- 吸热设定/实际时间 (s)
- 转换时间 (s)
- 增压时间 (s)
- 冷却设定/实际压力 (bar)
- 冷却时间 (s)

#### 状态信息卡片 (紫色) - VW2570
- 焊接状态码: 数值
- 焊接状态: 中文描述
- 状态描述: 详细说明

**状态码对照表:**
- 0: 待机 - 设备处于待机状态，等待操作指令
- 1: 准备中 - 设备正在准备焊接，检查各项参数
- 2: 卷边阶段 - 正在进行管材端面卷边处理
- 3: 吸热阶段 - 热板加热，管材端面吸热软化
- 4: 转换阶段 - 移除热板，准备对接
- 5: 增压阶段 - 增加压力，确保焊接质量
- 6: 冷却阶段 - 冷却阶段，等待焊缝固化
- 7: 焊接完成 - 焊接过程成功完成
- 8: 焊接失败 - 焊接过程中出现问题，需要检查
- 9: 设备故障 - 设备出现故障，需要维修

#### 时间信息卡片 (青色)
- 熔接开始日期: YYYY-MM-DD
- 熔接开始时间: HH:MM:SS
- 熔接结束日期: YYYY-MM-DD
- 熔接结束时间: HH:MM:SS

#### 原始数据预览卡片 (灰色)
- 前20字节预览
- 可展开查看完整原始数据
- 等宽字体显示十六进制数据

## 测试数据示例

点击"测试解析"按钮会加载以下模拟数据：

```
管材直径: 500 mm
管材SDR: 11
管材厚度: 45 mm
环境温度: 25 °C
热板温度: 220 °C
拖动压力: 50 bar

卷边设定压力: 60 bar
卷边实际压力: 58 bar
卷边设定时间: 30 s
卷边实际时间: 29 s

吸热设定压力: 70 bar
吸热实际压力: 69 bar
吸热设定时间: 120 s
吸热实际时间: 119 s

转换时间: 5 s
增压时间: 10 s
冷却设定压力: 80 bar
冷却实际压力: 79 bar
冷却时间: 300 s

焊接状态: 焊接完成
熔接开始时间: 2024-01-08 14:30:00
熔接结束时间: 2024-01-08 14:35:00
```

## 技术实现

### 数据解析算法
```dart
// VW地址到字节索引的转换
int _getWordValue(List<int> bytes, int wordIndex) {
  int byteIndex = wordIndex * 2;
  if (byteIndex + 1 < bytes.length) {
    // 大端序：高字节在前，低字节在后
    return (bytes[byteIndex] << 8) | bytes[byteIndex + 1];
  }
  return 0;
}
```

### 错误处理
- 数据长度验证
- 十六进制格式检查
- 字节数组转换异常捕获
- 解析过程异常处理

### 用户体验
- 彩色分类卡片
- 清晰的图标标识
- 响应式布局
- 滚动查看支持
- 可展开的原始数据

## 使用方法

1. **获取实时数据**: 点击"获取数据"按钮从焊机获取160字节数据
2. **测试功能**: 点击"测试解析"按钮查看解析效果
3. **查看详情**: 滚动浏览各个参数卡片
4. **原始数据**: 展开原始数据预览查看完整十六进制数据

这个功能为焊接工程师提供了完整的焊机状态监控和数据分析能力，支持实时监控焊接过程的各项关键参数。
