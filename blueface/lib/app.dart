import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'screens/login_screen.dart';
import 'screens/home_screen.dart';
import 'services/user_service.dart';
import 'vision_detector_views/face_detector_view.dart';
import 'screens/device_photo_screen.dart';
import 'screens/pipe_photo_screen.dart';
import 'screens/welding_screen.dart';
import 'screens/welding_config_screen.dart';
import 'screens/offline_data_screen.dart';
import 'screens/welding_joint_management_screen.dart';
import 'screens/data_upload_screen.dart';
import 'screens/welding_data_history_screen.dart';

class App extends StatefulWidget {
  const App({Key? key}) : super(key: key);

  @override
  _AppState createState() => _AppState();
}

class _AppState extends State<App> {
  final UserService _userService = UserService();
  bool _initialized = false;
  bool _isLoggedIn = false;

  @override
  void initState() {
    super.initState();
    _initApp();
  }

  Future<void> _initApp() async {
    await _userService.init();
    final isLoggedIn = await _userService.isLoggedIn();

    print('初始化应用程序 - 登录状态: $isLoggedIn');
    setState(() {
      _isLoggedIn = isLoggedIn;
      _initialized = true;
    });
    print('初始化完成 - 当前登录状态: $_isLoggedIn');
  }

  void _handleLoginResult(bool success) {
    print('处理登录结果: $success (当前状态: $_isLoggedIn)');
    setState(() {
      _isLoggedIn = success;
    });
    print('登录状态更新为: $_isLoggedIn');
  }

  @override
  Widget build(BuildContext context) {
    print('App build方法被调用 - 当前登录状态: $_isLoggedIn');
    if (!_initialized) {
      return MaterialApp(
        debugShowCheckedModeBanner: false,
        home: Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: '焊接工程管理',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        scaffoldBackgroundColor: Colors.white,
      ),
      home: _isLoggedIn
          ? HomeScreen()
          : LoginScreen(onLoginResult: _handleLoginResult),
      routes: {
        '/login': (context) => LoginScreen(onLoginResult: _handleLoginResult),
        '/home': (context) => HomeScreen(),
        '/faceDetection': (context) => FaceDetectorView(),
        '/devicePhoto': (context) => DevicePhotoScreen(),
        '/pipePhoto': (context) => PipePhotoScreen(),
        '/startWelding': (context) => WeldingScreen(),
        '/weldingConfig': (context) => WeldingConfigScreen(),
        '/offlineData': (context) => OfflineDataScreen(),
        '/weldingJointManagement': (context) => WeldingJointManagementScreen(),
        '/dataUpload': (context) => DataUploadScreen(),
        '/weldingDataHistory': (context) => WeldingDataHistoryScreen(),
      },
    );
  }
}
