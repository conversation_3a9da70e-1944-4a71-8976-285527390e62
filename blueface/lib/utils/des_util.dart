import 'dart:typed_data';

class DesUtil {
  static final DesCore _desCore = DesCore();

  /// DES加密（secretKey代表3个key，用逗号分隔）
  static String encode(String data, String secretKey) {
    if (data.isEmpty) return "";
    List<String> ks = secretKey.split(",");
    if (ks.length >= 3) {
      return _desCore.strEnc(data, ks[0], ks[1], ks[2]);
    }
    return _desCore.strEnc(data, secretKey, "", "");
  }

  /// DES解密（secretKey代表3个key，用逗号分隔）
  static String decode(String data, String secretKey) {
    if (data.isEmpty) return "";
    List<String> ks = secretKey.split(",");
    if (ks.length >= 3) {
      return _desCore.strDec(data, ks[0], ks[1], ks[2]);
    }
    return _desCore.strDec(data, secretKey, "", "");
  }
}

class DesCore {
  final List<List<List<int>>> sBox = [
    [
      [14, 4, 13, 1, 2, 15, 11, 8, 3, 10, 6, 12, 5, 9, 0, 7],
      [0, 15, 7, 4, 14, 2, 13, 1, 10, 6, 12, 11, 9, 5, 3, 8],
      [4, 1, 14, 8, 13, 6, 2, 11, 15, 12, 9, 7, 3, 10, 5, 0],
      [15, 12, 8, 2, 4, 9, 1, 7, 5, 11, 3, 14, 10, 0, 6, 13]
    ],
    [
      [15, 1, 8, 14, 6, 11, 3, 4, 9, 7, 2, 13, 12, 0, 5, 10],
      [3, 13, 4, 7, 15, 2, 8, 14, 12, 0, 1, 10, 6, 9, 11, 5],
      [0, 14, 7, 11, 10, 4, 13, 1, 5, 8, 12, 6, 9, 3, 2, 15],
      [13, 8, 10, 1, 3, 15, 4, 2, 11, 6, 7, 12, 0, 5, 14, 9]
    ],
    [
      [10, 0, 9, 14, 6, 3, 15, 5, 1, 13, 12, 7, 11, 4, 2, 8],
      [13, 7, 0, 9, 3, 4, 6, 10, 2, 8, 5, 14, 12, 11, 15, 1],
      [13, 6, 4, 9, 8, 15, 3, 0, 11, 1, 2, 12, 5, 10, 14, 7],
      [1, 10, 13, 0, 6, 9, 8, 7, 4, 15, 14, 3, 11, 5, 2, 12]
    ],
    [
      [7, 13, 14, 3, 0, 6, 9, 10, 1, 2, 8, 5, 11, 12, 4, 15],
      [13, 8, 11, 5, 6, 15, 0, 3, 4, 7, 2, 12, 1, 10, 14, 9],
      [10, 6, 9, 0, 12, 11, 7, 13, 15, 1, 3, 14, 5, 2, 8, 4],
      [3, 15, 0, 6, 10, 1, 13, 8, 9, 4, 5, 11, 12, 7, 2, 14]
    ],
    [
      [2, 12, 4, 1, 7, 10, 11, 6, 8, 5, 3, 15, 13, 0, 14, 9],
      [14, 11, 2, 12, 4, 7, 13, 1, 5, 0, 15, 10, 3, 9, 8, 6],
      [4, 2, 1, 11, 10, 13, 7, 8, 15, 9, 12, 5, 6, 3, 0, 14],
      [11, 8, 12, 7, 1, 14, 2, 13, 6, 15, 0, 9, 10, 4, 5, 3]
    ],
    [
      [12, 1, 10, 15, 9, 2, 6, 8, 0, 13, 3, 4, 14, 7, 5, 11],
      [10, 15, 4, 2, 7, 12, 9, 5, 6, 1, 13, 14, 0, 11, 3, 8],
      [9, 14, 15, 5, 2, 8, 12, 3, 7, 0, 4, 10, 1, 13, 11, 6],
      [4, 3, 2, 12, 9, 5, 15, 10, 11, 14, 1, 7, 6, 0, 8, 13]
    ],
    [
      [4, 11, 2, 14, 15, 0, 8, 13, 3, 12, 9, 7, 5, 10, 6, 1],
      [13, 0, 11, 7, 4, 9, 1, 10, 14, 3, 5, 12, 2, 15, 8, 6],
      [1, 4, 11, 13, 12, 3, 7, 14, 10, 15, 6, 8, 0, 5, 9, 2],
      [6, 11, 13, 8, 1, 4, 10, 7, 9, 5, 0, 15, 14, 2, 3, 12]
    ],
    [
      [13, 2, 8, 4, 6, 15, 11, 1, 10, 9, 3, 14, 5, 0, 12, 7],
      [1, 15, 13, 8, 10, 3, 7, 4, 12, 5, 6, 11, 0, 14, 9, 2],
      [7, 11, 4, 1, 9, 12, 14, 2, 0, 6, 10, 13, 15, 3, 5, 8],
      [2, 1, 14, 7, 4, 10, 8, 13, 15, 12, 9, 0, 3, 5, 6, 11]
    ]
  ];

  String strEnc(
      String data, String firstKey, String secondKey, String thirdKey) {
    int leng = data.length;
    String encData = "";
    List<List<int>>? firstKeyBt, secondKeyBt, thirdKeyBt;
    int firstLength = 0, secondLength = 0, thirdLength = 0;

    if (firstKey.isNotEmpty) {
      firstKeyBt = getKeyBytes(firstKey);
      firstLength = firstKeyBt.length;
    }
    if (secondKey.isNotEmpty) {
      secondKeyBt = getKeyBytes(secondKey);
      secondLength = secondKeyBt.length;
    }
    if (thirdKey.isNotEmpty) {
      thirdKeyBt = getKeyBytes(thirdKey);
      thirdLength = thirdKeyBt.length;
    }

    if (leng > 0) {
      if (leng < 4) {
        List<int> bt = strToBt(data);
        List<int>? encByte;

        if (firstKey.isNotEmpty &&
            secondKey.isNotEmpty &&
            thirdKey.isNotEmpty) {
          List<int> tempBt = bt;
          for (int x = 0; x < firstLength; x++) {
            tempBt = enc(tempBt, firstKeyBt![x]);
          }
          for (int y = 0; y < secondLength; y++) {
            tempBt = enc(tempBt, secondKeyBt![y]);
          }
          for (int z = 0; z < thirdLength; z++) {
            tempBt = enc(tempBt, thirdKeyBt![z]);
          }
          encByte = tempBt;
        } else if (firstKey.isNotEmpty && secondKey.isNotEmpty) {
          List<int> tempBt = bt;
          for (int x = 0; x < firstLength; x++) {
            tempBt = enc(tempBt, firstKeyBt![x]);
          }
          for (int y = 0; y < secondLength; y++) {
            tempBt = enc(tempBt, secondKeyBt![y]);
          }
          encByte = tempBt;
        } else if (firstKey.isNotEmpty) {
          List<int> tempBt = bt;
          for (int x = 0; x < firstLength; x++) {
            tempBt = enc(tempBt, firstKeyBt![x]);
          }
          encByte = tempBt;
        }

        encData = bt64ToHex(encByte!);
      } else {
        int iterator = (leng / 4).floor();
        int remainder = leng % 4;

        for (int i = 0; i < iterator; i++) {
          String tempData = data.substring(i * 4, i * 4 + 4);
          List<int> tempByte = strToBt(tempData);
          List<int>? encByte;

          if (firstKey.isNotEmpty &&
              secondKey.isNotEmpty &&
              thirdKey.isNotEmpty) {
            List<int> tempBt = tempByte;
            for (int x = 0; x < firstLength; x++) {
              tempBt = enc(tempBt, firstKeyBt![x]);
            }
            for (int y = 0; y < secondLength; y++) {
              tempBt = enc(tempBt, secondKeyBt![y]);
            }
            for (int z = 0; z < thirdLength; z++) {
              tempBt = enc(tempBt, thirdKeyBt![z]);
            }
            encByte = tempBt;
          } else if (firstKey.isNotEmpty && secondKey.isNotEmpty) {
            List<int> tempBt = tempByte;
            for (int x = 0; x < firstLength; x++) {
              tempBt = enc(tempBt, firstKeyBt![x]);
            }
            for (int y = 0; y < secondLength; y++) {
              tempBt = enc(tempBt, secondKeyBt![y]);
            }
            encByte = tempBt;
          } else if (firstKey.isNotEmpty) {
            List<int> tempBt = tempByte;
            for (int x = 0; x < firstLength; x++) {
              tempBt = enc(tempBt, firstKeyBt![x]);
            }
            encByte = tempBt;
          }

          encData += bt64ToHex(encByte!);
        }

        if (remainder > 0) {
          String remainderData = data.substring(iterator * 4);
          List<int> tempByte = strToBt(remainderData);
          List<int>? encByte;

          if (firstKey.isNotEmpty &&
              secondKey.isNotEmpty &&
              thirdKey.isNotEmpty) {
            List<int> tempBt = tempByte;
            for (int x = 0; x < firstLength; x++) {
              tempBt = enc(tempBt, firstKeyBt![x]);
            }
            for (int y = 0; y < secondLength; y++) {
              tempBt = enc(tempBt, secondKeyBt![y]);
            }
            for (int z = 0; z < thirdLength; z++) {
              tempBt = enc(tempBt, thirdKeyBt![z]);
            }
            encByte = tempBt;
          } else if (firstKey.isNotEmpty && secondKey.isNotEmpty) {
            List<int> tempBt = tempByte;
            for (int x = 0; x < firstLength; x++) {
              tempBt = enc(tempBt, firstKeyBt![x]);
            }
            for (int y = 0; y < secondLength; y++) {
              tempBt = enc(tempBt, secondKeyBt![y]);
            }
            encByte = tempBt;
          } else if (firstKey.isNotEmpty) {
            List<int> tempBt = tempByte;
            for (int x = 0; x < firstLength; x++) {
              tempBt = enc(tempBt, firstKeyBt![x]);
            }
            encByte = tempBt;
          }

          encData += bt64ToHex(encByte!);
        }
      }
    }
    return encData;
  }

  List<List<int>> getKeyBytes(String key) {
    List<List<int>> keyBytes = [];
    int leng = key.length;
    int iterator = (leng / 4).floor();
    int remainder = leng % 4;

    for (int i = 0; i < iterator; i++) {
      keyBytes.add(strToBt(key.substring(i * 4, i * 4 + 4)));
    }
    if (remainder > 0) {
      keyBytes.add(strToBt(key.substring(iterator * 4)));
    }
    return keyBytes;
  }

  List<int> strToBt(String str) {
    List<int> bt = List.filled(64, 0);
    int leng = str.length;
    if (leng < 4) {
      for (int i = 0; i < leng; i++) {
        int k = str.codeUnitAt(i);
        for (int j = 0; j < 16; j++) {
          int pow = 1;
          for (int m = 15; m > j; m--) {
            pow *= 2;
          }
          bt[16 * i + j] = (k ~/ pow) % 2;
        }
      }
      for (int p = leng; p < 4; p++) {
        int k = 0;
        for (int q = 0; q < 16; q++) {
          int pow = 1;
          for (int m = 15; m > q; m--) {
            pow *= 2;
          }
          bt[16 * p + q] = (k ~/ pow) % 2;
        }
      }
    } else {
      for (int i = 0; i < 4; i++) {
        int k = str.codeUnitAt(i);
        for (int j = 0; j < 16; j++) {
          int pow = 1;
          for (int m = 15; m > j; m--) {
            pow *= 2;
          }
          bt[16 * i + j] = (k ~/ pow) % 2;
        }
      }
    }
    return bt;
  }

  String bt64ToHex(List<int> byteData) {
    String hex = "";
    for (int i = 0; i < 16; i++) {
      String bt = "";
      for (int j = 0; j < 4; j++) {
        bt += byteData[i * 4 + j].toString();
      }
      hex += bt4ToHex(bt);
    }
    return hex;
  }

  String bt4ToHex(String binary) {
    switch (binary) {
      case "0000":
        return "0";
      case "0001":
        return "1";
      case "0010":
        return "2";
      case "0011":
        return "3";
      case "0100":
        return "4";
      case "0101":
        return "5";
      case "0110":
        return "6";
      case "0111":
        return "7";
      case "1000":
        return "8";
      case "1001":
        return "9";
      case "1010":
        return "A";
      case "1011":
        return "B";
      case "1100":
        return "C";
      case "1101":
        return "D";
      case "1110":
        return "E";
      case "1111":
        return "F";
      default:
        return "";
    }
  }

  List<int> enc(List<int> dataByte, List<int> keyByte) {
    List<List<int>> keys = generateKeys(keyByte);
    List<int> ipByte = initPermute(dataByte);
    List<int> ipLeft = List.filled(32, 0);
    List<int> ipRight = List.filled(32, 0);
    List<int> tempLeft = List.filled(32, 0);

    for (int k = 0; k < 32; k++) {
      ipLeft[k] = ipByte[k];
      ipRight[k] = ipByte[32 + k];
    }

    for (int i = 0; i < 16; i++) {
      for (int j = 0; j < 32; j++) {
        tempLeft[j] = ipLeft[j];
        ipLeft[j] = ipRight[j];
      }
      List<int> key = List.filled(48, 0);
      for (int m = 0; m < 48; m++) {
        key[m] = keys[i][m];
      }
      List<int> tempRight = xor(
          pPermute(sBoxPermute(xor(expandPermute(ipRight), key))), tempLeft);
      for (int n = 0; n < 32; n++) {
        ipRight[n] = tempRight[n];
      }
    }

    List<int> finalData = List.filled(64, 0);
    for (int i = 0; i < 32; i++) {
      finalData[i] = ipRight[i];
      finalData[32 + i] = ipLeft[i];
    }
    return finallyPermute(finalData);
  }

  List<List<int>> generateKeys(List<int> keyByte) {
    List<int> key = List.filled(56, 0);
    List<List<int>> keys = List.generate(16, (_) => List.filled(48, 0));
    List<int> loop = [1, 1, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 1];

    for (int i = 0; i < 7; i++) {
      for (int j = 0, k = 7; j < 8; j++, k--) {
        key[i * 8 + j] = keyByte[8 * k + i];
      }
    }

    for (int i = 0; i < 16; i++) {
      for (int j = 0; j < loop[i]; j++) {
        int tempLeft = key[0];
        int tempRight = key[28];
        for (int k = 0; k < 27; k++) {
          key[k] = key[k + 1];
          key[28 + k] = key[29 + k];
        }
        key[27] = tempLeft;
        key[55] = tempRight;
      }

      List<int> tempKey = List.filled(48, 0);
      tempKey[0] = key[13];
      tempKey[1] = key[16];
      tempKey[2] = key[10];
      tempKey[3] = key[23];
      tempKey[4] = key[0];
      tempKey[5] = key[4];
      tempKey[6] = key[2];
      tempKey[7] = key[27];
      tempKey[8] = key[14];
      tempKey[9] = key[5];
      tempKey[10] = key[20];
      tempKey[11] = key[9];
      tempKey[12] = key[22];
      tempKey[13] = key[18];
      tempKey[14] = key[11];
      tempKey[15] = key[3];
      tempKey[16] = key[25];
      tempKey[17] = key[7];
      tempKey[18] = key[15];
      tempKey[19] = key[6];
      tempKey[20] = key[26];
      tempKey[21] = key[19];
      tempKey[22] = key[12];
      tempKey[23] = key[1];
      tempKey[24] = key[40];
      tempKey[25] = key[51];
      tempKey[26] = key[30];
      tempKey[27] = key[36];
      tempKey[28] = key[46];
      tempKey[29] = key[54];
      tempKey[30] = key[29];
      tempKey[31] = key[39];
      tempKey[32] = key[50];
      tempKey[33] = key[44];
      tempKey[34] = key[32];
      tempKey[35] = key[47];
      tempKey[36] = key[43];
      tempKey[37] = key[48];
      tempKey[38] = key[38];
      tempKey[39] = key[55];
      tempKey[40] = key[33];
      tempKey[41] = key[52];
      tempKey[42] = key[45];
      tempKey[43] = key[41];
      tempKey[44] = key[49];
      tempKey[45] = key[35];
      tempKey[46] = key[28];
      tempKey[47] = key[31];

      for (int m = 0; m < 48; m++) {
        keys[i][m] = tempKey[m];
      }
    }
    return keys;
  }

  List<int> initPermute(List<int> originalData) {
    List<int> ipByte = List.filled(64, 0);
    for (int i = 0, m = 1, n = 0; i < 4; i++, m += 2, n += 2) {
      for (int j = 7, k = 0; j >= 0; j--, k++) {
        ipByte[i * 8 + k] = originalData[j * 8 + m];
        ipByte[i * 8 + k + 32] = originalData[j * 8 + n];
      }
    }
    return ipByte;
  }

  List<int> expandPermute(List<int> rightData) {
    List<int> epByte = List.filled(48, 0);
    for (int i = 0; i < 8; i++) {
      if (i == 0) {
        epByte[i * 6] = rightData[31];
      } else {
        epByte[i * 6] = rightData[i * 4 - 1];
      }
      epByte[i * 6 + 1] = rightData[i * 4];
      epByte[i * 6 + 2] = rightData[i * 4 + 1];
      epByte[i * 6 + 3] = rightData[i * 4 + 2];
      epByte[i * 6 + 4] = rightData[i * 4 + 3];
      if (i == 7) {
        epByte[i * 6 + 5] = rightData[0];
      } else {
        epByte[i * 6 + 5] = rightData[i * 4 + 4];
      }
    }
    return epByte;
  }

  List<int> xor(List<int> byteOne, List<int> byteTwo) {
    List<int> xorByte = List.filled(byteOne.length, 0);
    for (int i = 0; i < byteOne.length; i++) {
      xorByte[i] = byteOne[i] ^ byteTwo[i];
    }
    return xorByte;
  }

  List<int> pPermute(List<int> sBoxByte) {
    List<int> pByte = List.filled(32, 0);
    pByte[0] = sBoxByte[15];
    pByte[1] = sBoxByte[6];
    pByte[2] = sBoxByte[19];
    pByte[3] = sBoxByte[20];
    pByte[4] = sBoxByte[28];
    pByte[5] = sBoxByte[11];
    pByte[6] = sBoxByte[27];
    pByte[7] = sBoxByte[16];
    pByte[8] = sBoxByte[0];
    pByte[9] = sBoxByte[14];
    pByte[10] = sBoxByte[22];
    pByte[11] = sBoxByte[25];
    pByte[12] = sBoxByte[4];
    pByte[13] = sBoxByte[17];
    pByte[14] = sBoxByte[30];
    pByte[15] = sBoxByte[9];
    pByte[16] = sBoxByte[1];
    pByte[17] = sBoxByte[7];
    pByte[18] = sBoxByte[23];
    pByte[19] = sBoxByte[13];
    pByte[20] = sBoxByte[31];
    pByte[21] = sBoxByte[26];
    pByte[22] = sBoxByte[2];
    pByte[23] = sBoxByte[8];
    pByte[24] = sBoxByte[18];
    pByte[25] = sBoxByte[12];
    pByte[26] = sBoxByte[29];
    pByte[27] = sBoxByte[5];
    pByte[28] = sBoxByte[21];
    pByte[29] = sBoxByte[10];
    pByte[30] = sBoxByte[3];
    pByte[31] = sBoxByte[24];
    return pByte;
  }

  List<int> sBoxPermute(List<int> expandByte) {
    List<int> sBoxByte = List.filled(32, 0);
    String binary = "";
    for (int i = 0; i < 8; i++) {
      int row = expandByte[i * 6] * 2 + expandByte[i * 6 + 5];
      int col = expandByte[i * 6 + 1] * 8 +
          expandByte[i * 6 + 2] * 4 +
          expandByte[i * 6 + 3] * 2 +
          expandByte[i * 6 + 4];
      int value = sBox[i][row][col];
      for (int j = 0; j < 4; j++) {
        sBoxByte[i * 4 + j] = (value >> (3 - j)) & 0x1;
      }
    }
    return sBoxByte;
  }

  List<int> finallyPermute(List<int> endByte) {
    List<int> fpByte = List.filled(64, 0);
    fpByte[0] = endByte[39];
    fpByte[1] = endByte[7];
    fpByte[2] = endByte[47];
    fpByte[3] = endByte[15];
    fpByte[4] = endByte[55];
    fpByte[5] = endByte[23];
    fpByte[6] = endByte[63];
    fpByte[7] = endByte[31];
    fpByte[8] = endByte[38];
    fpByte[9] = endByte[6];
    fpByte[10] = endByte[46];
    fpByte[11] = endByte[14];
    fpByte[12] = endByte[54];
    fpByte[13] = endByte[22];
    fpByte[14] = endByte[62];
    fpByte[15] = endByte[30];
    fpByte[16] = endByte[37];
    fpByte[17] = endByte[5];
    fpByte[18] = endByte[45];
    fpByte[19] = endByte[13];
    fpByte[20] = endByte[53];
    fpByte[21] = endByte[21];
    fpByte[22] = endByte[61];
    fpByte[23] = endByte[29];
    fpByte[24] = endByte[36];
    fpByte[25] = endByte[4];
    fpByte[26] = endByte[44];
    fpByte[27] = endByte[12];
    fpByte[28] = endByte[52];
    fpByte[29] = endByte[20];
    fpByte[30] = endByte[60];
    fpByte[31] = endByte[28];
    fpByte[32] = endByte[35];
    fpByte[33] = endByte[3];
    fpByte[34] = endByte[43];
    fpByte[35] = endByte[11];
    fpByte[36] = endByte[51];
    fpByte[37] = endByte[19];
    fpByte[38] = endByte[59];
    fpByte[39] = endByte[27];
    fpByte[40] = endByte[34];
    fpByte[41] = endByte[2];
    fpByte[42] = endByte[42];
    fpByte[43] = endByte[10];
    fpByte[44] = endByte[50];
    fpByte[45] = endByte[18];
    fpByte[46] = endByte[58];
    fpByte[47] = endByte[26];
    fpByte[48] = endByte[33];
    fpByte[49] = endByte[1];
    fpByte[50] = endByte[41];
    fpByte[51] = endByte[9];
    fpByte[52] = endByte[49];
    fpByte[53] = endByte[17];
    fpByte[54] = endByte[57];
    fpByte[55] = endByte[25];
    fpByte[56] = endByte[32];
    fpByte[57] = endByte[0];
    fpByte[58] = endByte[40];
    fpByte[59] = endByte[8];
    fpByte[60] = endByte[48];
    fpByte[61] = endByte[16];
    fpByte[62] = endByte[56];
    fpByte[63] = endByte[24];
    return fpByte;
  }

  String strDec(
      String data, String firstKey, String secondKey, String thirdKey) {
    int leng = data.length;
    String decStr = "";
    List<List<int>>? firstKeyBt, secondKeyBt, thirdKeyBt;
    int firstLength = 0, secondLength = 0, thirdLength = 0;

    if (firstKey.isNotEmpty) {
      firstKeyBt = getKeyBytes(firstKey);
      firstLength = firstKeyBt.length;
    }
    if (secondKey.isNotEmpty) {
      secondKeyBt = getKeyBytes(secondKey);
      secondLength = secondKeyBt.length;
    }
    if (thirdKey.isNotEmpty) {
      thirdKeyBt = getKeyBytes(thirdKey);
      thirdLength = thirdKeyBt.length;
    }

    int iterator = leng ~/ 16;
    List<int> decByte = [];
    for (int i = 0; i < iterator; i++) {
      String str = data.substring(i * 16 + 0, i * 16 + 16);
      List<int> decByteItem = hexToBt64(str);
      List<int> tempByte = decByteItem;

      if (firstKey.isNotEmpty && secondKey.isNotEmpty && thirdKey.isNotEmpty) {
        for (int x = thirdLength - 1; x >= 0; x--) {
          tempByte = dec(tempByte, thirdKeyBt![x]);
        }
        for (int y = secondLength - 1; y >= 0; y--) {
          tempByte = dec(tempByte, secondKeyBt![y]);
        }
        for (int z = firstLength - 1; z >= 0; z--) {
          tempByte = dec(tempByte, firstKeyBt![z]);
        }
      } else if (firstKey.isNotEmpty && secondKey.isNotEmpty) {
        for (int x = secondLength - 1; x >= 0; x--) {
          tempByte = dec(tempByte, secondKeyBt![x]);
        }
        for (int y = firstLength - 1; y >= 0; y--) {
          tempByte = dec(tempByte, firstKeyBt![y]);
        }
      } else if (firstKey.isNotEmpty) {
        for (int x = firstLength - 1; x >= 0; x--) {
          tempByte = dec(tempByte, firstKeyBt![x]);
        }
      }
      decByte.addAll(tempByte);
    }

    String decStr1 = byteToString(decByte);
    return decStr1;
  }

  List<int> hexToBt64(String hex) {
    List<int> binary = List.filled(64, 0);
    for (int i = 0; i < 16; i++) {
      String bt = hexToBt4(hex[i]);
      for (int j = 0; j < 4; j++) {
        binary[i * 4 + j] = int.parse(bt[j]);
      }
    }
    return binary;
  }

  String hexToBt4(String hex) {
    String binary = "";
    switch (hex.toUpperCase()) {
      case "0":
        binary = "0000";
        break;
      case "1":
        binary = "0001";
        break;
      case "2":
        binary = "0010";
        break;
      case "3":
        binary = "0011";
        break;
      case "4":
        binary = "0100";
        break;
      case "5":
        binary = "0101";
        break;
      case "6":
        binary = "0110";
        break;
      case "7":
        binary = "0111";
        break;
      case "8":
        binary = "1000";
        break;
      case "9":
        binary = "1001";
        break;
      case "A":
        binary = "1010";
        break;
      case "B":
        binary = "1011";
        break;
      case "C":
        binary = "1100";
        break;
      case "D":
        binary = "1101";
        break;
      case "E":
        binary = "1110";
        break;
      case "F":
        binary = "1111";
        break;
    }
    return binary;
  }

  String byteToString(List<int> byteData) {
    String str = "";
    for (int i = 0; i < byteData.length; i += 8) {
      int sum = 0;
      for (int j = 0; j < 8; j++) {
        sum += byteData[i + j] * (1 << (7 - j));
      }
      str += String.fromCharCode(sum);
    }
    return str;
  }

  List<int> dec(List<int> dataByte, List<int> keyByte) {
    List<List<int>> keys = generateKeys(keyByte);
    List<int> ipByte = initPermute(dataByte);
    List<int> ipLeft = List.filled(32, 0);
    List<int> ipRight = List.filled(32, 0);
    List<int> tempLeft = List.filled(32, 0);

    for (int k = 0; k < 32; k++) {
      ipLeft[k] = ipByte[k];
      ipRight[k] = ipByte[32 + k];
    }

    for (int i = 15; i >= 0; i--) {
      for (int j = 0; j < 32; j++) {
        tempLeft[j] = ipLeft[j];
        ipLeft[j] = ipRight[j];
      }
      List<int> key = List.filled(48, 0);
      for (int m = 0; m < 48; m++) {
        key[m] = keys[i][m];
      }
      List<int> tempRight = xor(
          pPermute(sBoxPermute(xor(expandPermute(ipRight), key))), tempLeft);
      for (int n = 0; n < 32; n++) {
        ipRight[n] = tempRight[n];
      }
    }

    List<int> finalData = List.filled(64, 0);
    for (int i = 0; i < 32; i++) {
      finalData[i] = ipRight[i];
      finalData[32 + i] = ipLeft[i];
    }
    return finallyPermute(finalData);
  }
}
