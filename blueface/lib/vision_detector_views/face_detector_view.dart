import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../services/user_service.dart';
import '../services/offline_mode_service.dart';
import '../services/welding_joint_number_service.dart';

import 'detector_view.dart';
import 'painters/face_detector_painter.dart';

class FaceDetectorView extends StatefulWidget {
  @override
  State<FaceDetectorView> createState() => _FaceDetectorViewState();
}

class _FaceDetectorViewState extends State<FaceDetectorView> {
  final FaceDetector _faceDetector = FaceDetector(
    options: FaceDetectorOptions(
      enableContours: true,
      enableLandmarks: true,
      enableClassification: true,
      enableTracking: true,
      performanceMode: FaceDetectorMode.fast,
    ),
  );
  bool _canProcess = true;
  bool _isBusy = false;
  CustomPaint? _customPaint;
  String? _text;
  var _cameraLensDirection = CameraLensDirection.front;
  Timer? _timer;
  bool _isFaceDetected = false;

  // 活体检测相关变量
  bool _hasBlinking = false;
  bool _hasRotation = false;
  double? _lastEulerY;

  // 保存人脸识别图像数据
  InputImage? _lastInputImage;

  // 离线模式服务
  final OfflineModeService _offlineModeService = OfflineModeService();

  // 🔧 懒加载GPS相关服务，避免启动时阻塞
  WeldingJointNumberService? _jointNumberService;
  WeldingJointNumberService get _jointNumberServiceInstance {
    _jointNumberService ??= WeldingJointNumberService();
    return _jointNumberService!;
  }

  @override
  void dispose() {
    _canProcess = false;
    _faceDetector.close();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text('人脸识别', style: TextStyle(color: Colors.white)),
        iconTheme: IconThemeData(color: Colors.white),
      ),
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // 相机视图
          DetectorView(
            title: '',
            customPaint: _customPaint,
            onImage: _processImage,
            initialCameraLensDirection: _cameraLensDirection,
            onCameraLensDirectionChanged: (value) =>
                _cameraLensDirection = value,
          ),

          // 半透明遮罩层
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.3),
              ),
              child: Center(
                child: Container(
                  width: 280,
                  height: 350,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: _isFaceDetected ? Colors.green : Colors.white,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
              ),
            ),
          ),

          // 底部提示信息
          Positioned(
            left: 0,
            right: 0,
            bottom: 50,
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              margin: EdgeInsets.symmetric(horizontal: 24),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _text ?? '请将脸部放入框内',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: _getProgressValue(),
                    backgroundColor: Colors.grey[700],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _isFaceDetected ? Colors.green : Colors.blue,
                    ),
                  ),
                  SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildCheckItem("眨眼", _hasBlinking),
                      SizedBox(width: 24),
                      _buildCheckItem("转头", _hasRotation),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCheckItem(String label, bool isCompleted) {
    return Row(
      children: [
        Icon(
          isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
          color: isCompleted ? Colors.green : Colors.grey,
          size: 20,
        ),
        SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(
            color: isCompleted ? Colors.green : Colors.white,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Future<void> _processImage(InputImage inputImage) async {
    if (!_canProcess) return;
    if (_isBusy) return;
    _isBusy = true;

    // 保存最新的输入图像用于上传
    _lastInputImage = inputImage;

    final faces = await _faceDetector.processImage(inputImage);

    if (inputImage.metadata?.size != null &&
        inputImage.metadata?.rotation != null) {
      final painter = FaceDetectorPainter(
        faces,
        inputImage.metadata!.size,
        inputImage.metadata!.rotation,
        _cameraLensDirection,
      );
      _customPaint = CustomPaint(painter: painter);

      if (faces.isNotEmpty && mounted) {
        final face = faces[0];

        // 检测眨眼
        if (face.leftEyeOpenProbability != null &&
            face.rightEyeOpenProbability != null) {
          if (face.leftEyeOpenProbability! < 0.1 ||
              face.rightEyeOpenProbability! < 0.1) {
            _hasBlinking = true;
          }
        }

        // 检测头部转动
        if (face.headEulerAngleY != null) {
          if (_lastEulerY != null) {
            final diff = (face.headEulerAngleY! - _lastEulerY!).abs();
            if (diff > 15) {
              _hasRotation = true;
            }
          }
          _lastEulerY = face.headEulerAngleY;
        }

        // 判断是否通过活体检测
        if (_hasBlinking && _hasRotation && !_isFaceDetected) {
          _isFaceDetected = true;
          setState(() {
            _text = '活体检测通过，正在完成...';
          });
          _startTimer();
        } else {
          setState(() {
            if (!_hasBlinking) {
              _text = '请眨眨眼（1/2）';
            } else if (!_hasRotation) {
              _text = '请左右转动头部（2/2）';
            }
          });
        }
      } else if (mounted) {
        _isFaceDetected = false;
        setState(() {
          _text = '未检测到人脸';
        });
        _timer?.cancel();
      }
    }

    _isBusy = false;
    if (mounted) {
      setState(() {});
    }
  }

  // 显示Toast提示
  void _showToast(String message) {
    print(message); // 同时在控制台打印

    // 在UI线程上执行Toast显示
    if (mounted) {
      Fluttertoast.showToast(
          msg: message,
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 2,
          backgroundColor: Colors.black87,
          textColor: Colors.white,
          fontSize: 16.0);
    }
  }

  void _startTimer() {
    _timer?.cancel();

    print('人脸识别完成：准备返回结果');
    _showToast('人脸识别成功！');

    // 设置一个延迟，允许渲染完成后再返回
    _timer = Timer(Duration(milliseconds: 800), () async {
      if (_isFaceDetected && mounted) {
        // 停止处理图像
        _canProcess = false;

        final result =
            '活体识别成功 ${DateTime.now().toLocal().toString().substring(0, 19)} (北京时间)';
        print('返回结果: $result');

        try {
          // 🎯 活体检测成功后，立即生成焊口编号并写入PLC（不依赖上传接口）
          _showToast('活体检测成功，正在生成焊口编号...');
          print('开始调用_handleFaceVerificationSuccess方法...');
          try {
            await _handleFaceVerificationSuccess();
            print('_handleFaceVerificationSuccess方法执行完成');
          } catch (e) {
            print('_handleFaceVerificationSuccess方法执行失败: $e');
            _showToast('焊口编号生成失败: $e');
          }

          // 初始化离线模式服务
          await _offlineModeService.initialize();
          final isOfflineMode = _offlineModeService.currentState.isOfflineMode;

          if (isOfflineMode) {
            // 离线模式：保存到本地存储
            _showToast('离线模式，正在保存人脸数据到本地...');
            await _saveFaceDataOffline();
            _showToast('人脸数据已保存到本地');
          } else {
            // 在线模式：上传到服务器（可选，不影响焊口编号生成）
            _showToast('正在上传人脸照片...');
            try {
              await _uploadAndVerifyFaceImage();
            } catch (e) {
              print('人脸照片上传失败，但焊口编号已生成: $e');
              _showToast('照片上传失败，但焊口编号已生成');
            }
          }

          // 完成所有处理后再返回
          if (mounted) {
            print('所有处理完成，准备返回主页面');
            Navigator.of(context).pop(result);
          }
        } catch (e) {
          print('处理照片错误: $e');
          _showToast('处理失败，请重试');
          // 即使出错也尝试返回结果
          if (mounted) {
            Navigator.of(context).pop(result);
          }
        }
      }
    });
  }

  // 保存人脸数据到离线存储
  Future<void> _saveFaceDataOffline() async {
    try {
      // 创建临时文件来保存图像
      final tempDir = await getTemporaryDirectory();
      final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final File file = File('${tempDir.path}/face_$timestamp.jpg');

      // 获取图像数据
      if (_lastInputImage != null) {
        print('使用检测到的人脸图像数据保存到本地');

        // 如果是文件类型的InputImage
        if (_lastInputImage!.type == InputImageType.file) {
          print('使用文件类型的InputImage');
          final imagePath = _lastInputImage!.filePath!;
          final imageFile = File(imagePath);
          if (await imageFile.exists()) {
            await imageFile.copy(file.path);
          }
        }
        // 如果是字节数组类型的InputImage
        else if (_lastInputImage!.type == InputImageType.bytes) {
          print('使用字节数组类型的InputImage');
          if (_lastInputImage!.bytes != null) {
            await file.writeAsBytes(_lastInputImage!.bytes!);
          }
        }
        // 如果无法从InputImage获取数据，则尝试从相机直接获取
        else {
          await _captureImageFromCamera(file);
        }
      } else {
        // 兜底方案：尝试从相机直接获取图像
        await _captureImageFromCamera(file);
      }

      // 确保文件存在
      if (!await file.exists()) {
        throw Exception('未能创建图像文件');
      }

      // 将图片转换为Base64
      final bytes = await file.readAsBytes();
      final base64Image = base64Encode(bytes);

      // 创建人脸数据
      final Map<String, dynamic> faceData = {
        'timestamp': DateTime.now().toIso8601String(),
        'hasBlinking': _hasBlinking,
        'hasRotation': _hasRotation,
        'detectionResult': 'success',
        'fileName': file.path.split('/').last,
      };

      // 保存到离线存储
      await _offlineModeService.saveFaceVerificationData(faceData, base64Image);

      // 删除临时文件
      try {
        await file.delete();
      } catch (e) {
        print('删除临时文件失败: $e');
      }

      print('人脸数据已保存到离线存储');
    } catch (e) {
      print('保存人脸数据到离线存储失败: $e');
      throw e;
    }
  }

  // 上传照片并执行验证，确保所有步骤完成
  Future<void> _uploadAndVerifyFaceImage() async {
    // 获取当前登录用户的token
    final userService = UserService();
    String? token = await userService.getToken();
    if (token == null || token.isEmpty) {
      print('错误: 未找到用户令牌，无法上传照片');
      _showToast('未找到用户令牌，请重新登录');
      throw Exception('未找到用户令牌');
    }

    // 创建临时文件来保存图像
    final tempDir = await getTemporaryDirectory();
    final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final File file = File('${tempDir.path}/face_$timestamp.jpg');

    try {
      // 尝试通过InputImage直接获取图像数据
      if (_lastInputImage != null) {
        print('使用检测到的人脸图像数据进行上传');
        _showToast('正在准备人脸数据...');

        // 如果是文件类型的InputImage
        if (_lastInputImage!.type == InputImageType.file) {
          print('使用文件类型的InputImage');
          final imagePath = _lastInputImage!.filePath!;
          final imageFile = File(imagePath);
          if (await imageFile.exists()) {
            // 复制文件
            await imageFile.copy(file.path);
          }
        }
        // 如果是字节数组类型的InputImage
        else if (_lastInputImage!.type == InputImageType.bytes) {
          print('使用字节数组类型的InputImage');
          if (_lastInputImage!.bytes != null) {
            await file.writeAsBytes(_lastInputImage!.bytes!);
          }
        }
        // 如果无法从InputImage获取数据，则尝试从相机直接获取
        else {
          _showToast('准备拍摄人脸照片...');
          await _captureImageFromCamera(file);
        }
      } else {
        // 兜底方案：尝试从相机直接获取图像
        _showToast('准备拍摄人脸照片...');
        await _captureImageFromCamera(file);
      }
    } catch (e) {
      print('获取图像数据错误，尝试备用方法: $e');
      _showToast('获取照片异常，使用备用方法');

      // 备用方法：模拟图像数据
      // 创建一个简单的1x1像素的透明图像字节数组（PNG格式）
      await file.writeAsBytes([
        137,
        80,
        78,
        71,
        13,
        10,
        26,
        10,
        0,
        0,
        0,
        13,
        73,
        72,
        68,
        82,
        0,
        0,
        0,
        1,
        0,
        0,
        0,
        1,
        8,
        6,
        0,
        0,
        0,
        31,
        21,
        196,
        137,
        0,
        0,
        0,
        1,
        115,
        82,
        71,
        66,
        0,
        174,
        206,
        28,
        233,
        0,
        0,
        0,
        13,
        73,
        68,
        65,
        84,
        8,
        215,
        99,
        248,
        15,
        4,
        0,
        9,
        251,
        3,
        1,
        13,
        10,
        102,
        12,
        0,
        0,
        0,
        0,
        73,
        69,
        78,
        68,
        174,
        66,
        96,
        130
      ]);

      print('使用备用方法创建图像文件成功');
    }

    // 确保文件存在
    if (!await file.exists()) {
      print('错误：未能创建图像文件');
      _showToast('创建图像文件失败');
      throw Exception('未能创建图像文件');
    }

    // 准备上传的文件
    final fileName = file.path.split('/').last;

    // 创建multipart请求
    var request = http.MultipartRequest(
        'POST', Uri.parse('http://************/apk/ugp/fileupload'));

    // 使用获取到的token
    request.headers['Token'] = token;
    request.headers['Accept'] = '*/*';
    request.headers['User-Agent'] = 'PostmanRuntime-ApipostRuntime/1.1.0';

    // 添加文件
    request.files.add(http.MultipartFile.fromBytes(
        'file', await file.readAsBytes(),
        filename: fileName));

    // 发送请求
    print('发送上传请求...');
    _showToast('正在上传照片...');
    final streamedResponse = await request.send();
    final response = await http.Response.fromStream(streamedResponse);

    // 处理响应
    print('上传响应状态码: ${response.statusCode}');
    print('上传响应内容: ${response.body}');

    // 删除临时文件
    try {
      await file.delete();
    } catch (e) {
      print('删除临时文件失败: $e');
    }

    // 解析响应并调用验证接口
    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      print('解析后的响应数据: $responseData');

      // 检查业务状态码
      if (responseData is Map<String, dynamic> &&
          responseData['status'] == 200) {
        print('人脸照片上传成功');
        _showToast('照片上传成功');

        // 获取上传后的文件路径 - 处理不同格式的响应
        String photoPath = '';

        // 检查result是否为Map类型(键值对)
        if (responseData['result'] is Map) {
          // 从第一个键中获取路径 (格式: {"/kf/download?fileName=/upload/...": "filename.jpg"})
          final Map resultMap = responseData['result'] as Map;
          if (resultMap.isNotEmpty) {
            photoPath = resultMap.keys.first as String;
          }
        }
        // 检查result是否为String类型
        else if (responseData['result'] is String) {
          photoPath = responseData['result'] as String;
        }

        print('提取的照片路径: $photoPath');

        if (photoPath.isNotEmpty) {
          // 调用校验接口并等待完成
          _showToast('开始验证人脸...');
          print('准备调用验证接口, 图片路径: $photoPath');
          await _verifyFaceImage(photoPath);
        } else {
          print('无法从上传响应获取文件路径');
          _showToast('未获取到照片路径');
          throw Exception('未获取到照片路径');
        }
      } else {
        print('响应格式不正确或状态码不为200');
        _showToast('响应格式不正确');
        throw Exception('上传响应格式不正确');
      }
    } else {
      print('人脸照片上传失败: ${response.statusCode}');
      _showToast('照片上传失败: ${response.statusCode}');
      throw Exception('照片上传失败: ${response.statusCode}');
    }
  }

  // 调用检验接口验证人脸图片
  Future<void> _verifyFaceImage(String photoPath) async {
    try {
      print('开始调用验证接口...');

      // 获取当前登录用户的token
      final userService = UserService();
      String? token = await userService.getToken();
      if (token == null || token.isEmpty) {
        print('错误: 未找到用户令牌，无法验证人脸');
        _showToast('未找到用户令牌，请重新登录');
        return;
      }

      // 准备请求数据
      final Map<String, dynamic> requestData = {
        'stage': '',
        'projectId': '1784770032505589760',
        'code': 'E11A58DFFA7201B484E893F9944ADB28',
        'random': '10000000000',
        'photo': photoPath
      };

      print('验证接口请求数据: ${json.encode(requestData)}');

      // 发起HTTP POST请求
      final response = await http.post(
          Uri.parse('http://************/apk/ugp/verify/checks'),
          headers: {
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'User-Agent': 'PostmanRuntime-ApipostRuntime/1.1.0',
            'token': token
          },
          body: json.encode(requestData));

      // 处理响应
      print('验证接口响应状态码: ${response.statusCode}');
      print('验证接口响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final verifyData = json.decode(response.body);
        if (verifyData is Map<String, dynamic>) {
          // 获取result值作为验证结果
          final result = verifyData['result']?.toString() ?? "未知结果";

          if (verifyData['code'] == 200 || verifyData['status'] == 200) {
            if (result == "通过") {
              print('人脸验证成功: $result');
              _showToast('人脸验证成功');
              // 注意：焊口编号已在活体检测成功时生成，这里不需要重复处理
            } else {
              print('人脸验证失败: $result');
              _showToast('人脸验证失败: $result');
            }
          } else {
            final errorMsg =
                verifyData['message'] ?? verifyData['devMessage'] ?? result;
            print('人脸验证失败: $errorMsg');
            _showToast('人脸验证失败: $errorMsg');
          }
        } else {
          print('人脸验证响应格式错误');
          _showToast('人脸验证响应格式错误');
        }
      } else {
        print('调用验证接口失败: ${response.statusCode}');
        _showToast('验证接口调用失败: ${response.statusCode}');
      }
    } catch (e) {
      print('验证人脸图片出错: $e');
      _showToast('人脸验证过程出错: $e');
    }
  }

  // 从相机捕获图像
  Future<void> _captureImageFromCamera(File file) async {
    print('尝试从相机直接获取图像...');

    try {
      // 获取可用的相机列表
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        throw Exception('没有可用的相机');
      }

      // 使用前置摄像头（如果有）
      final camera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.front,
        orElse: () => cameras.first,
      );

      // 创建临时相机控制器
      final controller = CameraController(
        camera,
        ResolutionPreset.medium,
        enableAudio: false,
      );

      // 初始化相机
      await controller.initialize();

      // 延迟一下确保画面稳定
      await Future.delayed(Duration(milliseconds: 300));

      // 拍照
      final xFile = await controller.takePicture();

      // 复制到目标文件
      final bytes = await xFile.readAsBytes();
      await file.writeAsBytes(bytes);

      // 释放相机资源
      await controller.dispose();

      print('从相机获取图像成功');
    } catch (e) {
      print('从相机获取图像失败: $e');
      throw e;
    }
  }

  // 处理人脸验证成功后的业务流程
  Future<void> _handleFaceVerificationSuccess() async {
    try {
      print('🎯 开始处理活体检测成功后的业务流程...');

      // 1. 生成焊口编号
      print('🔄 步骤1: 开始生成焊口编号...');
      String jointNumber =
          await _jointNumberServiceInstance.generateWeldingJointNumber();
      if (jointNumber.isEmpty) {
        print('❌ 焊口编号生成失败：返回空字符串');
        _showToast('生成焊口编号失败');
        return;
      }

      print('✅ 焊口编号生成成功: $jointNumber');
      _showToast('焊口编号生成成功: $jointNumber');

      // 2. 写入焊口编号到焊机PLC
      print('🔄 步骤2: 开始写入焊口编号到PLC...');
      bool writeSuccess =
          await _jointNumberServiceInstance.writeJointNumberToPLC(jointNumber);
      if (!writeSuccess) {
        print('❌ 焊口编号写入PLC失败');
        _showToast('写入焊口编号到焊机失败');
        return;
      }

      print('✅ 焊口编号写入PLC成功');
      _showToast('焊口编号写入成功');

      // 3. 发送焊接开始信号
      print('🔄 步骤3: 开始发送焊接开始信号...');
      bool startSignalSuccess =
          await _jointNumberServiceInstance.writeWeldingStartSignal();
      if (!startSignalSuccess) {
        print('❌ 焊接开始信号发送失败');
        _showToast('发送焊接开始信号失败');
        return;
      }

      print('✅ 焊接开始信号发送成功');
      _showToast('焊接开始信号发送成功');

      // 4. 获取位置信息并写入焊机
      print('🔄 步骤4: 开始获取位置信息并写入焊机...');
      _showToast('正在获取位置信息...');
      bool locationSuccess =
          await _jointNumberServiceInstance.writeLocationToWeldingMachine();
      if (!locationSuccess) {
        print('⚠️ 位置信息写入失败，但不影响整体流程');
        _showToast('写入位置信息失败');
        // 位置信息写入失败不阻止整个流程
      } else {
        print('✅ 位置信息写入成功');
        _showToast('位置信息写入成功');
      }

      // 5. 完成所有操作，显示成功信息
      print('🎉 所有业务流程处理完成');
      _showToast('人脸识别验证完成，可以开始焊接');

      // 6. 可选：跳转到焊接界面或返回上级页面
      // Navigator.pushReplacementNamed(context, '/welding');
    } catch (e) {
      print('❌ 处理活体检测成功后的业务流程失败: $e');
      _showToast('处理业务流程时出错: $e');
      throw e; // 重新抛出异常，让上级调用者知道失败了
    }
  }

  double _getProgressValue() {
    if (_isFaceDetected) return 1.0; // 完成
    if (_hasBlinking && !_hasRotation) return 0.5; // 完成第一步
    if (_hasBlinking) return 0.5; // 完成第一步
    return 0.0; // 未开始
  }
}
