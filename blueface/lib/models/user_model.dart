import 'dart:convert';

class User {
  final String id;
  final String username;
  final String? name;
  final String? role;
  final String? department;
  final String? email;
  final String? phone;
  final String? avatar;
  final String? lastLogin;
  final Map<String, dynamic> additionalInfo;

  User({
    required this.id,
    required this.username,
    this.name,
    this.role,
    this.department,
    this.email,
    this.phone,
    this.avatar,
    this.lastLogin,
    Map<String, dynamic>? additionalInfo,
  }) : this.additionalInfo = additionalInfo ?? {};

  // 从JSON映射创建用户
  factory User.fromJson(Map<String, dynamic> json) {
    Map<String, dynamic> additionalInfo = Map.from(json);

    // 移除已经映射到具体字段的数据
    [
      'id',
      'username',
      'name',
      'role',
      'department',
      'email',
      'phone',
      'avatar',
      'lastLogin'
    ].forEach((field) => additionalInfo.remove(field));

    return User(
      id: json['id']?.toString() ?? '未知ID',
      username: json['username']?.toString() ?? '未知用户名',
      name: json['name']?.toString(),
      role: json['role']?.toString(),
      department: json['department']?.toString(),
      email: json['email']?.toString(),
      phone: json['phone']?.toString(),
      avatar: json['avatar']?.toString(),
      lastLogin: json['lastLogin']?.toString(),
      additionalInfo: additionalInfo,
    );
  }

  // 从原始JSON创建用户
  factory User.fromRawJson(String str) => User.fromJson(json.decode(str));

  // 转换为JSON映射
  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {
      'id': id,
      'username': username,
    };

    if (name != null) data['name'] = name;
    if (role != null) data['role'] = role;
    if (department != null) data['department'] = department;
    if (email != null) data['email'] = email;
    if (phone != null) data['phone'] = phone;
    if (avatar != null) data['avatar'] = avatar;
    if (lastLogin != null) data['lastLogin'] = lastLogin;

    // 添加额外信息
    data.addAll(additionalInfo);

    return data;
  }

  // 转换为JSON字符串
  String toRawJson() => json.encode(toJson());
}
