class WeldRecord {
  final String id;
  final String projectId;
  final String projectName;
  final String weldCode;
  final String operatorId;
  final String operatorName;
  final String deviceId;
  final DateTime timeStamp;
  final String faceImagePath;
  final String deviceImagePath;
  final String pipeImagePath;
  final Map<String, dynamic> parameters;
  final bool isUploaded;

  WeldRecord({
    required this.id,
    required this.projectId,
    required this.projectName,
    required this.weldCode,
    required this.operatorId,
    required this.operatorName,
    required this.deviceId,
    required this.timeStamp,
    required this.faceImagePath,
    required this.deviceImagePath,
    required this.pipeImagePath,
    required this.parameters,
    this.isUploaded = false,
  });

  // 从JSON创建实例
  factory WeldRecord.fromJson(Map<String, dynamic> json) {
    return WeldRecord(
      id: json['id'] as String,
      projectId: json['projectId'] as String,
      projectName: json['projectName'] as String,
      weldCode: json['weldCode'] as String,
      operatorId: json['operatorId'] as String,
      operatorName: json['operatorName'] as String,
      deviceId: json['deviceId'] as String,
      timeStamp: DateTime.parse(json['timeStamp'] as String),
      faceImagePath: json['faceImagePath'] as String,
      deviceImagePath: json['deviceImagePath'] as String,
      pipeImagePath: json['pipeImagePath'] as String,
      parameters: json['parameters'] as Map<String, dynamic>,
      isUploaded: json['isUploaded'] as bool,
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'projectId': projectId,
      'projectName': projectName,
      'weldCode': weldCode,
      'operatorId': operatorId,
      'operatorName': operatorName,
      'deviceId': deviceId,
      'timeStamp': timeStamp.toIso8601String(),
      'faceImagePath': faceImagePath,
      'deviceImagePath': deviceImagePath,
      'pipeImagePath': pipeImagePath,
      'parameters': parameters,
      'isUploaded': isUploaded,
    };
  }

  // 创建上传状态已更新的副本
  WeldRecord copyWithUploadStatus(bool isUploaded) {
    return WeldRecord(
      id: id,
      projectId: projectId,
      projectName: projectName,
      weldCode: weldCode,
      operatorId: operatorId,
      operatorName: operatorName,
      deviceId: deviceId,
      timeStamp: timeStamp,
      faceImagePath: faceImagePath,
      deviceImagePath: deviceImagePath,
      pipeImagePath: pipeImagePath,
      parameters: parameters,
      isUploaded: isUploaded,
    );
  }

  // 创建更新某些属性的副本
  WeldRecord copyWith({
    String? id,
    String? projectId,
    String? projectName,
    String? weldCode,
    String? operatorId,
    String? operatorName,
    String? deviceId,
    DateTime? timeStamp,
    String? faceImagePath,
    String? deviceImagePath,
    String? pipeImagePath,
    Map<String, dynamic>? parameters,
    bool? isUploaded,
  }) {
    return WeldRecord(
      id: id ?? this.id,
      projectId: projectId ?? this.projectId,
      projectName: projectName ?? this.projectName,
      weldCode: weldCode ?? this.weldCode,
      operatorId: operatorId ?? this.operatorId,
      operatorName: operatorName ?? this.operatorName,
      deviceId: deviceId ?? this.deviceId,
      timeStamp: timeStamp ?? this.timeStamp,
      faceImagePath: faceImagePath ?? this.faceImagePath,
      deviceImagePath: deviceImagePath ?? this.deviceImagePath,
      pipeImagePath: pipeImagePath ?? this.pipeImagePath,
      parameters: parameters ?? this.parameters,
      isUploaded: isUploaded ?? this.isUploaded,
    );
  }
}
