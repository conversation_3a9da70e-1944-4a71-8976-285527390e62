import 'dart:convert';

class WeldingJoint {
  final String id;
  final String code;
  final String projectId;
  final String pipeSegmentId;
  final String? status;
  final String? createdAt;
  final String? updatedAt;
  final String? description;
  final Map<String, dynamic> additionalInfo;

  WeldingJoint({
    required this.id,
    required this.code,
    required this.projectId,
    required this.pipeSegmentId,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.description,
    Map<String, dynamic>? additionalInfo,
  }) : this.additionalInfo = additionalInfo ?? {};

  // 从JSON映射创建焊口
  factory WeldingJoint.fromJson(Map<String, dynamic> json) {
    Map<String, dynamic> additionalInfo = Map.from(json);

    // 移除已经映射到具体字段的数据
    [
      'id',
      'code',
      'projectId',
      'pipeSegmentId',
      'status',
      'createdAt',
      'updatedAt',
      'description'
    ].forEach((field) => additionalInfo.remove(field));

    return WeldingJoint(
      id: json['id']?.toString() ?? '未知ID',
      code: json['code']?.toString() ?? '未知编号',
      projectId: json['projectId']?.toString() ?? '',
      pipeSegmentId: json['pipeSegmentId']?.toString() ?? '',
      status: json['status']?.toString(),
      createdAt: json['createdAt']?.toString(),
      updatedAt: json['updatedAt']?.toString(),
      description: json['description']?.toString(),
      additionalInfo: additionalInfo,
    );
  }

  // 从原始JSON创建焊口
  factory WeldingJoint.fromRawJson(String str) =>
      WeldingJoint.fromJson(json.decode(str));

  // 转换为JSON映射
  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {
      'id': id,
      'code': code,
      'projectId': projectId,
      'pipeSegmentId': pipeSegmentId,
    };

    if (status != null) data['status'] = status;
    if (createdAt != null) data['createdAt'] = createdAt;
    if (updatedAt != null) data['updatedAt'] = updatedAt;
    if (description != null) data['description'] = description;

    // 添加额外信息
    data.addAll(additionalInfo);

    return data;
  }

  // 转换为JSON字符串
  String toRawJson() => json.encode(toJson());
}
