// 离线模式数据模型
class OfflineDataModel {
  final String id;
  final DateTime createdAt;
  final String type; // 'face', 'device', 'pipe', 'welding'
  final Map<String, dynamic> data;
  final List<String> imagePaths; // 本地图片路径
  final bool isUploaded;
  final DateTime? uploadedAt;

  OfflineDataModel({
    required this.id,
    required this.createdAt,
    required this.type,
    required this.data,
    required this.imagePaths,
    this.isUploaded = false,
    this.uploadedAt,
  });

  factory OfflineDataModel.fromJson(Map<String, dynamic> json) {
    return OfflineDataModel(
      id: json['id'],
      createdAt: DateTime.parse(json['createdAt']),
      type: json['type'],
      data: Map<String, dynamic>.from(json['data']),
      imagePaths: List<String>.from(json['imagePaths'] ?? []),
      isUploaded: json['isUploaded'] ?? false,
      uploadedAt: json['uploadedAt'] != null
          ? DateTime.parse(json['uploadedAt'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'createdAt': createdAt.toIso8601String(),
      'type': type,
      'data': data,
      'imagePaths': imagePaths,
      'isUploaded': isUploaded,
      'uploadedAt': uploadedAt?.toIso8601String(),
    };
  }

  OfflineDataModel copyWith({
    String? id,
    DateTime? createdAt,
    String? type,
    Map<String, dynamic>? data,
    List<String>? imagePaths,
    bool? isUploaded,
    DateTime? uploadedAt,
  }) {
    return OfflineDataModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      type: type ?? this.type,
      data: data ?? this.data,
      imagePaths: imagePaths ?? this.imagePaths,
      isUploaded: isUploaded ?? this.isUploaded,
      uploadedAt: uploadedAt ?? this.uploadedAt,
    );
  }
}

// 离线模式状态
class OfflineModeState {
  final bool isOfflineMode;
  final int pendingUploads;
  final DateTime? lastSyncTime;
  final String? currentUserId;
  final String? currentProjectId;

  OfflineModeState({
    required this.isOfflineMode,
    required this.pendingUploads,
    this.lastSyncTime,
    this.currentUserId,
    this.currentProjectId,
  });

  factory OfflineModeState.fromJson(Map<String, dynamic> json) {
    return OfflineModeState(
      isOfflineMode: json['isOfflineMode'] ?? false,
      pendingUploads: json['pendingUploads'] ?? 0,
      lastSyncTime: json['lastSyncTime'] != null
          ? DateTime.parse(json['lastSyncTime'])
          : null,
      currentUserId: json['currentUserId'],
      currentProjectId: json['currentProjectId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isOfflineMode': isOfflineMode,
      'pendingUploads': pendingUploads,
      'lastSyncTime': lastSyncTime?.toIso8601String(),
      'currentUserId': currentUserId,
      'currentProjectId': currentProjectId,
    };
  }

  OfflineModeState copyWith({
    bool? isOfflineMode,
    int? pendingUploads,
    DateTime? lastSyncTime,
    String? currentUserId,
    String? currentProjectId,
  }) {
    return OfflineModeState(
      isOfflineMode: isOfflineMode ?? this.isOfflineMode,
      pendingUploads: pendingUploads ?? this.pendingUploads,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      currentUserId: currentUserId ?? this.currentUserId,
      currentProjectId: currentProjectId ?? this.currentProjectId,
    );
  }
}

// 焊接数据离线存储模型
class OfflineWeldingData {
  final String id;
  final String projectId;
  final String userId;
  final String deviceId;
  final Map<String, dynamic> weldingParams;
  final String weldingDataHex; // 160字节焊机数据
  final DateTime timestamp;
  final List<String> imagePaths;
  final bool isUploaded;

  OfflineWeldingData({
    required this.id,
    required this.projectId,
    required this.userId,
    required this.deviceId,
    required this.weldingParams,
    required this.weldingDataHex,
    required this.timestamp,
    required this.imagePaths,
    this.isUploaded = false,
  });

  factory OfflineWeldingData.fromJson(Map<String, dynamic> json) {
    return OfflineWeldingData(
      id: json['id'],
      projectId: json['projectId'],
      userId: json['userId'],
      deviceId: json['deviceId'],
      weldingParams: Map<String, dynamic>.from(json['weldingParams']),
      weldingDataHex: json['weldingDataHex'],
      timestamp: DateTime.parse(json['timestamp']),
      imagePaths: List<String>.from(json['imagePaths']),
      isUploaded: json['isUploaded'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'projectId': projectId,
      'userId': userId,
      'deviceId': deviceId,
      'weldingParams': weldingParams,
      'weldingDataHex': weldingDataHex,
      'timestamp': timestamp.toIso8601String(),
      'imagePaths': imagePaths,
      'isUploaded': isUploaded,
    };
  }
}
