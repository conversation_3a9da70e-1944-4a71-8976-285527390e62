import 'dart:convert';

/// 简化的焊接记录模型
/// 核心原则：以焊口编号为唯一标识，确保数据完整性和唯一性
class SimplifiedWeldingRecord {
  final String jointNumber;        // 焊口编号（唯一标识）- 格式：YYYYMMDDNNN
  final DateTime startTime;        // 焊接开始时间
  final DateTime? completionTime;  // 焊接完成时间
  final int status;                // 焊接状态 (0=未开始, 1=成功, -1=失败)
  final Map<String, dynamic> deviceInfo;      // 设备信息
  final Map<String, dynamic> weldingData;     // 焊接机器数据
  final Map<String, dynamic> parsedData;      // 解析后的焊接数据
  final String? locationData;      // GPS位置信息
  final Map<String, dynamic>? projectInfo;    // 项目信息（可选）
  final List<String> imagePaths;   // 图片路径列表
  final bool isUploaded;           // 是否已上传
  final DateTime? uploadTime;      // 上传时间

  SimplifiedWeldingRecord({
    required this.jointNumber,
    required this.startTime,
    this.completionTime,
    required this.status,
    required this.deviceInfo,
    required this.weldingData,
    required this.parsedData,
    this.locationData,
    this.projectInfo,
    required this.imagePaths,
    this.isUploaded = false,
    this.uploadTime,
  });

  /// 从JSON创建焊接记录
  factory SimplifiedWeldingRecord.fromJson(Map<String, dynamic> json) {
    return SimplifiedWeldingRecord(
      jointNumber: json['jointNumber'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      completionTime: json['completionTime'] != null 
          ? DateTime.parse(json['completionTime'] as String) 
          : null,
      status: json['status'] as int,
      deviceInfo: Map<String, dynamic>.from(json['deviceInfo'] ?? {}),
      weldingData: Map<String, dynamic>.from(json['weldingData'] ?? {}),
      parsedData: Map<String, dynamic>.from(json['parsedData'] ?? {}),
      locationData: json['locationData'] as String?,
      projectInfo: json['projectInfo'] != null 
          ? Map<String, dynamic>.from(json['projectInfo']) 
          : null,
      imagePaths: List<String>.from(json['imagePaths'] ?? []),
      isUploaded: json['isUploaded'] as bool? ?? false,
      uploadTime: json['uploadTime'] != null 
          ? DateTime.parse(json['uploadTime'] as String) 
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'jointNumber': jointNumber,
      'startTime': startTime.toIso8601String(),
      'completionTime': completionTime?.toIso8601String(),
      'status': status,
      'deviceInfo': deviceInfo,
      'weldingData': weldingData,
      'parsedData': parsedData,
      'locationData': locationData,
      'projectInfo': projectInfo,
      'imagePaths': imagePaths,
      'isUploaded': isUploaded,
      'uploadTime': uploadTime?.toIso8601String(),
    };
  }

  /// 从JSON字符串创建
  factory SimplifiedWeldingRecord.fromJsonString(String jsonString) {
    return SimplifiedWeldingRecord.fromJson(json.decode(jsonString));
  }

  /// 转换为JSON字符串
  String toJsonString() {
    return json.encode(toJson());
  }

  /// 获取状态描述
  String get statusDescription {
    switch (status) {
      case 0:
        return '未开始';
      case 1:
        return '焊接成功';
      case -1:
        return '焊接失败';
      default:
        return '未知状态';
    }
  }

  /// 获取焊接持续时间
  Duration? get duration {
    if (completionTime != null) {
      return completionTime!.difference(startTime);
    }
    return null;
  }

  /// 是否已完成
  bool get isCompleted => completionTime != null;

  /// 是否成功
  bool get isSuccessful => status == 1;

  /// 创建副本
  SimplifiedWeldingRecord copyWith({
    String? jointNumber,
    DateTime? startTime,
    DateTime? completionTime,
    int? status,
    Map<String, dynamic>? deviceInfo,
    Map<String, dynamic>? weldingData,
    Map<String, dynamic>? parsedData,
    String? locationData,
    Map<String, dynamic>? projectInfo,
    List<String>? imagePaths,
    bool? isUploaded,
    DateTime? uploadTime,
  }) {
    return SimplifiedWeldingRecord(
      jointNumber: jointNumber ?? this.jointNumber,
      startTime: startTime ?? this.startTime,
      completionTime: completionTime ?? this.completionTime,
      status: status ?? this.status,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      weldingData: weldingData ?? this.weldingData,
      parsedData: parsedData ?? this.parsedData,
      locationData: locationData ?? this.locationData,
      projectInfo: projectInfo ?? this.projectInfo,
      imagePaths: imagePaths ?? this.imagePaths,
      isUploaded: isUploaded ?? this.isUploaded,
      uploadTime: uploadTime ?? this.uploadTime,
    );
  }

  /// 标记为已上传
  SimplifiedWeldingRecord markAsUploaded() {
    return copyWith(
      isUploaded: true,
      uploadTime: DateTime.now(),
    );
  }

  /// 完成焊接
  SimplifiedWeldingRecord complete(int finalStatus) {
    return copyWith(
      completionTime: DateTime.now(),
      status: finalStatus,
    );
  }

  @override
  String toString() {
    return 'SimplifiedWeldingRecord(jointNumber: $jointNumber, status: $statusDescription, isUploaded: $isUploaded)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SimplifiedWeldingRecord && other.jointNumber == jointNumber;
  }

  @override
  int get hashCode => jointNumber.hashCode;
}

/// 焊接记录状态枚举
class WeldingStatus {
  static const int notStarted = 0;
  static const int success = 1;
  static const int failed = -1;
  static const int heatAbsorptionFailed = 2;
  static const int curlingFailed = 3;
  static const int coolingFailed = 4;

  static String getDescription(int status) {
    switch (status) {
      case notStarted:
        return '未开始';
      case success:
        return '焊接成功';
      case failed:
        return '焊接失败';
      case heatAbsorptionFailed:
        return '吸热失败';
      case curlingFailed:
        return '卷边失败';
      case coolingFailed:
        return '冷却失败';
      default:
        return '未知状态';
    }
  }
}
