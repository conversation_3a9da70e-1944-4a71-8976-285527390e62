class WeldRecord {
  final String id;
  final String jointId;
  final String projectName;
  final String projectId;
  final String processType;
  final String operatorName;
  final String deviceId;
  final DateTime createdAt;
  final bool hasImages;
  final int imageCount;
  final String? notes;

  WeldRecord({
    required this.id,
    required this.jointId,
    required this.projectName,
    required this.projectId,
    required this.processType,
    required this.operatorName,
    required this.deviceId,
    required this.createdAt,
    this.hasImages = false,
    this.imageCount = 0,
    this.notes,
  });

  factory WeldRecord.fromJson(Map<String, dynamic> json) {
    return WeldRecord(
      id: json['id'] as String,
      jointId: json['jointId'] as String,
      projectName: json['projectName'] as String,
      projectId: json['projectId'] as String,
      processType: json['processType'] as String,
      operatorName: json['operatorName'] as String,
      deviceId: json['deviceId'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      hasImages: json['hasImages'] as bool? ?? false,
      imageCount: json['imageCount'] as int? ?? 0,
      notes: json['notes'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'jointId': jointId,
      'projectName': projectName,
      'projectId': projectId,
      'processType': processType,
      'operatorName': operatorName,
      'deviceId': deviceId,
      'createdAt': createdAt.toIso8601String(),
      'hasImages': hasImages,
      'imageCount': imageCount,
      'notes': notes,
    };
  }

  @override
  String toString() {
    return 'WeldRecord{id: $id, jointId: $jointId, projectName: $projectName}';
  }
}
