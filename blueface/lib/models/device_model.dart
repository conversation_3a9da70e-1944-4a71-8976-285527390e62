import 'dart:convert';

class Device {
  final String id;
  final String name;
  final String serialNumber;
  final String model;
  final String? manufacturer;
  final String? status;
  final String? lastMaintenance;
  final String? nextMaintenance;
  final String? location;
  final String? projectId;
  final Map<String, dynamic> additionalInfo;

  Device({
    required this.id,
    required this.name,
    required this.serialNumber,
    required this.model,
    this.manufacturer,
    this.status,
    this.lastMaintenance,
    this.nextMaintenance,
    this.location,
    this.projectId,
    Map<String, dynamic>? additionalInfo,
  }) : this.additionalInfo = additionalInfo ?? {};

  // 从JSON映射创建设备
  factory Device.fromJson(Map<String, dynamic> json) {
    Map<String, dynamic> additionalInfo = Map.from(json);

    // 移除已经映射到具体字段的数据
    [
      'id',
      'name',
      'serialNumber',
      'model',
      'manufacturer',
      'status',
      'lastMaintenance',
      'nextMaintenance',
      'location',
      'projectId'
    ].forEach((field) => additionalInfo.remove(field));

    return Device(
      id: json['id']?.toString() ?? '未知ID',
      name: json['name']?.toString() ?? '未知设备',
      serialNumber: json['serialNumber']?.toString() ?? '未知序列号',
      model: json['model']?.toString() ?? '未知型号',
      manufacturer: json['manufacturer']?.toString(),
      status: json['status']?.toString(),
      lastMaintenance: json['lastMaintenance']?.toString(),
      nextMaintenance: json['nextMaintenance']?.toString(),
      location: json['location']?.toString(),
      projectId: json['projectId']?.toString(),
      additionalInfo: additionalInfo,
    );
  }

  // 从原始JSON创建设备
  factory Device.fromRawJson(String str) => Device.fromJson(json.decode(str));

  // 转换为JSON映射
  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {
      'id': id,
      'name': name,
      'serialNumber': serialNumber,
      'model': model,
    };

    if (manufacturer != null) data['manufacturer'] = manufacturer;
    if (status != null) data['status'] = status;
    if (lastMaintenance != null) data['lastMaintenance'] = lastMaintenance;
    if (nextMaintenance != null) data['nextMaintenance'] = nextMaintenance;
    if (location != null) data['location'] = location;
    if (projectId != null) data['projectId'] = projectId;

    // 添加额外信息
    data.addAll(additionalInfo);

    return data;
  }

  // 转换为JSON字符串
  String toRawJson() => json.encode(toJson());
}
