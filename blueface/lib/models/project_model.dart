import 'dart:convert';

class Project {
  final String id;
  final String name;
  final String code;
  final String address;
  final String constructionUnit;
  final String status;
  final String? startDate;
  final String? endDate;
  final String? description;
  final Map<String, dynamic> additionalInfo;

  Project({
    required this.id,
    required this.name,
    required this.code,
    required this.address,
    required this.constructionUnit,
    required this.status,
    this.startDate,
    this.endDate,
    this.description,
    Map<String, dynamic>? additionalInfo,
  }) : this.additionalInfo = additionalInfo ?? {};

  // 从JSON映射创建项目
  factory Project.fromJson(Map<String, dynamic> json) {
    Map<String, dynamic> additionalInfo = Map.from(json);

    // 移除已经映射到具体字段的数据
    [
      'id',
      'sequenceNbr', // 项目ID字段
      'name',
      'code',
      'address',
      'constructionUnit',
      'status',
      'startDate',
      'endDate',
      'description'
    ].forEach((field) => additionalInfo.remove(field));

    return Project(
      // 优先使用sequenceNbr作为项目ID，如果没有则使用id字段
      id: json['sequenceNbr']?.toString() ?? json['id']?.toString() ?? '未知ID',
      name: json['name']?.toString() ?? '未命名项目',
      code: json['code']?.toString() ?? '未知编号',
      address: json['address']?.toString() ?? '未知地址',
      constructionUnit: json['constructionUnit']?.toString() ?? '未知单位',
      status: json['status']?.toString() ?? '未知状态',
      startDate: json['startDate']?.toString(),
      endDate: json['endDate']?.toString(),
      description: json['description']?.toString(),
      additionalInfo: additionalInfo,
    );
  }

  // 从原始JSON创建项目
  factory Project.fromRawJson(String str) => Project.fromJson(json.decode(str));

  // 转换为JSON映射
  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {
      'id': id,
      'name': name,
      'code': code,
      'address': address,
      'constructionUnit': constructionUnit,
      'status': status,
    };

    if (startDate != null) data['startDate'] = startDate;
    if (endDate != null) data['endDate'] = endDate;
    if (description != null) data['description'] = description;

    // 添加额外信息
    data.addAll(additionalInfo);

    return data;
  }

  // 转换为JSON字符串
  String toRawJson() => json.encode(toJson());
}
