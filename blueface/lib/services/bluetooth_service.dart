import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart' as fbp;
import 'package:gbk_codec/gbk_codec.dart'; // 已有的GBK编码支持
import 'user_service.dart'; // 🎯 添加用户服务导入

class BleService {
  static final BleService _instance = BleService._internal();
  // 不需要创建GBK对象，直接使用gbk全局变量

  factory BleService() => _instance;

  BleService._internal();

  // 蓝牙状态
  bool isScanning = false;
  List<fbp.BluetoothDevice> devices = [];
  fbp.BluetoothDevice? connectedDevice;
  fbp.BluetoothCharacteristic? writeCharacteristic;
  final StreamController<List<fbp.BluetoothDevice>> _devicesController =
      StreamController<List<fbp.BluetoothDevice>>.broadcast();
  final StreamController<fbp.BluetoothDevice?> _connectedDeviceController =
      StreamController<fbp.BluetoothDevice?>.broadcast();
  final StreamController<String> _receivedDataController =
      StreamController<String>.broadcast();
  final StreamController<String> _logController =
      StreamController<String>.broadcast();

  // 获取设备列表流
  Stream<List<fbp.BluetoothDevice>> get devicesStream =>
      _devicesController.stream;

  // 获取连接设备流
  Stream<fbp.BluetoothDevice?> get connectedDeviceStream =>
      _connectedDeviceController.stream;

  // 获取接收数据流
  Stream<String> get receivedDataStream => _receivedDataController.stream;

  // 获取日志流
  Stream<String> get logStream => _logController.stream;

  // 缓存特征响应的信息
  String _notificationType = "";
  String _specialContent = "";

  // 添加设备信息缓存
  Map<String, String> _deviceInfoCache = {
    'machineNumber': '',
    'weldingStandard': '',
    'machineType': '',
    'cylinderArea': '',
    'connectionStatus': '',
  };

  // 添加160字节数据分片重组相关变量
  List<int> _weldingDataBuffer = [];
  bool _isReceivingWeldingData = false;
  int _expectedWeldingDataLength = 163; // 01 03 A0 + 160字节数据 + 2字节CRC
  Timer? _weldingDataTimeout;

  // 获取特征通知类型
  String get notificationType => _notificationType;

  // 获取特征内容
  String get specialContent => _specialContent;

  // 添加日志
  void addLog(String message) {
    print('蓝牙: $message');
    _logController.add(message);
  }

  // 添加详细日志，包含原始数据和CRC信息
  void addDetailedLog(String prefix, List<int> data, {String? crcInfo}) {
    String hexString = data
        .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
        .join(' ');

    // 尝试将数据转换为UTF-8文本
    String? textString;
    try {
      textString = utf8.decode(data);
    } catch (_) {
      textString = null;
    }

    String logMessage = '$prefix: $hexString';

    // 添加文本表示（如果可解码）
    if (textString != null && textString.isNotEmpty) {
      logMessage += ' (文本: $textString)';
    }

    // 添加原始数据
    logMessage += ' | RAW: [${data.join(', ')}]';

    // 添加CRC信息（如果有）
    if (crcInfo != null && crcInfo.isNotEmpty) {
      logMessage += ' | CRC: $crcInfo';
    }

    addLog(logMessage);
  }

  // 开始扫描设备
  Future<void> startScan() async {
    if (isScanning) return;

    try {
      isScanning = true;
      // 清空设备列表
      devices.clear();
      _devicesController.add(devices); // 触发UI更新

      addLog('开始扫描蓝牙设备...');

      // 检查蓝牙是否打开
      if (await fbp.FlutterBluePlus.isAvailable == false) {
        addLog('错误: 蓝牙不可用');
        isScanning = false;
        _devicesController.add(devices);
        return;
      }

      // 开始扫描
      await fbp.FlutterBluePlus.startScan(timeout: Duration(seconds: 5));

      // 监听扫描结果
      fbp.FlutterBluePlus.scanResults.listen((results) {
        for (fbp.ScanResult result in results) {
          if (result.device.name.isNotEmpty &&
              !devices.contains(result.device)) {
            devices.add(result.device);
            addLog('发现设备: ${result.device.name} [${result.device.id}]');
            _devicesController.add(List.from(devices)); // 更新UI，使用List.from创建新列表
          }
        }
      });

      // 扫描完成后
      Future.delayed(Duration(seconds: 5), () {
        stopScan();
      });
    } catch (e) {
      addLog('扫描错误: $e');
      isScanning = false;
      _devicesController.add(devices);
    }
  }

  // 停止扫描
  void stopScan() {
    if (!isScanning) return;

    fbp.FlutterBluePlus.stopScan();
    isScanning = false;
    addLog('扫描完成，发现 ${devices.length} 个设备');
    _devicesController.add(List.from(devices)); // 使用List.from创建新列表
  }

  // 连接到设备
  Future<bool> connectToDevice(fbp.BluetoothDevice device) async {
    try {
      addLog('正在连接到设备: ${device.name}...');

      // 先断开已连接的设备
      await disconnectDevice();

      // 连接新设备前先更新状态
      connectedDevice = device;
      _connectedDeviceController.add(device);

      // 连接新设备
      await device.connect(timeout: Duration(seconds: 15));
      addLog('已连接到设备: ${device.name}');

      // 发现服务
      addLog('正在发现服务...');
      List<fbp.BluetoothService> services = await device.discoverServices();
      addLog('发现 ${services.length} 个服务');

      // 修改: 使用临时变量优先选择fff2
      fbp.BluetoothCharacteristic? tempFff2;
      fbp.BluetoothCharacteristic? tempFff3;

      for (fbp.BluetoothService service in services) {
        addLog('检查服务: ${service.uuid}');
        for (fbp.BluetoothCharacteristic characteristic
            in service.characteristics) {
          String uuid = characteristic.uuid.toString().split('-')[0];
          addLog(
              '发现特征: $uuid, 属性: ${_getCharacteristicProperties(characteristic)}');

          // 设置所有可通知的特征来接收数据
          if (characteristic.properties.notify ||
              characteristic.properties.indicate) {
            try {
              addLog('尝试启用特征通知: $uuid');
              await characteristic.setNotifyValue(true);
              addLog('已启用特征通知: $uuid');

              characteristic.value.listen((value) {
                if (value.isNotEmpty) {
                  addLog('从特征 $uuid 接收到原始数据: [${value.join(',')}]');
                  _handleReceivedData(value);
                }
              });
            } catch (e) {
              addLog('设置通知失败: $uuid, 错误: $e');
            }
          }

          // 记录可写特征，优先选择fff2
          if (characteristic.properties.write ||
              characteristic.properties.writeWithoutResponse) {
            if (uuid == "fff2") {
              tempFff2 = characteristic;
              addLog('找到写特征: fff2');
            } else if (uuid == "fff3") {
              tempFff3 = characteristic;
              addLog('找到写特征: fff3');
            }
          }
        }
      }

      // 优先选择fff2作为写特征
      if (tempFff2 != null) {
        writeCharacteristic = tempFff2;
        addLog('>>> 最终选用写特征: fff2 <<<');
      } else if (tempFff3 != null) {
        writeCharacteristic = tempFff3;
        addLog('>>> 未找到fff2，使用fff3作为写特征 <<<');
      } else {
        addLog('警告: 未找到可写特征');
        // 即使找不到写特征也保持连接状态
      }

      // 确保再次触发状态流，以便UI更新
      _connectedDeviceController.add(device);

      // 连接成功后，自动发送连接成功指示位命令
      if (isConnected) {
        // 延迟更长时间后发送命令
        await Future.delayed(Duration(milliseconds: 800));

        // 发送连接成功指示位命令: 01 06 00 03 00 01 B8 0A
        bool connectionStatusSent = await sendConnectionStatusCommand();
        if (connectionStatusSent) {
          addLog('连接指示位命令发送成功');

          // 延迟后读取所有设备信息，增加等待时间
          await Future.delayed(Duration(milliseconds: 600));

          // 读取焊机编号
          bool machineNumberSent = await sendQueryMachineNumberCommand();
          addLog(machineNumberSent ? '焊机编号查询命令发送成功' : '焊机编号查询命令发送失败');

          // 延迟后读取焊接标准，增加等待时间
          await Future.delayed(Duration(milliseconds: 800));
          bool standardSent = await sendQueryWeldingStandardCommand();
          addLog(standardSent ? '焊接标准查询命令发送成功' : '焊接标准查询命令发送失败');

          // 延迟后读取焊机机型，增加等待时间
          await Future.delayed(Duration(milliseconds: 800));
          bool machineTypeSent = await sendQueryMachineTypeCommand();
          addLog(machineTypeSent ? '焊机机型查询命令发送成功' : '焊机机型查询命令发送失败');

          // 延迟后读取油缸面积
          await Future.delayed(Duration(milliseconds: 800));
          bool cylinderAreaSent = await sendQueryCylinderAreaCommand();
          addLog(cylinderAreaSent ? '油缸面积查询命令发送成功' : '油缸面积查询命令发送失败');

          addLog('所有设备信息查询命令已发送');

          // 添加一个额外的延迟和日志，以便于观察所有响应
          await Future.delayed(Duration(seconds: 1));
          addLog('设备信息查询完成，等待蓝牙响应...');
        } else {
          addLog('连接指示位命令发送失败');
        }
      }

      return isConnected;
    } catch (e) {
      addLog('连接设备错误: $e');
      connectedDevice = null;
      writeCharacteristic = null;
      _connectedDeviceController.add(null);
      return false;
    }
  }

  // 断开设备连接
  Future<void> disconnectDevice() async {
    if (connectedDevice == null) return;

    try {
      addLog('正在断开设备连接: ${connectedDevice!.name}...');
      await connectedDevice!.disconnect();
      addLog('设备已断开连接');
    } catch (e) {
      addLog('断开连接错误: $e');
    } finally {
      connectedDevice = null;
      writeCharacteristic = null;
      _connectedDeviceController.add(null);
    }
  }

  // 用于记录最近发送的命令类型，帮助解析响应
  String recentCommandType = "";

  Future<bool> sendData(List<int> data, {bool withResponse = true}) async {
    try {
      if (connectedDevice == null || writeCharacteristic == null) {
        addLog('未连接到设备，无法发送数据');
        return false;
      }

      // 记录命令类型，用于响应解析
      String dataHex = bytesToHexString(data);
      if (dataHex.contains("00 7B")) {
        recentCommandType = "机型";
      } else if (dataHex.contains("00 7C")) {
        recentCommandType = "油缸";
      } else if (dataHex.contains("00 73")) {
        recentCommandType = "焊接标准";
      } else if (dataHex.contains("00 05")) {
        recentCommandType = "焊机编号";
      } else {
        recentCommandType = "";
      }

      // 有响应模式
      if (withResponse) {
        addLog('发送数据(有响应): ${bytesToHexString(data)}');
        try {
          await writeCharacteristic!.write(data, withoutResponse: false);
          addLog('数据发送成功(有响应模式)');
          return true;
        } catch (e) {
          addLog('发送数据失败(有响应模式): $e');
          return false;
        }
      }
      // 无响应模式
      else {
        addLog('发送数据(无响应): ${bytesToHexString(data)}');
        try {
          await writeCharacteristic!.write(data, withoutResponse: true);
          addLog('数据发送成功(无响应模式)');
          return true;
        } catch (e) {
          addLog('发送数据失败(无响应模式): $e');
          return false;
        }
      }
    } catch (e) {
      addLog('发送数据错误: $e');
      return false;
    }
  }

  // 处理接收到的数据
  void _handleReceivedData(List<int> data) {
    try {
      // 确保数据不为空
      if (data.isEmpty) {
        addLog('接收到空数据');
        return;
      }

      // 检测固定响应 [136,0,4,0,1,16,17] - 这是设备不支持查询时的标准响应
      if (data.length == 7 &&
          data[0] == 136 &&
          data[1] == 0 &&
          data[2] == 4 &&
          data[3] == 0 &&
          data[4] == 1 &&
          data[5] == 16 &&
          data[6] == 17) {
        addLog('ℹ️ 检测到设备不支持响应: [136,0,4,0,1,16,17] - 此查询类型设备不支持');
        return; // 直接返回，不进行进一步处理
      }

      // 160字节数据分片重组处理
      if (_handle160ByteDataFragments(data)) {
        return; // 如果是160字节数据片段，已经处理，直接返回
      }

      // 记录原始接收数据，使用16进制格式
      String hexRawData = data
          .map((e) => '0x${e.toRadixString(16).padLeft(2, '0').toUpperCase()}')
          .join(', ');
      addLog('接收到数据细节: [${hexRawData}], 长度: ${data.length}');

      // 转换为十六进制字符串显示（方便阅读）
      String hexString = bytesToHexString(data);
      String logMessage = '接收: $hexString';

      // 尝试文本解码并记录
      String textString = '';
      bool isModbusResponse = false;
      String notificationType = "";
      String specialContent = "";

      // 检查是否是ASCII字符构成的数据
      bool isAsciiData = true;
      for (int byte in data) {
        if (byte > 127 || (byte < 32 && byte != 10 && byte != 13)) {
          isAsciiData = false;
          break;
        }
      }

      // 如果是ASCII数据，尝试将其转换为文本
      if (isAsciiData) {
        try {
          textString = String.fromCharCodes(data);
          addLog('数据为ASCII文本: $textString');
        } catch (e) {
          addLog('ASCII解码失败: $e');
        }
      }

      // 处理特殊情况：ASCII文本形式的十六进制数据
      if (textString.isNotEmpty && textString.startsWith("0103")) {
        // 处理焊机编号(0A)
        if (textString.length >= 8 && textString.substring(4, 6) == "0A") {
          // 这是ASCII文本形式的焊机编号响应，提取十六进制数据并解析
          addLog('检测到ASCII文本形式的焊机编号数据: $textString');

          try {
            // 提取焊机编号部分，前6个字符是"01030A"，后面是焊机编号部分
            String hexPart = textString.substring(6);

            // 确保只取前10个字符(5个字节)
            if (hexPart.length > 10) {
              hexPart = hexPart.substring(0, 10);
            }

            addLog('提取的焊机编号十六进制部分: $hexPart (长度: ${hexPart.length})');

            // 转换为字节数组 - 每两个字符一个字节
            List<int> bytes = [];
            for (int i = 0; i < hexPart.length; i += 2) {
              if (i + 2 <= hexPart.length) {
                int byte = int.parse(hexPart.substring(i, i + 2), radix: 16);
                bytes.add(byte);
              }
            }

            addLog(
                '转换后的字节数组: [${bytesToHexString(bytes)}] (长度: ${bytes.length})');

            // 直接解析bytes，不做任何校验
            String machineNumber = "";

            // 使用多种方法尝试解码，按优先级顺序
            // 方法1：使用gbk_codec库尝试解码 - 最通用的方法
            try {
              String gbkText = gbk.decode(bytes);
              gbkText = gbkText.trim();

              if (gbkText.isNotEmpty && !_containsUnprintableChars(gbkText)) {
                addLog('GBK解码结果: "$gbkText"');
                machineNumber = gbkText;
              } else {
                addLog('GBK解码包含不可打印字符，可能是乱码: "$gbkText"');
              }
            } catch (e) {
              addLog('GBK解码异常: $e');
            }

            // 方法2：如果GBK解码失败或出现乱码，尝试ASCII解码（适用于英文/数字）
            if (machineNumber.isEmpty) {
              try {
                String asciiText = '';
                for (int byte in bytes) {
                  if (byte > 0 && byte < 127 && byte != 0) {
                    asciiText += String.fromCharCode(byte);
                  }
                }
                asciiText = asciiText.trim();
                if (asciiText.isNotEmpty) {
                  addLog('ASCII解码结果: "$asciiText"');
                  machineNumber = asciiText;
                }
              } catch (e) {
                addLog('ASCII解码异常: $e');
              }
            }

            // 方法3：如果前两种方法都失败，使用十六进制表示
            if (machineNumber.isEmpty) {
              machineNumber = "HEX:" + hexPart;
              addLog('使用十六进制表示: "$machineNumber"');
            }

            addLog('========================');
            addLog('=== 接收到焊机编号(ASCII文本形式) ===');
            addLog('解析结果: "$machineNumber"');
            addLog('========================');

            notificationType = "焊机编号";
            specialContent = machineNumber;

            // 添加更明显的日志记录
            addLog('【重要】设备焊机编号(ASCII文本): $machineNumber');

            // 在接收到焊机编号后显示明显的通知
            _receivedDataController.add('【重要】设备焊机编号(ASCII文本): $machineNumber');
            // 同时也发送标准格式的通知，确保兼容性
            _receivedDataController.add('【重要】焊机编号: $machineNumber');
            _receivedDataController.add('焊机编号:$machineNumber');

            // 更新设备信息缓存
            _deviceInfoCache['machineNumber'] = machineNumber;
            addLog('✅ 已更新焊机编号到缓存: $machineNumber');

            // 特殊情况已处理，直接返回
            return;
          } catch (e) {
            addLog('解析ASCII文本形式焊机编号失败: $e');
          }
        }
        // 处理焊接标准(0E)
        else if (textString.length >= 8 && textString.substring(4, 6) == "0E") {
          // 这是ASCII文本形式的焊接标准响应
          addLog('检测到ASCII文本形式的焊接标准数据: $textString');

          try {
            // 提取焊接标准部分，前6个字符是"01030E"，后面是焊接标准部分
            String hexPart = textString.substring(6);

            // 记录原始提取的十六进制部分
            addLog('原始提取的焊接标准十六进制部分: $hexPart (长度: ${hexPart.length})');

            // 转换为字节数组 - 每两个字符一个字节
            List<int> bytes = [];
            for (int i = 0; i < hexPart.length; i += 2) {
              if (i + 2 <= hexPart.length) {
                try {
                  int byte = int.parse(hexPart.substring(i, i + 2), radix: 16);
                  bytes.add(byte);
                } catch (e) {
                  addLog('解析十六进制字符异常: ${hexPart.substring(i, i + 2)} - $e');
                }
              }
            }

            addLog(
                '转换后的字节数组: [${bytesToHexString(bytes)}] (长度: ${bytes.length})');

            // 解析焊接标准内容
            String standardText = "";

            // 尝试使用GBK解码
            try {
              String gbkText = gbk.decode(bytes);
              gbkText = gbkText.trim();

              if (gbkText.isNotEmpty && !_containsUnprintableChars(gbkText)) {
                addLog('GBK解码结果: "$gbkText"');
                standardText = gbkText;
              } else {
                addLog('GBK解码可能包含不可打印字符');
              }
            } catch (e) {
              addLog('GBK解码异常: $e');
            }

            // 如果GBK解码失败，尝试ASCII解码
            if (standardText.isEmpty) {
              try {
                String asciiText = '';
                for (int byte in bytes) {
                  if (byte > 0 && byte < 127 && byte != 0) {
                    asciiText += String.fromCharCode(byte);
                  }
                }
                asciiText = asciiText.trim();
                if (asciiText.isNotEmpty) {
                  addLog('ASCII解码结果: "$asciiText"');
                  standardText = asciiText;
                }
              } catch (e) {
                addLog('ASCII解码异常: $e');
              }
            }

            // 如果文本解码都失败，使用十六进制表示
            if (standardText.isEmpty) {
              standardText =
                  "HEX:" + bytesToHexString(bytes, withSpaces: false);
              addLog('使用十六进制表示: "$standardText"');
            }

            addLog('========================');
            addLog('=== 接收到焊接标准(ASCII文本形式) ===');
            addLog('解析结果: "$standardText"');
            addLog('========================');

            notificationType = "焊接标准";
            specialContent = standardText;

            // 添加更明显的日志记录
            addLog('【重要】焊接标准(ASCII文本): $standardText');

            // 发送多种格式的通知，确保UI能够捕获
            _receivedDataController.add('【重要】焊接标准(ASCII文本): $standardText');
            _receivedDataController.add('【重要】焊接标准: $standardText');
            _receivedDataController.add('焊接标准:$standardText');

            // 更新设备信息缓存
            _deviceInfoCache['weldingStandard'] = standardText;
            addLog('✅ 已更新焊接标准到缓存: $standardText');

            // 特殊情况已处理，直接返回
            return;
          } catch (e) {
            addLog('解析ASCII文本形式焊接标准失败: $e');
          }
        }
        // 处理焊机机型(02) - 新增部分
        else if (textString.length >= 8 && textString.substring(4, 6) == "02") {
          // 这是ASCII文本形式的焊机机型响应
          addLog('检测到ASCII文本形式的焊机机型/油缸面积数据: $textString');

          try {
            // 提取焊机机型部分，前6个字符是"010302"，后面是焊机机型部分
            String hexPart = textString.substring(6);

            // 记录原始提取的十六进制部分
            addLog('原始提取的十六进制部分: $hexPart (长度: ${hexPart.length})');

            // 转换为字节数组 - 每两个字符一个字节
            List<int> bytes = [];
            for (int i = 0; i < hexPart.length; i += 2) {
              if (i + 2 <= hexPart.length) {
                try {
                  int byte = int.parse(hexPart.substring(i, i + 2), radix: 16);
                  bytes.add(byte);
                } catch (e) {
                  addLog('解析十六进制字符异常: ${hexPart.substring(i, i + 2)} - $e');
                }
              }
            }

            addLog(
                '转换后的字节数组: [${bytesToHexString(bytes)}] (长度: ${bytes.length})');

            // 解析内容
            String machineType = "";

            // 方法1：尝试直接转换为数值（常见的机型编码）
            if (bytes.length == 2) {
              // 如果是2字节，直接显示数值
              int typeValue = (bytes[0] << 8) | bytes[1];
              machineType = "$typeValue";
              addLog('焊机机型数值解析: $typeValue');
            } else {
              // 尝试文本解码
              try {
                String gbkText = gbk.decode(bytes);
                gbkText = gbkText.trim();
                if (gbkText.isNotEmpty && !_containsUnprintableChars(gbkText)) {
                  machineType = gbkText;
                  addLog('焊机机型GBK解码: "$gbkText"');
                }
              } catch (e) {
                addLog('焊机机型GBK解码失败: $e');
              }

              // 如果GBK解码失败，尝试ASCII
              if (machineType.isEmpty) {
                try {
                  String asciiText = '';
                  for (int byte in bytes) {
                    if (byte > 0 && byte < 127 && byte != 0) {
                      asciiText += String.fromCharCode(byte);
                    }
                  }
                  asciiText = asciiText.trim();
                  if (asciiText.isNotEmpty) {
                    machineType = asciiText;
                    addLog('焊机机型ASCII解码: "$asciiText"');
                  }
                } catch (e) {
                  addLog('焊机机型ASCII解码失败: $e');
                }
              }

              // 如果都失败，使用十六进制
              if (machineType.isEmpty) {
                machineType = bytesToHexString(bytes, withSpaces: false);
                addLog('焊机机型十六进制表示: "$machineType"');
              }
            }

            addLog('========================');
            addLog('=== 接收到焊机机型 ===');
            addLog('解析结果: "$machineType"');
            addLog('========================');

            notificationType = "焊机机型";
            specialContent = machineType;

            addLog('【重要】焊机机型: $machineType');
            _receivedDataController.add('【重要】焊机机型: $machineType');
            _receivedDataController.add('焊机机型:$machineType');

            // 更新设备信息缓存
            _deviceInfoCache['machineType'] = machineType;
            addLog('✅ 已更新焊机机型到缓存: $machineType');
          } catch (e) {
            addLog('解析ASCII文本形式焊机机型/油缸面积失败: $e');
          }
        }
      }

      // 处理Modbus二进制响应
      if (data.length >= 3) {
        // Modbus响应通常是从设备地址开始，后面是功能码
        if (data[0] == 0x01) {
          // 设备地址为1
          // 功能码 3: 读保持寄存器, 6: 写单个寄存器, 16(0x10): 写多个寄存器 等
          if (data[1] == 0x03 || data[1] == 0x06 || data[1] == 0x10) {
            isModbusResponse = true;
            logMessage += ' [Modbus响应]';

            // 特别记录整个Modbus响应
            addLog(
                '接收到Modbus响应: 功能码 0x${data[1].toRadixString(16).padLeft(2, '0').toUpperCase()}, 完整数据: ${hexString}');

            // 检查是否是焊机编号响应 (01 03 0A XX...)
            if (data[1] == 0x03 && data.length >= 8 && data[2] == 0x0A) {
              // 用改进后的方法解析焊机编号，跳过CRC校验
              String machineNumber =
                  parseWeldingMachineNumber(data, skipCrcCheck: true);
              addLog('========================');
              addLog('=== 接收到焊机编号 ===');
              addLog('解析结果: "$machineNumber"');
              addLog('========================');

              // 确保UI和其他组件能够获取到焊机编号
              notificationType = "焊机编号";
              specialContent = machineNumber;

              // 添加更明显的日志记录
              addLog('【重要】设备焊机编号: $machineNumber');

              // 在接收到焊机编号后显示明显的通知
              _receivedDataController.add('【重要】焊机编号: $machineNumber');

              // 更新设备信息缓存 - 这是关键的缓存更新语句
              _deviceInfoCache['machineNumber'] = machineNumber;
              addLog('✅ 已更新焊机编号到缓存: $machineNumber');
            }

            // 检查是否是连接成功响应 (01 06 00 03 00 01 B8 0A)
            else if (data[1] == 0x06 &&
                data.length >= 8 &&
                data[2] == 0x00 &&
                data[3] == 0x03 &&
                data[4] == 0x00 &&
                data[5] == 0x01) {
              addLog('========================');
              addLog('=== 接收到连接成功响应 ===');
              addLog('响应数据: ${hexString}');
              addLog('========================');

              // 设置特殊通知
              notificationType = "连接状态";
              specialContent = "连接成功确认";

              // 通知UI
              addLog('【重要】连接状态: 连接成功确认');
              _receivedDataController.add('【重要】连接状态: 连接成功确认');

              // 更新设备信息缓存
              _deviceInfoCache['connectionStatus'] = '连接成功确认';
              addLog('✅ 已更新连接状态到缓存: 连接成功确认');

              // 🎯 连接成功后自动发送用户ID
              _sendUserIdAfterConnection();
            }

            // 检查是否是焊接标准响应 (01 03 0E XX...) 或者检查功能码和命令类型
            else if ((data[1] == 0x03 && data.length >= 8 && data[2] == 0x0E) ||
                (data[1] == 0x03 && recentCommandType.contains("焊接标准"))) {
              // 提取数据长度（第3个字节）
              int dataLength = data[2];
              if (data.length >= 3 + dataLength + 2) {
                // 确保有足够的数据和CRC
                // 提取数据部分（跳过头三个字节和最后两个CRC字节）
                List<int> dataBytes = data.sublist(3, 3 + dataLength);

                addLog('焊接标准数据长度: $dataLength 字节');
                addLog('焊接标准原始字节: [${bytesToHexString(dataBytes)}]');

                // 尝试解析焊接标准内容
                String weldingStandard = "";

                // 方法1：尝试使用GBK解码（如果是文本内容）
                try {
                  String gbkText = gbk.decode(dataBytes);
                  gbkText = gbkText.trim();
                  if (gbkText.isNotEmpty &&
                      !_containsUnprintableChars(gbkText)) {
                    weldingStandard = gbkText;
                    addLog('焊接标准GBK解码: "$gbkText"');
                  }
                } catch (e) {
                  addLog('焊接标准GBK解码失败: $e');
                }

                // 方法2：如果GBK解码失败，尝试ASCII解码
                if (weldingStandard.isEmpty) {
                  try {
                    String asciiText = '';
                    for (int byte in dataBytes) {
                      if (byte > 0 && byte < 127 && byte != 0) {
                        asciiText += String.fromCharCode(byte);
                      }
                    }
                    asciiText = asciiText.trim();
                    if (asciiText.isNotEmpty) {
                      weldingStandard = asciiText;
                      addLog('焊接标准ASCII解码: "$asciiText"');
                    }
                  } catch (e) {
                    addLog('焊接标准ASCII解码失败: $e');
                  }
                }

                // 方法3：如果文本解码都失败，使用十六进制表示
                if (weldingStandard.isEmpty) {
                  weldingStandard =
                      "HEX:" + bytesToHexString(dataBytes, withSpaces: false);
                  addLog('焊接标准十六进制表示: "$weldingStandard"');
                }

                addLog('========================');
                addLog('=== 接收到焊接标准 ===');
                addLog('解析结果: "$weldingStandard"');
                addLog('========================');

                // 通知UI
                notificationType = "焊接标准";
                specialContent = weldingStandard;

                addLog('【重要】焊接标准: $weldingStandard');
                _receivedDataController.add('【重要】焊接标准: $weldingStandard');
                _receivedDataController.add('焊接标准:$weldingStandard');

                // 更新设备信息缓存
                _deviceInfoCache['weldingStandard'] = weldingStandard;
                addLog('✅ 已更新焊接标准到缓存: $weldingStandard');
              } else {
                addLog('焊接标准响应数据长度不足，忽略处理');
              }
            }

            // 检查是否是焊机机型响应 (01 03 02 XX...)
            else if ((data[1] == 0x03 &&
                    data.length >= 6 &&
                    data[2] == 0x02 &&
                    recentCommandType.contains("机型")) ||
                (data[1] == 0x03 && recentCommandType.contains("机型"))) {
              // 提取数据长度（第3个字节）
              int dataLength = data[2];
              if (data.length >= 3 + dataLength + 2) {
                // 确保有足够的数据和CRC
                // 提取数据部分（跳过头三个字节和最后两个CRC字节）
                List<int> dataBytes = data.sublist(3, 3 + dataLength);

                addLog('焊机机型数据长度: $dataLength 字节');
                addLog('焊机机型原始字节: [${bytesToHexString(dataBytes)}]');

                // 解析焊机机型内容
                String machineType = "";

                // 方法1：尝试直接转换为数值（常见的机型编码）
                if (dataBytes.length == 2) {
                  // 如果是2字节，直接显示数值
                  int typeValue = (dataBytes[0] << 8) | dataBytes[1];
                  machineType = "$typeValue";
                  addLog('焊机机型数值解析: $typeValue');
                } else {
                  // 尝试文本解码
                  try {
                    String gbkText = gbk.decode(dataBytes);
                    gbkText = gbkText.trim();
                    if (gbkText.isNotEmpty &&
                        !_containsUnprintableChars(gbkText)) {
                      machineType = gbkText;
                      addLog('焊机机型GBK解码: "$gbkText"');
                    }
                  } catch (e) {
                    addLog('焊机机型GBK解码失败: $e');
                  }

                  // 如果GBK解码失败，尝试ASCII
                  if (machineType.isEmpty) {
                    try {
                      String asciiText = '';
                      for (int byte in dataBytes) {
                        if (byte > 0 && byte < 127 && byte != 0) {
                          asciiText += String.fromCharCode(byte);
                        }
                      }
                      asciiText = asciiText.trim();
                      if (asciiText.isNotEmpty) {
                        machineType = asciiText;
                        addLog('焊机机型ASCII解码: "$asciiText"');
                      }
                    } catch (e) {
                      addLog('焊机机型ASCII解码失败: $e');
                    }
                  }

                  // 如果都失败，使用十六进制
                  if (machineType.isEmpty) {
                    machineType =
                        bytesToHexString(dataBytes, withSpaces: false);
                    addLog('焊机机型十六进制表示: "$machineType"');
                  }
                }

                addLog('========================');
                addLog('=== 接收到焊机机型 ===');
                addLog('解析结果: "$machineType"');
                addLog('========================');

                notificationType = "焊机机型";
                specialContent = machineType;

                addLog('【重要】焊机机型: $machineType');
                _receivedDataController.add('【重要】焊机机型: $machineType');
                _receivedDataController.add('焊机机型:$machineType');

                // 更新设备信息缓存
                _deviceInfoCache['machineType'] = machineType;
                addLog('✅ 已更新焊机机型到缓存: $machineType');
              } else {
                addLog('焊机机型响应数据长度不足，忽略处理');
              }
            }

            // 检查是否是油缸面积响应
            else if ((data[1] == 0x03 &&
                    data.length >= 6 &&
                    data[2] == 0x02 &&
                    recentCommandType.contains("油缸")) ||
                (data[1] == 0x03 && recentCommandType.contains("油缸"))) {
              // 提取数据长度（第3个字节）
              int dataLength = data[2];
              if (data.length >= 3 + dataLength + 2) {
                // 确保有足够的数据和CRC
                // 提取数据部分（跳过头三个字节和最后两个CRC字节）
                List<int> dataBytes = data.sublist(3, 3 + dataLength);

                addLog('油缸面积数据长度: $dataLength 字节');
                addLog('油缸面积原始字节: [${bytesToHexString(dataBytes)}]');

                // 解析油缸面积内容
                String cylinderArea = "";

                // 方法1：尝试解析为数值（面积通常是数值）
                if (dataBytes.length == 2) {
                  // 如果是2字节，直接显示数值
                  int areaValue = (dataBytes[0] << 8) | dataBytes[1];
                  cylinderArea = "$areaValue";
                  addLog('油缸面积数值解析: $areaValue');
                } else if (dataBytes.length == 4) {
                  // 如果是4字节，直接显示数值
                  int areaValue = (dataBytes[0] << 24) |
                      (dataBytes[1] << 16) |
                      (dataBytes[2] << 8) |
                      dataBytes[3];
                  cylinderArea = "$areaValue";
                  addLog('油缸面积32位数值解析: $areaValue');
                } else {
                  // 尝试文本解码
                  try {
                    String gbkText = gbk.decode(dataBytes);
                    gbkText = gbkText.trim();
                    if (gbkText.isNotEmpty &&
                        !_containsUnprintableChars(gbkText)) {
                      cylinderArea = gbkText;
                      addLog('油缸面积GBK解码: "$gbkText"');
                    }
                  } catch (e) {
                    addLog('油缸面积GBK解码失败: $e');
                  }

                  // 如果GBK解码失败，尝试ASCII
                  if (cylinderArea.isEmpty) {
                    try {
                      String asciiText = '';
                      for (int byte in dataBytes) {
                        if (byte > 0 && byte < 127 && byte != 0) {
                          asciiText += String.fromCharCode(byte);
                        }
                      }
                      asciiText = asciiText.trim();
                      if (asciiText.isNotEmpty) {
                        cylinderArea = asciiText;
                        addLog('油缸面积ASCII解码: "$asciiText"');
                      }
                    } catch (e) {
                      addLog('油缸面积ASCII解码失败: $e');
                    }
                  }

                  // 如果都失败，使用十六进制
                  if (cylinderArea.isEmpty) {
                    cylinderArea =
                        bytesToHexString(dataBytes, withSpaces: false);
                    addLog('油缸面积十六进制表示: "$cylinderArea"');
                  }
                }

                addLog('========================');
                addLog('=== 接收到油缸面积 ===');
                addLog('解析结果: "$cylinderArea"');
                addLog('========================');

                notificationType = "油缸面积";
                specialContent = cylinderArea;

                addLog('【重要】油缸面积: $cylinderArea');
                _receivedDataController.add('【重要】油缸面积: $cylinderArea');
                _receivedDataController.add('油缸面积:$cylinderArea');

                // 更新设备信息缓存
                _deviceInfoCache['cylinderArea'] = cylinderArea;
                addLog('✅ 已更新油缸面积到缓存: $cylinderArea');
              } else {
                addLog('油缸面积响应数据长度不足，忽略处理');
              }
            }

            // 检查是否是160字节焊机数据响应 (01 03 A0 XX...XX)
            else if (data[1] == 0x03 && data.length >= 163 && data[2] == 0xA0) {
              // 160字节数据响应：01 03 A0 + 160字节数据 + 2字节CRC = 163字节
              addLog('========================');
              addLog('=== 接收到160字节焊机数据 ===');
              addLog('响应数据长度: ${data.length} 字节');
              addLog('========================');

              // 提取160字节数据部分（跳过头3个字节和最后2个CRC字节）
              List<int> weldingDataBytes = data.sublist(3, 163);

              addLog('160字节焊机数据已提取完成');
              addLog('数据长度验证: ${weldingDataBytes.length} 字节');

              // 将数据转换为连续的十六进制字符串显示（不分段）
              String formattedWeldingData =
                  bytesToHexString(weldingDataBytes, withSpaces: false);

              // notificationType = "160字节焊机数据";
              // specialContent = formattedWeldingData;

              addLog('【重要】160字节焊机数据已接收（分片重组）');
              _receivedDataController.add('【重要】160字节焊机数据已接收');
              _receivedDataController.add('160字节焊机数据:$formattedWeldingData');

              // 更新设备信息缓存，添加焊机数据
              _deviceInfoCache['weldingData'] = formattedWeldingData;
              addLog('✅ 已更新160字节焊机数据到缓存');
            }
          }
        }
      }

      // 推送到数据流
      _receivedDataController.add(hexString);

      // 如果有特殊通知，单独推送一条消息
      if (notificationType.isNotEmpty) {
        String specialMessage = '$notificationType:$specialContent';
        _receivedDataController.add(specialMessage);

        // 同时记录到日志
        addLog('特殊信息提取: $specialMessage');

        // 更新特征缓存
        _notificationType = notificationType;
        _specialContent = specialContent;
      }
    } catch (e) {
      addLog('处理接收数据错误: $e');
    }
  }

  // 计算Modbus CRC16 - 注意：仅用于发送命令时生成CRC校验码，接收数据时不进行CRC校验
  int _calculateModbusCRC(List<int> data) {
    int crc = 0xFFFF;

    for (int byte in data) {
      crc ^= byte;
      for (int i = 0; i < 8; i++) {
        if ((crc & 0x0001) != 0) {
          crc >>= 1;
          crc ^= 0xA001;
        } else {
          crc >>= 1;
        }
      }
    }

    // 确保返回的结果是Modbus标准的CRC值（低字节在前，高字节在后的字节顺序）
    // 但在整数表示中是正常的值
    return crc;
  }

  // 将整数CRC值转换为Modbus CRC字节数组（低字节在前，高字节在后）
  List<int> intToModbusCrc(int crc) {
    return [crc & 0xFF, (crc >> 8) & 0xFF];
  }

  // 资源释放
  void dispose() {
    _devicesController.close();
    _connectedDeviceController.close();
    _receivedDataController.close();
    _logController.close();
    disconnectDevice();
  }

  // 检查是否已连接
  bool get isConnected =>
      connectedDevice != null && writeCharacteristic != null;

  // 测试接收数据处理（供外部测试用）
  void testReceiveData(List<int> testData) {
    addLog('手动测试接收数据: ${bytesToHexString(testData)}');
    _handleReceivedData(testData);
  }

  // 添加方法 - 发送特定命令 (用于连接后自动发送查询命令)
  Future<bool> sendSpecificCommand() async {
    try {
      // 查询焊机编号的命令: 01 03 00 05 00 05 95 C8
      List<int> command = [0x01, 0x03, 0x00, 0x05, 0x00, 0x05, 0x95, 0xC8];
      addLog('发送特定命令: ${bytesToHexString(command)}');
      return await sendData(command);
    } catch (e) {
      addLog('发送特定命令错误: $e');
      return false;
    }
  }

  // 将字节数组格式化为带空格的十六进制字符串
  String bytesToHexString(List<int> bytes, {bool withSpaces = true}) {
    String separator = withSpaces ? ' ' : '';
    return bytes
        .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
        .join(separator);
  }

  // 解析焊机编号 - 从响应数据中提取焊机编号，跳过CRC校验
  String parseWeldingMachineNumber(List<int> data, {bool skipCrcCheck = true}) {
    try {
      // 检查数据是否符合焊机编号响应格式: 01 03 0A XX...XX CRC
      if (data.length < 8 ||
          data[0] != 0x01 ||
          data[1] != 0x03 ||
          data[2] != 0x0A) {
        addLog('数据格式不符合焊机编号响应: [${bytesToHexString(data)}]');
        return "格式错误";
      }

      // 详细记录原始数据
      addLog('解析焊机编号原始数据: [${bytesToHexString(data)}]');

      // 始终跳过CRC校验，直接提取数据
      // 提取焊机编号数据部分（从第4个字节开始，共5个字节）
      int endIndex = data.length > 8 ? 8 : data.length - 2; // 减去CRC字节
      List<int> machineNumberBytes = data.sublist(3, endIndex);
      String hexBytes = machineNumberBytes
          .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
          .join('');
      addLog(
          '焊机编号字节(${machineNumberBytes.length}字节): [${bytesToHexString(machineNumberBytes)}] = $hexBytes');

      // 尝试不同的解码方式来获取焊机编号

      // 方法1：尝试使用GBK解码（用于中文）
      try {
        // 使用项目中已有的gbk解码功能
        String gbkText = gbk.decode(machineNumberBytes);
        // 过滤空白字符
        gbkText = gbkText.trim();
        if (gbkText.isNotEmpty) {
          addLog('解析到焊机编号(GBK中文): "$gbkText" [HEX: $hexBytes]');
          return gbkText;
        }
      } catch (e) {
        addLog('GBK解码失败: $e');
      }

      // 方法2：直接ASCII字符转换，过滤掉非打印字符和结尾的0
      String machineNumber = '';
      for (int byte in machineNumberBytes) {
        if (byte > 0 && byte < 127) {
          // 可打印ASCII字符
          machineNumber += String.fromCharCode(byte);
        }
      }

      // 过滤末尾的空字符并去除两端空格
      machineNumber = machineNumber.trim();
      if (machineNumber.isNotEmpty) {
        addLog('解析到焊机编号(ASCII): "$machineNumber" [HEX: $hexBytes]');
        return machineNumber;
      }

      // 方法3：如果前两种方法解析结果为空，尝试将字节作为十六进制字符串显示
      String hexNumber = hexBytes;
      addLog('解析到焊机编号(HEX): "$hexNumber"');
      return "HEX:$hexNumber";
    } catch (e) {
      addLog('解析焊机编号错误: $e');
      return "解析错误($e)";
    }
  }

  // 获取特征的属性描述
  String _getCharacteristicProperties(
      fbp.BluetoothCharacteristic characteristic) {
    List<String> props = [];
    if (characteristic.properties.broadcast) props.add("broadcast");
    if (characteristic.properties.read) props.add("read");
    if (characteristic.properties.writeWithoutResponse)
      props.add("writeWithoutResponse");
    if (characteristic.properties.write) props.add("write");
    if (characteristic.properties.notify) props.add("notify");
    if (characteristic.properties.indicate) props.add("indicate");
    if (characteristic.properties.authenticatedSignedWrites)
      props.add("authenticatedSignedWrites");
    if (characteristic.properties.extendedProperties)
      props.add("extendedProperties");
    if (characteristic.properties.notifyEncryptionRequired)
      props.add("notifyEncryptionRequired");
    if (characteristic.properties.indicateEncryptionRequired)
      props.add("indicateEncryptionRequired");
    return props.join(", ");
  }

  // 检查字符串是否包含不可打印字符（可能是乱码）
  bool _containsUnprintableChars(String text) {
    for (int i = 0; i < text.length; i++) {
      int code = text.codeUnitAt(i);
      if ((code < 32 && code != 10 && code != 13) || code == 0xFFFD) {
        return true;
      }
    }
    return false;
  }

  // 发送查询焊机编号命令
  Future<bool> sendQueryMachineNumberCommand() async {
    try {
      // 焊机编号查询命令: 01 03 00 05 00 05 95 C8
      List<int> command = [0x01, 0x03, 0x00, 0x05, 0x00, 0x05, 0x95, 0xC8];
      addLog('发送查询焊机编号命令: ${bytesToHexString(command)}');
      return await sendData(command);
    } catch (e) {
      addLog('发送查询焊机编号命令错误: $e');
      return false;
    }
  }

  // 发送查询焊接标准命令
  Future<bool> sendQueryWeldingStandardCommand() async {
    try {
      // 焊接标准查询命令: 01 03 00 73 00 07 F5 D3
      List<int> command = [0x01, 0x03, 0x00, 0x73, 0x00, 0x07, 0xF5, 0xD3];
      addLog('发送查询焊接标准命令: ${bytesToHexString(command)}');
      return await sendData(command);
    } catch (e) {
      addLog('发送查询焊接标准命令错误: $e');
      return false;
    }
  }

  // 发送查询焊机机型命令
  Future<bool> sendQueryMachineTypeCommand() async {
    try {
      // 焊机机型查询命令: 01 03 00 7B 00 01 F4 13
      List<int> command = [0x01, 0x03, 0x00, 0x7B, 0x00, 0x01, 0xF4, 0x13];
      addLog('发送查询焊机机型命令: ${bytesToHexString(command)}');
      return await sendData(command);
    } catch (e) {
      addLog('发送查询焊机机型命令错误: $e');
      return false;
    }
  }

  // 发送查询油缸面积命令
  Future<bool> sendQueryCylinderAreaCommand() async {
    try {
      // 油缸面积查询命令: 01 03 00 7C 00 01 45 D2
      List<int> command = [0x01, 0x03, 0x00, 0x7C, 0x00, 0x01, 0x45, 0xD2];
      addLog('发送查询油缸面积命令: ${bytesToHexString(command)}');
      return await sendData(command);
    } catch (e) {
      addLog('发送查询油缸面积命令错误: $e');
      return false;
    }
  }

  // 发送获取160字节焊机数据命令
  Future<bool> sendQueryWeldingDataCommand() async {
    try {
      // 160字节焊机数据查询命令: 01 03 00 FA 00 50 65 C7
      List<int> command = [0x01, 0x03, 0x00, 0xFA, 0x00, 0x50, 0x65, 0xC7];
      addLog('发送查询160字节焊机数据命令: ${bytesToHexString(command)}');
      return await sendData(command);
    } catch (e) {
      addLog('发送查询160字节焊机数据命令错误: $e');
      return false;
    }
  }

  // 发送连接指示位命令
  Future<bool> sendConnectionStatusCommand() async {
    try {
      // 写入连接指示位命令: 01 06 00 03 00 01 B8 0A
      List<int> command = [0x01, 0x06, 0x00, 0x03, 0x00, 0x01, 0xB8, 0x0A];
      addLog('发送连接指示位命令: ${bytesToHexString(command)}');
      return await sendData(command);
    } catch (e) {
      addLog('发送连接指示位命令错误: $e');
      return false;
    }
  }

  // 读取设备全部信息（综合查询）
  Future<void> readAllDeviceInfo() async {
    if (!isConnected) {
      addLog('设备未连接，无法读取信息');
      return;
    }

    try {
      addLog('开始读取设备全部信息...');

      // 首先发送连接指示位
      await Future.delayed(Duration(milliseconds: 300));
      bool connectionStatus = await sendConnectionStatusCommand();
      addLog(connectionStatus ? '发送连接指示位成功' : '发送连接指示位失败');

      // 查询焊机编号
      await Future.delayed(Duration(milliseconds: 300));
      bool machineNumberStatus = await sendQueryMachineNumberCommand();
      addLog(machineNumberStatus ? '发送查询焊机编号命令成功' : '发送查询焊机编号命令失败');

      // 查询焊接标准
      await Future.delayed(Duration(milliseconds: 300));
      bool weldingStandardStatus = await sendQueryWeldingStandardCommand();
      addLog(weldingStandardStatus ? '发送查询焊接标准命令成功' : '发送查询焊接标准命令失败');

      // 查询焊机机型
      await Future.delayed(Duration(milliseconds: 300));
      bool machineTypeStatus = await sendQueryMachineTypeCommand();
      addLog(machineTypeStatus ? '发送查询焊机机型命令成功' : '发送查询焊机机型命令失败');

      // 查询油缸面积
      await Future.delayed(Duration(milliseconds: 300));
      bool cylinderAreaStatus = await sendQueryCylinderAreaCommand();
      addLog(cylinderAreaStatus ? '发送查询油缸面积命令成功' : '发送查询油缸面积命令失败');

      addLog('设备信息查询完成');
    } catch (e) {
      addLog('读取设备信息错误: $e');
    }
  }

  // 添加读取锁，防止重复调用
  bool _isReadingDeviceInfo = false;

  // 界面中读取设备信息的方法，返回包含各项信息的Map
  Future<Map<String, String>> readDeviceInfoForUI() async {
    // 检查是否已经在读取中，避免重复调用
    if (_isReadingDeviceInfo) {
      addLog('⚠️ 设备信息读取正在进行中，跳过重复调用');
      return {
        'connectionStatus': '读取中',
        'machineNumber': '读取中',
        'weldingStandard': '读取中',
        'machineType': '读取中',
        'cylinderArea': '读取中',
      };
    }

    _isReadingDeviceInfo = true; // 设置锁定状态

    try {
      Map<String, String> deviceInfo = {
        'connectionStatus': '未知',
        'machineNumber': '未知',
        'weldingStandard': '未知',
        'machineType': '未知',
        'cylinderArea': '未知',
      };

      if (!isConnected) {
        addLog('设备未连接，无法读取信息');
        deviceInfo['connectionStatus'] = '未连接';
        return deviceInfo;
      }

      addLog('🔄 UI刷新请求: 开始重新读取设备信息...');

      // 保存已有的有效缓存数据，避免重复查询
      Map<String, String> backupCache = Map.from(_deviceInfoCache);
      addLog('💾 备份当前缓存数据: $backupCache');

      // 清空之前的临时状态数据，但保留有效数据
      _notificationType = "";
      _specialContent = "";

      // 刷新时强制重新查询所有项目，不跳过已有缓存
      addLog('🔄 刷新模式：将重新查询所有设备信息项目');

      // 1. 首先发送连接指示位，确保设备连接状态
      addLog('🔄 [1/5] 发送连接指示位命令...');
      deviceInfo['connectionStatus'] = '正在检查连接...';
      bool connectionStatus = await sendConnectionStatusCommand();
      if (connectionStatus) {
        deviceInfo['connectionStatus'] = '已连接';
        addLog('✅ 连接指示位发送成功');
      } else {
        deviceInfo['connectionStatus'] = '连接异常';
        addLog('❌ 连接指示位发送失败');
      }
      await Future.delayed(Duration(milliseconds: 1000)); // 等待连接确认

      // 2. 强制查询焊机编号
      addLog('🔄 [2/5] 发送查询焊机编号命令...');
      deviceInfo['machineNumber'] = '正在读取...';
      bool machineNumberStatus = await sendQueryMachineNumberCommand();
      addLog(machineNumberStatus ? '✅ 焊机编号查询命令发送成功' : '❌ 焊机编号查询命令发送失败');
      await Future.delayed(Duration(milliseconds: 1000)); // 等待焊机编号响应

      // 3. 强制查询焊接标准
      addLog('🔄 [3/5] 发送查询焊接标准命令...');
      deviceInfo['weldingStandard'] = '正在读取...';
      bool weldingStandardStatus = await sendQueryWeldingStandardCommand();
      addLog(weldingStandardStatus ? '✅ 焊接标准查询命令发送成功' : '❌ 焊接标准查询命令发送失败');
      await Future.delayed(Duration(milliseconds: 1000));

      // 焊接标准可能需要更长等待时间，增加重试机制
      addLog('🔄 [3.1/5] 焊接标准等待额外响应时间...');
      await Future.delayed(Duration(milliseconds: 1500)); // 额外等待1.5秒

      // 如果还是没有收到焊接标准响应，尝试重发一次
      if (_deviceInfoCache['weldingStandard']?.isEmpty != false) {
        addLog('🔄 [3.2/5] 焊接标准首次查询无响应，重试查询...');
        bool retryWeldingStandard = await sendQueryWeldingStandardCommand();
        addLog(retryWeldingStandard ? '✅ 焊接标准重试命令发送成功' : '❌ 焊接标准重试命令发送失败');
        await Future.delayed(Duration(milliseconds: 2000)); // 重试后等待2秒
      }

      // 4. 强制查询焊机机型
      addLog('🔄 [4/5] 发送查询焊机机型命令...');
      deviceInfo['machineType'] = '正在读取...';
      bool machineTypeStatus = await sendQueryMachineTypeCommand();
      addLog(machineTypeStatus ? '✅ 焊机机型查询命令发送成功' : '❌ 焊机机型查询命令发送失败');
      await Future.delayed(Duration(milliseconds: 1000));

      // 5. 强制查询油缸面积
      addLog('🔄 [5/5] 发送查询油缸面积命令...');
      deviceInfo['cylinderArea'] = '正在读取...';
      bool cylinderAreaStatus = await sendQueryCylinderAreaCommand();
      addLog(cylinderAreaStatus ? '✅ 油缸面积查询命令发送成功' : '❌ 油缸面积查询命令发送失败');
      await Future.delayed(Duration(milliseconds: 1000));

      addLog('🔄 所有查询命令处理完成，等待设备响应中...');

      // 等待设备响应（由于增加了焊接标准重试，延长等待时间到7秒）
      await Future.delayed(Duration(seconds: 7));
      addLog('⏰ 等待完成，已查询所有5项设备信息（包含焊接标准重试）');

      // 从缓存中获取实际的解析结果
      addLog('📄 从缓存中获取解析结果...');

      // 打印当前缓存状态，帮助调试
      addLog('🔍 当前设备信息缓存状态:');
      addLog('   - 焊机编号: "${_deviceInfoCache['machineNumber']}"');
      addLog('   - 焊接标准: "${_deviceInfoCache['weldingStandard']}"');
      addLog('   - 焊机机型: "${_deviceInfoCache['machineType']}"');
      addLog('   - 油缸面积: "${_deviceInfoCache['cylinderArea']}"');
      addLog('   - 连接状态: "${_deviceInfoCache['connectionStatus']}"');

      // 处理焊机编号
      if (_deviceInfoCache['machineNumber']?.isNotEmpty == true) {
        deviceInfo['machineNumber'] = _deviceInfoCache['machineNumber']!;
        addLog('✅ 焊机编号: ${_deviceInfoCache['machineNumber']}');
      } else if (backupCache['machineNumber']?.isNotEmpty == true) {
        // 如果新查询失败，恢复备份数据
        deviceInfo['machineNumber'] = backupCache['machineNumber']!;
        _deviceInfoCache['machineNumber'] = backupCache['machineNumber']!;
        addLog('🔄 恢复焊机编号备份数据: ${backupCache['machineNumber']}');
      } else {
        deviceInfo['machineNumber'] = '设备不响应';
        addLog('❌ 焊机编号未获取到');
      }

      // 处理焊接标准
      if (_deviceInfoCache['weldingStandard']?.isNotEmpty == true) {
        deviceInfo['weldingStandard'] = _deviceInfoCache['weldingStandard']!;
        addLog('✅ 焊接标准: ${_deviceInfoCache['weldingStandard']}');
      } else if (backupCache['weldingStandard']?.isNotEmpty == true) {
        // 如果新查询失败，恢复备份数据
        deviceInfo['weldingStandard'] = backupCache['weldingStandard']!;
        _deviceInfoCache['weldingStandard'] = backupCache['weldingStandard']!;
        addLog('🔄 恢复焊接标准备份数据: ${backupCache['weldingStandard']}');
      } else {
        deviceInfo['weldingStandard'] = '设备不支持';
        addLog('ℹ️ 焊接标准: 设备可能不支持此查询');
      }

      // 处理焊机机型
      if (_deviceInfoCache['machineType']?.isNotEmpty == true) {
        deviceInfo['machineType'] = _deviceInfoCache['machineType']!;
        addLog('✅ 焊机机型: ${_deviceInfoCache['machineType']}');
      } else if (backupCache['machineType']?.isNotEmpty == true) {
        // 如果新查询失败，恢复备份数据
        deviceInfo['machineType'] = backupCache['machineType']!;
        _deviceInfoCache['machineType'] = backupCache['machineType']!;
        addLog('🔄 恢复焊机机型备份数据: ${backupCache['machineType']}');
      } else {
        deviceInfo['machineType'] = '设备不支持';
        addLog('ℹ️ 焊机机型: 设备可能不支持此查询');
      }

      // 处理油缸面积
      if (_deviceInfoCache['cylinderArea']?.isNotEmpty == true) {
        deviceInfo['cylinderArea'] = _deviceInfoCache['cylinderArea']!;
        addLog('✅ 油缸面积: ${_deviceInfoCache['cylinderArea']}');
      } else if (backupCache['cylinderArea']?.isNotEmpty == true) {
        // 如果新查询失败，恢复备份数据
        deviceInfo['cylinderArea'] = backupCache['cylinderArea']!;
        _deviceInfoCache['cylinderArea'] = backupCache['cylinderArea']!;
        addLog('🔄 恢复油缸面积备份数据: ${backupCache['cylinderArea']}');
      } else {
        deviceInfo['cylinderArea'] = '设备不支持';
        addLog('ℹ️ 油缸面积: 设备可能不支持此查询');
      }

      // 处理连接状态
      if (_deviceInfoCache['connectionStatus']?.isNotEmpty == true) {
        deviceInfo['connectionStatus'] = _deviceInfoCache['connectionStatus']!;
        addLog('✅ 连接状态: ${_deviceInfoCache['connectionStatus']}');
      } else if (backupCache['connectionStatus']?.isNotEmpty == true) {
        deviceInfo['connectionStatus'] = backupCache['connectionStatus']!;
        _deviceInfoCache['connectionStatus'] = backupCache['connectionStatus']!;
        addLog('🔄 恢复连接状态备份数据: ${backupCache['connectionStatus']}');
      }

      addLog('🔄 查询完成，返回设备信息');
      addLog('📋 最终设备信息: $deviceInfo');

      return deviceInfo;
    } catch (e) {
      addLog('❌ UI读取设备信息错误: $e');
      return {
        'connectionStatus': '读取错误',
        'machineNumber': '读取错误',
        'weldingStandard': '读取错误',
        'machineType': '读取错误',
        'cylinderArea': '读取错误',
      };
    } finally {
      _isReadingDeviceInfo = false; // 无论成功失败都要释放锁
      addLog('🔓 设备信息读取锁已释放');
    }
  }

  // 添加解析160字节焊机数据的测试方法
  void parseWeldingDataFromHex(String hexString) {
    try {
      // 移除空格并转换为大写
      hexString = hexString.replaceAll(' ', '').toUpperCase();

      addLog('开始解析160字节焊机数据');
      addLog('原始十六进制字符串长度: ${hexString.length}');

      // 验证数据长度
      if (hexString.length != 330) {
        // (1+1+1+160+2)*2 = 330个字符
        addLog('错误：数据长度不正确，期望330个字符，实际${hexString.length}个字符');
        return;
      }

      // 将十六进制字符串转换为字节数组
      List<int> data = [];
      for (int i = 0; i < hexString.length; i += 2) {
        String byteHex = hexString.substring(i, i + 2);
        int byteValue = int.parse(byteHex, radix: 16);
        data.add(byteValue);
      }

      addLog('转换后的字节数组长度: ${data.length}');

      // 验证数据格式：01 03 A0
      if (data.length >= 3 &&
          data[0] == 0x01 &&
          data[1] == 0x03 &&
          data[2] == 0xA0) {
        addLog('✅ 数据格式验证通过：01 03 A0 (设备地址1, 功能码3, 数据长度160字节)');

        // 手动调用数据处理方法
        addLog('开始处理160字节焊机数据...');
        _handleReceivedData(data);
      } else {
        addLog('❌ 数据格式验证失败');
        addLog('期望格式：01 03 A0');
        addLog(
            '实际格式：${data.take(3).map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase()).join(' ')}');
      }
    } catch (e) {
      addLog('解析160字节焊机数据出错: $e');
    }
  }

  // 添加160字节数据分片重组处理方法
  bool _handle160ByteDataFragments(List<int> data) {
    try {
      // 检测是否是160字节数据响应的开始片段（01 03 A0）
      if (data.length >= 3 &&
          data[0] == 0x01 &&
          data[1] == 0x03 &&
          data[2] == 0xA0) {
        addLog('🔍 检测到160字节数据响应开始片段，启动分片重组模式');
        _isReceivingWeldingData = true;
        _weldingDataBuffer.clear();
        _weldingDataBuffer.addAll(data);

        // 设置超时定时器，5秒内未接收完整数据则超时
        _weldingDataTimeout?.cancel();
        _weldingDataTimeout = Timer(Duration(seconds: 5), () {
          addLog('⚠️ 160字节数据接收超时，已接收 ${_weldingDataBuffer.length} 字节');
          _resetWeldingDataReceiving();
        });

        addLog(
            '📦 160字节数据片段1: 已接收 ${_weldingDataBuffer.length} / $_expectedWeldingDataLength 字节');

        // 检查是否已经接收完整
        _checkWeldingDataComplete();
        return true;
      }

      // 如果正在接收160字节数据，继续累积片段
      if (_isReceivingWeldingData) {
        _weldingDataBuffer.addAll(data);
        addLog(
            '📦 160字节数据片段: 已接收 ${_weldingDataBuffer.length} / $_expectedWeldingDataLength 字节');

        // 检查是否已经接收完整
        _checkWeldingDataComplete();
        return true;
      }

      return false; // 不是160字节数据片段
    } catch (e) {
      addLog('160字节数据分片处理出错: $e');
      _resetWeldingDataReceiving();
      return false;
    }
  }

  // 检查160字节数据是否接收完整
  void _checkWeldingDataComplete() {
    if (_weldingDataBuffer.length >= _expectedWeldingDataLength) {
      addLog('✅ 160字节数据接收完成！总计 ${_weldingDataBuffer.length} 字节');

      // 取前163字节作为完整数据包
      List<int> completeData =
          _weldingDataBuffer.take(_expectedWeldingDataLength).toList();

      // 验证数据完整性
      if (completeData.length == 163 &&
          completeData[0] == 0x01 &&
          completeData[1] == 0x03 &&
          completeData[2] == 0xA0) {
        addLog('========================');
        addLog('=== 接收到完整160字节焊机数据（分片重组） ===');
        addLog('响应数据长度: ${completeData.length} 字节');
        addLog('========================');

        // 提取160字节数据部分（跳过头3个字节和最后2个CRC字节）
        List<int> weldingDataBytes = completeData.sublist(3, 163);

        addLog('160字节焊机数据已提取完成');
        addLog('数据长度验证: ${weldingDataBytes.length} 字节');

        // 将数据转换为连续的十六进制字符串显示（不分段）
        String formattedWeldingData =
            bytesToHexString(weldingDataBytes, withSpaces: false);

        // notificationType = "160字节焊机数据";
        // specialContent = formattedWeldingData;

        addLog('【重要】160字节焊机数据已接收（分片重组）');
        _receivedDataController.add('【重要】160字节焊机数据已接收');
        _receivedDataController.add('160字节焊机数据:$formattedWeldingData');

        // 更新设备信息缓存，添加焊机数据
        _deviceInfoCache['weldingData'] = formattedWeldingData;
        addLog('✅ 已更新160字节焊机数据到缓存（分片重组）');
      } else {
        addLog('❌ 160字节数据完整性验证失败');
      }

      // 重置接收状态
      _resetWeldingDataReceiving();
    }
  }

  // 重置160字节数据接收状态
  void _resetWeldingDataReceiving() {
    _isReceivingWeldingData = false;
    _weldingDataBuffer.clear();
    _weldingDataTimeout?.cancel();
    _weldingDataTimeout = null;
  }

  // 🎯 连接成功后自动发送用户ID
  Future<void> _sendUserIdAfterConnection() async {
    try {
      addLog('🎯 开始自动发送用户ID到焊机...');

      // 导入用户服务
      final userService = UserService();

      // 获取当前登录的用户ID
      String? userId = await userService.getUserId();

      if (userId == null || userId.isEmpty) {
        addLog('❌ 未找到登录用户ID，无法发送用户ID');
        return;
      }

      addLog('📤 准备发送用户ID: $userId');

      // 调用发送用户ID的方法
      bool success = await _sendUserIdToWeldingMachine(userId);

      if (success) {
        addLog('✅ 用户ID已自动发送到焊机');
        _receivedDataController.add('✅ 用户ID已自动发送: $userId');
      } else {
        addLog('❌ 用户ID自动发送失败');
        _receivedDataController.add('❌ 用户ID自动发送失败');
      }
    } catch (e) {
      addLog('❌ 自动发送用户ID异常: $e');
    }
  }

  // 🎯 发送用户ID到焊机的具体实现
  Future<bool> _sendUserIdToWeldingMachine(String userId) async {
    try {
      if (!isConnected) {
        addLog('❌ 蓝牙未连接，无法发送用户ID');
        return false;
      }

      // 将用户ID转换为10字节数据
      List<int> originalBytes = utf8.encode(userId);

      // 创建一个可变长度的列表，确保是10字节，不足补0，超出截断
      const int requiredLength = 10;
      List<int> userIdBytes = [];

      if (originalBytes.length >= requiredLength) {
        // 如果超出长度，截断到10字节
        userIdBytes = originalBytes.sublist(0, requiredLength);
      } else {
        // 如果不足10字节，先复制原数据，然后补0
        userIdBytes = List<int>.from(originalBytes);
        userIdBytes
            .addAll(List<int>.filled(requiredLength - originalBytes.length, 0));
      }

      // 构建Modbus命令: 01 10 00 0F 00 05 0A + 10字节用户ID数据
      List<int> command = [
        0x01, // 从站地址
        0x10, // 功能码：写入多个保持寄存器
        0x00, 0x0F, // 起始地址 (0x000F)
        0x00, 0x05, // 寄存器数量：5个字
        0x0A, // 字节数量：10字节
      ];

      // 添加用户ID数据
      command.addAll(userIdBytes);

      // 计算并添加CRC校验
      int crc = _calculateSimpleCRC16(command);
      command.add(crc & 0xFF); // CRC低字节
      command.add((crc >> 8) & 0xFF); // CRC高字节

      String commandHex = command
          .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
          .join(' ');
      addLog('📤 发送用户ID命令: $commandHex');
      addLog('📝 用户ID: $userId');

      // 发送命令到焊机
      bool success = await sendData(command);

      if (success) {
        addLog('✅ 用户ID命令发送成功');
      } else {
        addLog('❌ 用户ID命令发送失败');
      }

      return success;
    } catch (e) {
      addLog('❌ 发送用户ID异常: $e');
      return false;
    }
  }

  // 🔧 简单的CRC16校验算法（避免依赖其他服务）
  int _calculateSimpleCRC16(List<int> data) {
    int crc = 0xFFFF;
    for (int byte in data) {
      crc ^= byte;
      for (int i = 0; i < 8; i++) {
        if ((crc & 0x0001) != 0) {
          crc = (crc >> 1) ^ 0xA001;
        } else {
          crc = crc >> 1;
        }
      }
    }
    return crc;
  }
}
