import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/weld_record.dart';

class WeldRecordService {
  static const String _storageKey = 'weld_records';
  final Uuid _uuid = Uuid();

  // 获取所有焊接记录
  Future<List<WeldRecord>> getAllRecords() async {
    final prefs = await SharedPreferences.getInstance();
    final String? recordsJson = prefs.getString(_storageKey);

    if (recordsJson == null) {
      return [];
    }

    final List<dynamic> decoded = json.decode(recordsJson);
    return decoded.map((item) => WeldRecord.fromJson(item)).toList();
  }

  // 根据项目ID获取焊接记录
  Future<List<WeldRecord>> getRecordsByProjectId(String projectId) async {
    final records = await getAllRecords();
    return records.where((record) => record.projectId == projectId).toList();
  }

  // 获取未上传的记录
  Future<List<WeldRecord>> getUnuploadedRecords() async {
    final records = await getAllRecords();
    return records.where((record) => !record.isUploaded).toList();
  }

  // 保存新的焊接记录
  Future<WeldRecord> saveRecord(WeldRecord record) async {
    final records = await getAllRecords();

    // 使用现有ID或生成新ID
    final newRecord = WeldRecord(
      id: record.id.isEmpty ? _uuid.v4() : record.id,
      projectId: record.projectId,
      projectName: record.projectName,
      weldCode: record.weldCode,
      operatorId: record.operatorId,
      operatorName: record.operatorName,
      deviceId: record.deviceId,
      timeStamp: record.timeStamp,
      faceImagePath: record.faceImagePath,
      deviceImagePath: record.deviceImagePath,
      pipeImagePath: record.pipeImagePath,
      parameters: record.parameters,
      isUploaded: record.isUploaded,
    );

    records.add(newRecord);
    await _saveAllRecords(records);
    return newRecord;
  }

  // 更新焊接记录
  Future<bool> updateRecord(WeldRecord updatedRecord) async {
    final records = await getAllRecords();
    final index = records.indexWhere((record) => record.id == updatedRecord.id);

    if (index >= 0) {
      records[index] = updatedRecord;
      await _saveAllRecords(records);
      return true;
    }

    return false;
  }

  // 更新记录上传状态
  Future<bool> updateUploadStatus(String recordId, bool isUploaded) async {
    final records = await getAllRecords();
    final index = records.indexWhere((record) => record.id == recordId);

    if (index >= 0) {
      records[index] = records[index].copyWithUploadStatus(isUploaded);
      await _saveAllRecords(records);
      return true;
    }

    return false;
  }

  // 删除焊接记录
  Future<bool> deleteRecord(String recordId) async {
    final records = await getAllRecords();
    final initialLength = records.length;

    records.removeWhere((record) => record.id == recordId);

    if (records.length != initialLength) {
      await _saveAllRecords(records);
      return true;
    }

    return false;
  }

  // 保存所有记录到SharedPreferences
  Future<void> _saveAllRecords(List<WeldRecord> records) async {
    final prefs = await SharedPreferences.getInstance();
    final String encoded = json.encode(records.map((e) => e.toJson()).toList());
    await prefs.setString(_storageKey, encoded);
  }
}
