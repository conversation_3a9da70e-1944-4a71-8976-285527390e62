import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;
import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'package:fluttertoast/fluttertoast.dart';
import 'user_service.dart';
import 'offline_mode_service.dart';

class PipePhotoService {
  static final OfflineModeService _offlineModeService = OfflineModeService();

  // 拍摄管道照片
  static Future<XFile?> takePipePhoto() async {
    try {
      final ImagePicker _picker = ImagePicker();
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      return image;
    } catch (e) {
      print('拍摄管道照片出错: $e');
      showToast('拍照出错: $e');
      return null;
    }
  }

  // 处理管道照片（根据模式选择上传或保存本地）
  static Future<Map<String, dynamic>?> processPipePhoto(XFile photoFile) async {
    try {
      // 初始化离线模式服务
      await _offlineModeService.initialize();
      final isOfflineMode = _offlineModeService.currentState.isOfflineMode;

      if (isOfflineMode) {
        // 离线模式：保存到本地
        showToast('离线模式，正在保存管道照片到本地...');
        final result = await _savePipePhotoOffline(photoFile);
        showToast('管道照片已保存到本地');
        return result;
      } else {
        // 在线模式：上传到服务器
        showToast('正在上传管道照片...');
        final photoPath = await uploadPipePhoto(photoFile);
        if (photoPath != null) {
          final verifyResult = await verifyPipePhoto(photoPath);
          return {
            'path': photoFile.path,
            'serverPath': photoPath,
            'result': verifyResult
          };
        }
        return null;
      }
    } catch (e) {
      print('处理管道照片错误: $e');
      showToast('处理管道照片出错: $e');
      return null;
    }
  }

  // 保存管道照片到离线存储
  static Future<Map<String, dynamic>?> _savePipePhotoOffline(
      XFile photoFile) async {
    try {
      // 读取图片文件并转换为Base64
      final File imageFile = File(photoFile.path);
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);

      // 创建管道数据
      final Map<String, dynamic> pipeData = {
        'timestamp': DateTime.now().toIso8601String(),
        'fileName': photoFile.path.split('/').last,
        'fileSize': bytes.length,
        'captureLocation': '未知位置', // 如果有GPS可以获取实际位置
        'pipeInfo': {
          'diameter': '未知',
          'material': '未知',
          'thickness': '未知',
        },
        'deviceInfo': {
          'platform': Platform.operatingSystem,
          'version': Platform.operatingSystemVersion,
        }
      };

      // 保存到离线存储
      final id = await _offlineModeService
          .savePipeInspectionData(pipeData, [base64Image]);

      print('管道照片已保存到离线存储，ID: $id');

      return {'path': photoFile.path, 'offlineId': id, 'result': '离线保存成功'};
    } catch (e) {
      print('保存管道照片到离线存储失败: $e');
      throw e;
    }
  }

  // 上传管道照片
  static Future<String?> uploadPipePhoto(XFile photoFile) async {
    try {
      // 获取当前登录用户的token
      final userService = UserService();
      String? token = await userService.getToken();
      if (token == null || token.isEmpty) {
        print('错误: 未找到用户令牌，无法上传照片');
        showToast('未找到用户令牌，请重新登录');
        return null;
      }

      print('开始上传管道照片...');
      showToast('正在上传管道照片...');

      // 上传照片
      final File imageFile = File(photoFile.path);
      final String fileName = photoFile.path.split('/').last;

      // 创建multipart请求
      var request = http.MultipartRequest(
          'POST', Uri.parse('http://************/apk/ugp/fileupload'));

      // 使用获取到的token
      request.headers['Token'] = token;
      request.headers['Accept'] = '*/*';
      request.headers['User-Agent'] = 'PostmanRuntime-ApipostRuntime/1.1.0';

      // 添加文件
      request.files.add(http.MultipartFile.fromBytes(
          'file', await imageFile.readAsBytes(),
          filename: fileName));

      // 发送请求
      print('发送上传请求...');
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      // 处理响应
      print('上传响应状态码: ${response.statusCode}');
      print('上传响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        print('解析后的响应数据: $responseData');

        // 检查业务状态码
        if (responseData is Map<String, dynamic> &&
            responseData['status'] == 200) {
          print('管道照片上传成功');
          showToast('管道照片上传成功');

          // 获取上传后的文件路径
          String photoPath = '';

          // 检查result是否为Map类型(键值对)
          if (responseData['result'] is Map) {
            // 从第一个键中获取路径 (格式: {"/kf/download?fileName=/upload/...": "filename.jpg"})
            final Map resultMap = responseData['result'] as Map;
            if (resultMap.isNotEmpty) {
              photoPath = resultMap.keys.first as String;
            }
          }
          // 检查result是否为String类型
          else if (responseData['result'] is String) {
            photoPath = responseData['result'] as String;
          }

          print('提取的照片路径: $photoPath');
          return photoPath;
        } else {
          print('响应格式不正确或状态码不为200');
          showToast('响应格式不正确');
          return null;
        }
      } else {
        print('管道照片上传失败: ${response.statusCode}');
        showToast('管道照片上传失败: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('上传管道照片错误: $e');
      showToast('上传管道照片出错: $e');
      return null;
    }
  }

  // 验证管道照片
  static Future<String?> verifyPipePhoto(String photoPath) async {
    try {
      print('开始验证管道照片...');
      showToast('开始验证管道照片...');

      // 获取当前登录用户的token
      final userService = UserService();
      String? token = await userService.getToken();
      if (token == null || token.isEmpty) {
        print('错误: 未找到用户令牌，无法验证管道照片');
        showToast('未找到用户令牌，请重新登录');
        return null;
      }

      // 准备请求数据
      final Map<String, dynamic> requestData = {
        'stage': '管材',
        'projectId': '1784770032505589760',
        'code': 'E11A58DFFA7201B484E893F9944ADB28',
        'random': '10000000000',
        'photo': photoPath,
        'location': '123,112'
      };

      print('验证接口请求数据: ${json.encode(requestData)}');

      // 发起HTTP POST请求
      final response = await http.post(
          Uri.parse('http://************/apk/ugp/verify/checks'),
          headers: {
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'User-Agent': 'PostmanRuntime-ApipostRuntime/1.1.0',
            'token': token
          },
          body: json.encode(requestData));

      // 处理响应
      print('验证接口响应状态码: ${response.statusCode}');
      print('验证接口响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final verifyData = json.decode(response.body);
        if (verifyData is Map<String, dynamic>) {
          // 获取result值作为验证结果
          final result = verifyData['result']?.toString() ?? "未知结果";

          if (verifyData['code'] == 200 || verifyData['status'] == 200) {
            if (result == "通过") {
              print('管道照片验证成功: $result');
              showToast('管道照片验证成功: $result');
              return result;
            } else {
              print('管道照片验证失败: $result');
              showToast('管道照片验证失败: $result');
              return result;
            }
          } else {
            final errorMsg =
                verifyData['message'] ?? verifyData['devMessage'] ?? result;
            print('管道照片验证失败: $errorMsg');
            showToast('管道照片验证失败: $errorMsg');
            return errorMsg;
          }
        } else {
          print('管道照片验证响应格式错误');
          showToast('管道照片验证响应格式错误');
          return "响应格式错误";
        }
      } else {
        print('调用验证接口失败: ${response.statusCode}');
        showToast('验证接口调用失败: ${response.statusCode}');
        return "调用验证接口失败: ${response.statusCode}";
      }
    } catch (e) {
      print('验证管道照片出错: $e');
      showToast('管道照片验证过程出错: $e');
      return "验证过程出错: $e";
    }
  }

  // 显示Toast提示
  static void showToast(String message) {
    print(message); // 同时在控制台打印

    Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 2,
        backgroundColor: Colors.black87,
        textColor: Colors.white,
        fontSize: 16.0);
  }
}
