import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/simplified_welding_record.dart';
import 'bluetooth_service.dart';

/// 简化的焊接记录管理服务
/// 核心功能：管理基于焊口编号的焊接记录，确保数据完整性
class SimplifiedWeldingRecordService {
  static final SimplifiedWeldingRecordService _instance =
      SimplifiedWeldingRecordService._internal();
  factory SimplifiedWeldingRecordService() => _instance;
  SimplifiedWeldingRecordService._internal();

  final BleService _bleService = BleService();

  static const String _recordPrefix = 'welding_record_';
  static const String _recordListKey = 'welding_record_list';

  /// 保存焊接记录
  Future<bool> saveRecord(SimplifiedWeldingRecord record) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 保存单个记录
      await prefs.setString(
        '$_recordPrefix${record.jointNumber}', 
        record.toJsonString()
      );

      // 更新记录列表
      await _updateRecordList(record.jointNumber);

      _bleService.addLog('【焊接记录服务】记录已保存: ${record.jointNumber}');
      return true;
    } catch (e) {
      _bleService.addLog('【焊接记录服务】保存记录失败: $e');
      return false;
    }
  }

  /// 获取焊接记录
  Future<SimplifiedWeldingRecord?> getRecord(String jointNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recordJson = prefs.getString('$_recordPrefix$jointNumber');
      
      if (recordJson != null) {
        return SimplifiedWeldingRecord.fromJsonString(recordJson);
      }
      return null;
    } catch (e) {
      _bleService.addLog('【焊接记录服务】获取记录失败: $e');
      return null;
    }
  }

  /// 更新焊接记录
  Future<bool> updateRecord(SimplifiedWeldingRecord record) async {
    return await saveRecord(record);
  }

  /// 删除焊接记录
  Future<bool> deleteRecord(String jointNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 删除记录
      await prefs.remove('$_recordPrefix$jointNumber');
      
      // 更新记录列表
      await _removeFromRecordList(jointNumber);

      _bleService.addLog('【焊接记录服务】记录已删除: $jointNumber');
      return true;
    } catch (e) {
      _bleService.addLog('【焊接记录服务】删除记录失败: $e');
      return false;
    }
  }

  /// 获取所有焊接记录
  Future<List<SimplifiedWeldingRecord>> getAllRecords() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recordListJson = prefs.getString(_recordListKey) ?? '[]';
      final List<String> jointNumbers = List<String>.from(json.decode(recordListJson));
      
      final records = <SimplifiedWeldingRecord>[];
      for (final jointNumber in jointNumbers) {
        final record = await getRecord(jointNumber);
        if (record != null) {
          records.add(record);
        }
      }

      // 按时间排序（最新的在前）
      records.sort((a, b) => b.startTime.compareTo(a.startTime));
      return records;
    } catch (e) {
      _bleService.addLog('【焊接记录服务】获取所有记录失败: $e');
      return [];
    }
  }

  /// 获取未上传的记录
  Future<List<SimplifiedWeldingRecord>> getUnuploadedRecords() async {
    try {
      final allRecords = await getAllRecords();
      return allRecords.where((record) => !record.isUploaded).toList();
    } catch (e) {
      _bleService.addLog('【焊接记录服务】获取未上传记录失败: $e');
      return [];
    }
  }

  /// 获取今日记录
  Future<List<SimplifiedWeldingRecord>> getTodayRecords() async {
    try {
      final today = DateTime.now();
      final dateStr = '${today.year.toString().padLeft(4, '0')}'
          '${today.month.toString().padLeft(2, '0')}'
          '${today.day.toString().padLeft(2, '0')}';

      final allRecords = await getAllRecords();
      return allRecords
          .where((record) => record.jointNumber.startsWith(dateStr))
          .toList();
    } catch (e) {
      _bleService.addLog('【焊接记录服务】获取今日记录失败: $e');
      return [];
    }
  }

  /// 获取成功的记录
  Future<List<SimplifiedWeldingRecord>> getSuccessfulRecords() async {
    try {
      final allRecords = await getAllRecords();
      return allRecords.where((record) => record.isSuccessful).toList();
    } catch (e) {
      _bleService.addLog('【焊接记录服务】获取成功记录失败: $e');
      return [];
    }
  }

  /// 标记记录为已上传
  Future<bool> markAsUploaded(String jointNumber) async {
    try {
      final record = await getRecord(jointNumber);
      if (record != null) {
        final updatedRecord = record.markAsUploaded();
        return await updateRecord(updatedRecord);
      }
      return false;
    } catch (e) {
      _bleService.addLog('【焊接记录服务】标记上传失败: $e');
      return false;
    }
  }

  /// 完成焊接记录
  Future<bool> completeRecord(String jointNumber, int finalStatus) async {
    try {
      final record = await getRecord(jointNumber);
      if (record != null) {
        final completedRecord = record.complete(finalStatus);
        return await updateRecord(completedRecord);
      }
      return false;
    } catch (e) {
      _bleService.addLog('【焊接记录服务】完成记录失败: $e');
      return false;
    }
  }

  /// 清理过期记录（保留30天）
  Future<void> cleanupOldRecords() async {
    try {
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
      final cutoffStr = '${cutoffDate.year.toString().padLeft(4, '0')}'
          '${cutoffDate.month.toString().padLeft(2, '0')}'
          '${cutoffDate.day.toString().padLeft(2, '0')}';

      final allRecords = await getAllRecords();
      int deletedCount = 0;

      for (final record in allRecords) {
        if (record.jointNumber.length >= 8 &&
            record.jointNumber.substring(0, 8).compareTo(cutoffStr) < 0) {
          await deleteRecord(record.jointNumber);
          deletedCount++;
        }
      }

      _bleService.addLog('【焊接记录服务】清理完成，删除 $deletedCount 条过期记录');
    } catch (e) {
      _bleService.addLog('【焊接记录服务】清理过期记录失败: $e');
    }
  }

  /// 获取统计信息
  Future<Map<String, int>> getStatistics() async {
    try {
      final allRecords = await getAllRecords();
      final todayRecords = await getTodayRecords();
      final successfulRecords = allRecords.where((r) => r.isSuccessful).length;
      final unuploadedRecords = allRecords.where((r) => !r.isUploaded).length;

      return {
        'total': allRecords.length,
        'today': todayRecords.length,
        'successful': successfulRecords,
        'unuploaded': unuploadedRecords,
      };
    } catch (e) {
      _bleService.addLog('【焊接记录服务】获取统计信息失败: $e');
      return {};
    }
  }

  /// 更新记录列表
  Future<void> _updateRecordList(String jointNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recordListJson = prefs.getString(_recordListKey) ?? '[]';
      final List<String> jointNumbers = List<String>.from(json.decode(recordListJson));
      
      if (!jointNumbers.contains(jointNumber)) {
        jointNumbers.add(jointNumber);
        await prefs.setString(_recordListKey, json.encode(jointNumbers));
      }
    } catch (e) {
      _bleService.addLog('【焊接记录服务】更新记录列表失败: $e');
    }
  }

  /// 从记录列表中移除
  Future<void> _removeFromRecordList(String jointNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recordListJson = prefs.getString(_recordListKey) ?? '[]';
      final List<String> jointNumbers = List<String>.from(json.decode(recordListJson));
      
      jointNumbers.remove(jointNumber);
      await prefs.setString(_recordListKey, json.encode(jointNumbers));
    } catch (e) {
      _bleService.addLog('【焊接记录服务】从记录列表移除失败: $e');
    }
  }
}
