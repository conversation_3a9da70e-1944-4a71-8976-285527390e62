import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'bluetooth_service.dart';
import 'command_service.dart';
import 'offline_storage_service.dart';
import '../services/location_service.dart';
import '../models/offline_data_model.dart';

/// 焊口编号管理服务
/// 功能：生成焊口编号、管理焊接状态、写入PLC寄存器
class WeldingJointNumberService {
  static final WeldingJointNumberService _instance =
      WeldingJointNumberService._internal();
  factory WeldingJointNumberService() => _instance;
  WeldingJointNumberService._internal();

  final BleService _bleService = BleService();
  final CommandService _commandService = CommandService();
  final OfflineStorageService _offlineStorageService = OfflineStorageService();

  // 🔧 懒加载GPS服务，避免创建时就初始化
  LocationService? _locationService;
  LocationService get _locationServiceInstance {
    _locationService ??= LocationService();
    return _locationService!;
  }

  static const String _currentJointNumberKey = 'current_joint_number';
  static const String _dailySequenceKey = 'daily_sequence';
  static const String _lastGenerateDateKey = 'last_generate_date';
  static const String _weldingStatusKey = 'welding_status';

  /// 焊接状态枚举
  static const int WELDING_SUCCESS = 1; // 焊接成功
  static const int HEAT_ABSORPTION_FAILED = 2; // 吸热失败
  static const int CURLING_FAILED = 3; // 卷边失败
  static const int COOLING_FAILED = 4; // 冷却失败

  /// 人脸识别和活体检测成功后自动生成焊口编号
  Future<String> generateWeldingJointNumber() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final dateStr = '${today.year.toString().padLeft(4, '0')}'
          '${today.month.toString().padLeft(2, '0')}'
          '${today.day.toString().padLeft(2, '0')}';

      // 获取上次生成日期
      final lastDate = prefs.getString(_lastGenerateDateKey) ?? '';

      int sequence;
      if (lastDate == dateStr) {
        // 同一天，序列号递增
        sequence = prefs.getInt(_dailySequenceKey) ?? 0;
        sequence++;
      } else {
        // 新的一天，序列号重置为1
        sequence = 1;
        await prefs.setString(_lastGenerateDateKey, dateStr);
      }

      // 生成焊口编号：年月日+4位序列号
      final jointNumber = '$dateStr${sequence.toString().padLeft(4, '0')}';

      // 保存当前序列号
      await prefs.setInt(_dailySequenceKey, sequence);
      await prefs.setString(_currentJointNumberKey, jointNumber);

      // 初始化焊接状态为未成功（用于重试逻辑）
      await _setWeldingStatus(jointNumber, 0); // 0表示未开始

      _bleService.addLog('【焊口编号服务】生成新的焊口编号: $jointNumber');

      return jointNumber;
    } catch (e) {
      _bleService.addLog('【焊口编号服务】生成焊口编号失败: $e');
      throw Exception('生成焊口编号失败: $e');
    }
  }

  /// 获取当前焊口编号
  Future<String?> getCurrentJointNumber() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_currentJointNumberKey);
  }

  /// 检查并处理焊接状态
  /// 如果焊接不成功，保持当前编号不变；如果成功，下次可以生成新编号
  Future<bool> handleWeldingResult(int statusCode) async {
    try {
      final currentJointNumber = await getCurrentJointNumber();
      if (currentJointNumber == null) {
        _bleService.addLog('【焊口编号服务】错误：没有当前焊口编号');
        return false;
      }

      await _setWeldingStatus(currentJointNumber, statusCode);

      String statusMessage;
      switch (statusCode) {
        case WELDING_SUCCESS:
          statusMessage = '焊接成功';
          // 焊接成功，允许生成下一个编号
          await _markCurrentJointCompleted();
          break;
        case HEAT_ABSORPTION_FAILED:
          statusMessage = '吸热失败';
          break;
        case CURLING_FAILED:
          statusMessage = '卷边失败';
          break;
        case COOLING_FAILED:
          statusMessage = '冷却失败';
          break;
        default:
          statusMessage = '未知状态';
      }

      _bleService.addLog('【焊口编号服务】焊口 $currentJointNumber 状态更新: $statusMessage');

      // 如果焊接不成功，保持当前编号，等待重试
      if (statusCode != WELDING_SUCCESS) {
        _bleService.addLog('【焊口编号服务】焊接未成功，焊口编号保持不变: $currentJointNumber');
      }

      return true;
    } catch (e) {
      _bleService.addLog('【焊口编号服务】处理焊接结果失败: $e');
      return false;
    }
  }

  /// 发送用户ID到焊机
  /// 指令格式：01 10 00 0F 00 05 0A XX...XX CRC
  /// 寄存器地址：0x000F，5个字，10字节用户ID数据
  Future<bool> writeUserIdToWeldingMachine(String userId) async {
    try {
      if (!_bleService.isConnected) {
        _bleService.addLog('【焊口编号服务】错误：蓝牙未连接，无法发送用户ID');
        return false;
      }

      // 将用户ID转换为10字节数据
      List<int> userIdBytes = utf8.encode(userId);

      // 确保是10字节，不足补0，超出截断
      const int requiredLength = 10;
      if (userIdBytes.length < requiredLength) {
        userIdBytes
            .addAll(List<int>.filled(requiredLength - userIdBytes.length, 0));
      } else if (userIdBytes.length > requiredLength) {
        userIdBytes = userIdBytes.sublist(0, requiredLength);
      }

      // 构建Modbus命令: 01 10 00 0F 00 05 0A + 10字节用户ID数据
      List<int> command = [
        0x01, // 从站地址
        0x10, // 功能码：写入多个保持寄存器
        0x00, 0x0F, // 起始地址 (0x000F)
        0x00, 0x05, // 寄存器数量：5个字
        0x0A, // 字节数量：10字节
      ];

      // 添加用户ID数据
      command.addAll(userIdBytes);

      // 计算并添加CRC校验
      int crc = _commandService.calculateCRC16Modbus(command);
      command.add(crc & 0xFF); // CRC低字节
      command.add((crc >> 8) & 0xFF); // CRC高字节

      String commandHex = command
          .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
          .join(' ');
      _bleService.addLog('【焊口编号服务】发送用户ID到焊机: $commandHex');
      _bleService.addLog('【焊口编号服务】用户ID: $userId');

      // 发送命令到焊机
      bool success = await _bleService.sendData(command);

      if (success) {
        _bleService.addLog('【焊口编号服务】用户ID已成功发送到焊机');
      } else {
        _bleService.addLog('【焊口编号服务】用户ID发送失败');
      }

      return success;
    } catch (e) {
      _bleService.addLog('【焊口编号服务】发送用户ID异常: $e');
      return false;
    }
  }

  /// 写入连接指示位到焊机，通知焊机可以进行焊接
  /// VW2250置1操作：01 06 00 7D 00 01 D8 12
  Future<bool> writeWeldingStartSignal() async {
    try {
      if (!_bleService.isConnected) {
        _bleService.addLog('【焊口编号服务】错误：蓝牙未连接，无法发送焊接开始信号');
        return false;
      }

      // 构建Modbus写单个寄存器命令：01 06 00 7D 00 01 D8 12
      // VW2250 = 0x007D (十六进制)
      // 写入值：1
      List<int> command = [
        0x01, // 从站地址
        0x06, // 功能码：写单个保持寄存器
        0x00, 0x7D, // 寄存器地址 VW2250 (0x007D)
        0x00, 0x01, // 写入的值：1
        0xD8, 0x12, // CRC校验码
      ];

      String commandHex = command
          .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
          .join(' ');
      _bleService.addLog('【焊口编号服务】发送焊接开始信号: $commandHex');

      // 发送命令到焊机
      bool success = await _bleService.sendData(command);

      if (success) {
        _bleService.addLog('【焊口编号服务】焊接开始信号已发送成功，焊机可以开始焊接');
      } else {
        _bleService.addLog('【焊口编号服务】焊接开始信号发送失败');
      }

      return success;
    } catch (e) {
      _bleService.addLog('【焊口编号服务】发送焊接开始信号异常: $e');
      return false;
    }
  }

  /// 写入焊口编号到PLC指定寄存器
  /// 发送地址：01 10 00 69 00 06 0C XX...XX CRC
  Future<bool> writeJointNumberToPLC(String jointNumber) async {
    try {
      if (!_bleService.isConnected) {
        _bleService.addLog('【焊口编号服务】错误：蓝牙未连接，无法写入PLC');
        return false;
      }

      // 构建Modbus写入命令：01 10 00 69 00 06 0C XX...XX CRC
      // 寄存器地址：0x0069
      // 6个字 = 12字节数据
      List<int> command = await _buildModbusWriteCommand(jointNumber);

      if (command.isEmpty) {
        _bleService.addLog('【焊口编号服务】构建Modbus命令失败');
        return false;
      }

      // 发送命令到PLC
      bool success = await _bleService.sendData(command);

      if (success) {
        _bleService.addLog('【焊口编号服务】焊口编号 $jointNumber 已成功写入PLC寄存器 0x0069');
      } else {
        _bleService.addLog('【焊口编号服务】写入PLC失败');
      }

      return success;
    } catch (e) {
      _bleService.addLog('【焊口编号服务】写入PLC异常: $e');
      return false;
    }
  }

  /// 构建Modbus写入命令
  Future<List<int>> _buildModbusWriteCommand(String jointNumber) async {
    try {
      // 将焊口编号转换为12字节数据
      List<int> jointBytes = utf8.encode(jointNumber);

      // 确保是12字节，不足补0，超出截断
      const int requiredLength = 12;
      if (jointBytes.length < requiredLength) {
        jointBytes
            .addAll(List<int>.filled(requiredLength - jointBytes.length, 0));
      } else if (jointBytes.length > requiredLength) {
        jointBytes = jointBytes.sublist(0, requiredLength);
      }

      // 构建Modbus命令: 01 10 00 69 00 06 0C + 12字节数据
      List<int> command = [
        0x01, // 从站地址
        0x10, // 功能码：写入多个保持寄存器
        0x00, 0x69, // 起始地址 (0x0069)
        0x00, 0x06, // 寄存器数量：6个字
        0x0C, // 字节数量：12字节
      ];

      // 添加数据
      command.addAll(jointBytes);

      // 计算并添加CRC校验
      int crc = _commandService.calculateCRC16Modbus(command);
      command.add(crc & 0xFF); // CRC低字节
      command.add((crc >> 8) & 0xFF); // CRC高字节

      String commandHex = command
          .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
          .join(' ');
      _bleService.addLog('【焊口编号服务】Modbus命令: $commandHex');

      return command;
    } catch (e) {
      _bleService.addLog('【焊口编号服务】构建Modbus命令异常: $e');
      return [];
    }
  }

  /// 设置焊接状态
  Future<void> _setWeldingStatus(String jointNumber, int status) async {
    final prefs = await SharedPreferences.getInstance();
    final statusMap = await _getWeldingStatusMap();
    statusMap[jointNumber] = status;
    await prefs.setString(_weldingStatusKey, json.encode(statusMap));
  }

  /// 获取焊接状态
  Future<int> getWeldingStatus(String jointNumber) async {
    final statusMap = await _getWeldingStatusMap();
    return statusMap[jointNumber] ?? 0;
  }

  /// 获取焊接状态映射
  Future<Map<String, int>> _getWeldingStatusMap() async {
    final prefs = await SharedPreferences.getInstance();
    final statusJson = prefs.getString(_weldingStatusKey) ?? '{}';
    try {
      final Map<String, dynamic> rawMap = json.decode(statusJson);
      return Map<String, int>.from(rawMap);
    } catch (e) {
      return <String, int>{};
    }
  }

  /// 标记当前焊口完成
  Future<void> _markCurrentJointCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_currentJointNumberKey);
  }

  /// 获取状态描述
  String getStatusDescription(int statusCode) {
    switch (statusCode) {
      case 0:
        return '未开始';
      case WELDING_SUCCESS:
        return '焊接成功';
      case HEAT_ABSORPTION_FAILED:
        return '吸热失败';
      case CURLING_FAILED:
        return '卷边失败';
      case COOLING_FAILED:
        return '冷却失败';
      default:
        return '未知状态';
    }
  }

  /// 获取今日已生成的焊口编号列表
  Future<List<String>> getTodayJointNumbers() async {
    try {
      final today = DateTime.now();
      final dateStr = '${today.year.toString().padLeft(4, '0')}'
          '${today.month.toString().padLeft(2, '0')}'
          '${today.day.toString().padLeft(2, '0')}';

      final statusMap = await _getWeldingStatusMap();
      return statusMap.keys
          .where((jointNumber) => jointNumber.startsWith(dateStr))
          .toList()
        ..sort();
    } catch (e) {
      _bleService.addLog('【焊口编号服务】获取今日焊口编号失败: $e');
      return [];
    }
  }

  /// 清理过期数据（保留30天）
  Future<void> cleanupOldData() async {
    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: 30));
      final cutoffStr = '${cutoffDate.year.toString().padLeft(4, '0')}'
          '${cutoffDate.month.toString().padLeft(2, '0')}'
          '${cutoffDate.day.toString().padLeft(2, '0')}';

      final statusMap = await _getWeldingStatusMap();
      final updatedMap = <String, int>{};

      for (var entry in statusMap.entries) {
        if (entry.key.length >= 8 &&
            entry.key.substring(0, 8).compareTo(cutoffStr) >= 0) {
          updatedMap[entry.key] = entry.value;
        }
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_weldingStatusKey, json.encode(updatedMap));

      _bleService.addLog('【焊口编号服务】数据清理完成，保留 ${updatedMap.length} 条记录');
    } catch (e) {
      _bleService.addLog('【焊口编号服务】数据清理失败: $e');
    }
  }

  /// 写入经度到焊机
  /// 寄存器地址：0x015E，4个字，8字节数据
  /// 指令格式：01 10 01 5E 00 04 08 XX...XX CRC
  Future<bool> writeLongitudeToWeldingMachine(double longitude) async {
    try {
      print('🌐 开始写入经度到焊机: $longitude');

      // 转换经度为8字节数据（降低精度版本）
      List<int> longitudeBytes =
          _locationServiceInstance.convertCoordinateToBytes(longitude);
      print(
          '🔢 经度转换为字节: ${longitudeBytes.map((b) => '0x${b.toRadixString(16).padLeft(2, '0').toUpperCase()}').join(' ')}');

      // 构建Modbus指令
      List<int> command = [
        0x01, // 设备地址
        0x10, // 功能码：写多个寄存器
        0x01, 0x5E, // 寄存器地址：0x015E
        0x00, 0x04, // 寄存器数量：4个字
        0x08, // 字节数：8字节
      ];

      // 添加经度数据
      command.addAll(longitudeBytes);

      // 计算CRC校验
      int crc = _commandService.calculateCRC16Modbus(command);
      command.add(crc & 0xFF);
      command.add((crc >> 8) & 0xFF);

      print(
          '📡 经度Modbus指令: ${command.map((b) => '0x${b.toRadixString(16).padLeft(2, '0').toUpperCase()}').join(' ')}');

      // 发送指令
      bool success = await _bleService.sendData(command);

      if (success) {
        print('✅ 经度写入蓝牙发送成功');
        return true;
      } else {
        print('❌ 经度写入蓝牙发送失败');
        return false;
      }
    } catch (e) {
      print('💥 写入经度到焊机异常: $e');
      return false;
    }
  }

  /// 写入纬度到焊机
  /// 寄存器地址：0x016D，4个字，8字节数据
  /// 指令格式：01 10 01 6D 00 04 08 XX...XX CRC
  Future<bool> writeLatitudeToWeldingMachine(double latitude) async {
    try {
      print('🗺️ 开始写入纬度到焊机: $latitude');

      // 转换纬度为8字节数据（降低精度版本）
      List<int> latitudeBytes =
          _locationServiceInstance.convertCoordinateToBytes(latitude);
      print(
          '🔢 纬度转换为字节: ${latitudeBytes.map((b) => '0x${b.toRadixString(16).padLeft(2, '0').toUpperCase()}').join(' ')}');

      // 构建Modbus指令
      List<int> command = [
        0x01, // 设备地址
        0x10, // 功能码：写多个寄存器
        0x01, 0x6D, // 寄存器地址：0x016D
        0x00, 0x04, // 寄存器数量：4个字
        0x08, // 字节数：8字节
      ];

      // 添加纬度数据
      command.addAll(latitudeBytes);

      // 计算CRC校验
      int crc = _commandService.calculateCRC16Modbus(command);
      command.add(crc & 0xFF);
      command.add((crc >> 8) & 0xFF);

      print(
          '📡 纬度Modbus指令: ${command.map((b) => '0x${b.toRadixString(16).padLeft(2, '0').toUpperCase()}').join(' ')}');

      // 发送指令
      bool success = await _bleService.sendData(command);

      if (success) {
        print('✅ 纬度写入蓝牙发送成功');
        return true;
      } else {
        print('❌ 纬度写入蓝牙发送失败');
        return false;
      }
    } catch (e) {
      print('💥 写入纬度到焊机异常: $e');
      return false;
    }
  }

  /// 写入海拔到焊机
  /// 寄存器地址：0x0168，1个字，2字节数据
  /// 指令格式：01 06 01 68 XX XX CRC
  Future<bool> writeAltitudeToWeldingMachine(double altitude) async {
    try {
      print('⛰️ 开始写入海拔到焊机: $altitude');

      // 转换海拔为2字节数据
      List<int> altitudeBytes =
          _locationServiceInstance.convertAltitudeToBytes(altitude);
      print(
          '🔢 海拔转换为字节: ${altitudeBytes.map((b) => '0x${b.toRadixString(16).padLeft(2, '0').toUpperCase()}').join(' ')}');

      // 构建Modbus指令
      List<int> command = [
        0x01, // 设备地址
        0x06, // 功能码：写单个寄存器
        0x01, 0x68, // 寄存器地址：0x0168
      ];

      // 添加海拔数据（2字节）
      command.addAll(altitudeBytes);

      // 计算CRC校验
      int crc = _commandService.calculateCRC16Modbus(command);
      command.add(crc & 0xFF);
      command.add((crc >> 8) & 0xFF);

      print(
          '📡 海拔Modbus指令: ${command.map((b) => '0x${b.toRadixString(16).padLeft(2, '0').toUpperCase()}').join(' ')}');

      // 发送指令
      bool success = await _bleService.sendData(command);

      if (success) {
        print('✅ 海拔写入蓝牙发送成功');
        return true;
      } else {
        print('❌ 海拔写入蓝牙发送失败');
        return false;
      }
    } catch (e) {
      print('💥 写入海拔到焊机异常: $e');
      return false;
    }
  }

  /// 获取位置信息并写入焊机
  Future<bool> writeLocationToWeldingMachine() async {
    try {
      print('🌍 开始获取位置信息并写入焊机...');

      // 使用智能获取策略获取位置
      LocationData? location =
          await _locationServiceInstance.getLocationWithFallback();
      if (location == null) {
        print('❌ 获取位置信息失败（所有方案都尝试过了）');
        return false;
      }

      print(
          '✅ 位置信息获取成功：${_locationServiceInstance.formatLocationData(location)}');
      print('📤 准备发送位置数据到PLC...');

      // 写入经度
      print('📍 正在发送经度: ${location.longitude}');
      bool longitudeSuccess =
          await writeLongitudeToWeldingMachine(location.longitude);
      if (!longitudeSuccess) {
        print('❌ 写入经度失败');
        return false;
      }
      print('✅ 经度发送成功');

      // 等待一段时间后写入纬度
      await Future.delayed(Duration(milliseconds: 500));
      print('📍 正在发送纬度: ${location.latitude}');
      bool latitudeSuccess =
          await writeLatitudeToWeldingMachine(location.latitude);
      if (!latitudeSuccess) {
        print('❌ 写入纬度失败');
        return false;
      }
      print('✅ 纬度发送成功');

      // 等待一段时间后写入海拔
      await Future.delayed(Duration(milliseconds: 500));
      print('📍 正在发送海拔: ${location.altitude}');
      bool altitudeSuccess =
          await writeAltitudeToWeldingMachine(location.altitude);
      if (!altitudeSuccess) {
        print('❌ 写入海拔失败');
        return false;
      }
      print('✅ 海拔发送成功');

      print('🎉 位置信息写入焊机完全成功！');
      print('📋 发送摘要:');
      print('   经度: ${location.longitude} → VW2700 (0x015E)');
      print('   纬度: ${location.latitude} → VW2710 (0x016D)');
      print('   海拔: ${location.altitude} → VW2720 (0x0168)');
      return true;
    } catch (e) {
      print('💥 写入位置信息到焊机失败: $e');
      return false;
    }
  }

  /// 写入联网上传成功信号到焊机
  /// VW3202=1，寄存器地址：0x0259
  /// 指令格式：01 06 02 59 00 01 99 A1
  Future<bool> writeOnlineUploadSuccessSignal() async {
    try {
      print('开始写入联网上传成功信号到焊机...');

      // 构建Modbus指令
      List<int> command = [
        0x01, // 设备地址
        0x06, // 功能码：写单个寄存器
        0x02, 0x59, // 寄存器地址：0x0259 (VW3202)
        0x00, 0x01, // 寄存器值：1
      ];

      // 计算CRC校验
      int crc = _commandService.calculateCRC16Modbus(command);
      command.add(crc & 0xFF);
      command.add((crc >> 8) & 0xFF);

      print(
          '联网上传成功信号指令: ${command.map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase()).join(' ')}');

      // 发送指令
      bool success = await _bleService.sendData(command);

      if (success) {
        print('联网上传成功信号写入成功');
        return true;
      } else {
        print('联网上传成功信号写入失败');
        return false;
      }
    } catch (e) {
      print('写入联网上传成功信号失败: $e');
      return false;
    }
  }

  /// 写入断网存储成功信号到焊机
  /// VW3204=1，寄存器地址：0x025A
  /// 指令格式：01 06 02 5A 00 01 69 A1
  Future<bool> writeOfflineStorageSuccessSignal() async {
    try {
      print('开始写入断网存储成功信号到焊机...');

      // 构建Modbus指令
      List<int> command = [
        0x01, // 设备地址
        0x06, // 功能码：写单个寄存器
        0x02, 0x5A, // 寄存器地址：0x025A (VW3204)
        0x00, 0x01, // 寄存器值：1
      ];

      // 计算CRC校验
      int crc = _commandService.calculateCRC16Modbus(command);
      command.add(crc & 0xFF);
      command.add((crc >> 8) & 0xFF);

      print(
          '断网存储成功信号指令: ${command.map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase()).join(' ')}');

      // 发送指令
      bool success = await _bleService.sendData(command);

      if (success) {
        print('断网存储成功信号写入成功');
        return true;
      } else {
        print('断网存储成功信号写入失败');
        return false;
      }
    } catch (e) {
      print('写入断网存储成功信号失败: $e');
      return false;
    }
  }

  /// 检查网络连接状态
  Future<bool> isNetworkConnected() async {
    try {
      // 这里可以使用connectivity_plus包来检查网络状态
      // 暂时使用简单的HTTP请求来检测网络
      final response = await http.get(Uri.parse('https://www.baidu.com'),
          headers: {'User-Agent': 'Mozilla/5.0'}).timeout(Duration(seconds: 5));

      return response.statusCode == 200;
    } catch (e) {
      print('网络连接检查失败: $e');
      return false;
    }
  }

  /// 模拟上传焊接数据到服务器
  Future<bool> uploadWeldingDataToServer(String jointNumber) async {
    try {
      print('开始上传焊接数据到服务器: $jointNumber');

      // 获取焊接状态
      int status = await getWeldingStatus(jointNumber);
      if (status != WELDING_SUCCESS) {
        print('焊接未成功，不允许上传');
        return false;
      }

      // 模拟数据上传过程
      await Future.delayed(Duration(seconds: 2));

      // 这里应该调用实际的API接口上传数据
      // 包括：焊口编号、焊接时间、焊接状态、位置信息等
      Map<String, dynamic> uploadData = {
        'jointNumber': jointNumber,
        'status': status,
        'timestamp': DateTime.now().toIso8601String(),
        'uploadTime': DateTime.now().toIso8601String(),
      };

      print('模拟上传数据: $uploadData');

      // 模拟上传成功
      return true;
    } catch (e) {
      print('上传焊接数据失败: $e');
      return false;
    }
  }

  /// 处理焊接数据上传流程
  Future<bool> handleWeldingDataUpload(String jointNumber) async {
    try {
      print('开始处理焊接数据上传流程: $jointNumber');

      // 1. 检查网络状态
      bool isConnected = await isNetworkConnected();
      print('网络状态: ${isConnected ? "已连接" : "未连接"}');

      if (isConnected) {
        // 2a. 联网模式：上传到服务器
        bool uploadSuccess = await uploadWeldingDataToServer(jointNumber);
        if (uploadSuccess) {
          // 上传成功，写入联网上传成功信号
          bool signalSuccess = await writeOnlineUploadSuccessSignal();
          if (signalSuccess) {
            print('联网上传流程完成');
            return true;
          } else {
            print('联网上传成功但信号写入失败');
            return false;
          }
        } else {
          print('联网上传失败');
          return false;
        }
      } else {
        // 2b. 断网模式：本地存储
        print('网络不可用，使用断网存储模式');

        // 保存到本地离线存储
        final weldingData = OfflineWeldingData(
          id: jointNumber,
          projectId: 'offline_project', // 离线模式项目ID
          userId: 'offline_user', // 离线模式用户ID
          deviceId: 'welding_machine_01', // 焊机设备ID
          weldingParams: {
            'jointNumber': jointNumber,
            'status': await getWeldingStatus(jointNumber),
            'mode': 'offline',
          },
          weldingDataHex: '', // 这里可以后续从焊机读取实际数据
          timestamp: DateTime.now(),
          imagePaths: [], // 暂时没有图片
          isUploaded: false,
        );
        await _offlineStorageService.saveWeldingData(weldingData);

        // 写入断网存储成功信号
        bool signalSuccess = await writeOfflineStorageSuccessSignal();
        if (signalSuccess) {
          print('断网存储流程完成');
          return true;
        } else {
          print('断网存储成功但信号写入失败');
          return false;
        }
      }
    } catch (e) {
      print('处理焊接数据上传流程失败: $e');
      return false;
    }
  }
}
