import 'dart:convert';
import 'package:dio/dio.dart';
import '../models/project_model.dart';
import 'user_service.dart';
import 'bluetooth_service.dart';

class ProjectService {
  static final ProjectService _instance = ProjectService._internal();
  static const String API_BASE_URL = 'http://121.40.60.17/apk';

  final UserService _userService = UserService();
  final BleService _bleService = BleService();
  final Dio _dio = Dio();

  factory ProjectService() => _instance;

  ProjectService._internal();

  // 获取用户的项目列表
  Future<List<Project>> getUserProjects() async {
    try {
      var headers = await _userService.getFormApiHeaders();

      var response = await _dio.get(
        '$API_BASE_URL/ugp/project-resource/selectByUserId',
        options: Options(
          headers: headers,
          responseType: ResponseType.json,
        ),
      );

      if (response.statusCode == 200 && response.data['status'] == 200) {
        List<dynamic> projectsJson = response.data['result'] ?? [];
        return projectsJson.map((json) => Project.fromJson(json)).toList();
      } else {
        _bleService.addLog('【项目服务】加载项目失败: ${response.statusMessage}');
        throw Exception('项目加载失败: ${response.data['message'] ?? '未知错误'}');
      }
    } catch (e) {
      _bleService.addLog('【项目服务】加载项目错误: $e');
      throw Exception('项目加载错误: $e');
    }
  }

  // 获取项目详情
  Future<Project> getProjectDetails(String projectId) async {
    try {
      var headers = await _userService.getFormApiHeaders();

      var response = await _dio.get(
        '$API_BASE_URL/ugp/project-resource/$projectId',
        options: Options(
          headers: headers,
          responseType: ResponseType.json,
        ),
      );

      if (response.statusCode == 200 && response.data['status'] == 200) {
        return Project.fromJson(response.data['result']);
      } else {
        throw Exception('获取项目详情失败: ${response.data['message'] ?? '未知错误'}');
      }
    } catch (e) {
      _bleService.addLog('【项目服务】获取项目详情错误: $e');
      throw Exception('获取项目详情错误: $e');
    }
  }

  // 保存当前项目
  Future<void> saveCurrentProject(Project project) async {
    _bleService.addLog('【项目服务】保存当前项目: ${project.name}');
    await _userService.saveCurrentProject(project.toJson());
  }

  // 获取当前项目
  Future<Project?> getCurrentProject() async {
    Map<String, dynamic>? projectJson = await _userService.getCurrentProject();
    if (projectJson != null) {
      Project project = Project.fromJson(projectJson);
      _bleService.addLog('【项目服务】获取当前项目: ${project.name}');
      return project;
    }
    _bleService.addLog('【项目服务】无当前项目');
    return null;
  }

  // 获取缓存的项目列表
  Future<List<Project>> getCachedProjects() async {
    try {
      // 这里应该从本地缓存中获取项目列表
      // 在实际实现中，可能需要从SQLite数据库或文件中读取

      // 为了示例，我们先尝试从SharedPreferences中获取当前项目
      final currentProject = await getCurrentProject();

      // 返回包含当前项目的列表（如果有的话）
      return currentProject != null ? [currentProject] : [];
    } catch (e) {
      _bleService.addLog('【项目服务】获取缓存项目失败: $e');
      return [];
    }
  }

  // 通过项目ID获取焊口列表
  Future<List<Map<String, dynamic>>> getWeldJoints(String projectId) async {
    try {
      var headers = await _userService.getFormApiHeaders();

      var response = await _dio.get(
        '$API_BASE_URL/ugp/project-resource/$projectId/welding-joints',
        options: Options(
          headers: headers,
          responseType: ResponseType.json,
        ),
      );

      if (response.statusCode == 200 && response.data['status'] == 200) {
        List<dynamic> jointsJson = response.data['result'] ?? [];
        return jointsJson.cast<Map<String, dynamic>>();
      } else {
        throw Exception('获取焊口列表失败: ${response.data['message'] ?? '未知错误'}');
      }
    } catch (e) {
      _bleService.addLog('【项目服务】获取焊口列表错误: $e');
      throw Exception('获取焊口列表错误: $e');
    }
  }

  // 通过项目ID获取设备列表
  Future<List<Map<String, dynamic>>> getDevices(String projectId) async {
    try {
      var headers = await _userService.getFormApiHeaders();

      var response = await _dio.get(
        '$API_BASE_URL/ugp/project-resource/$projectId/devices',
        options: Options(
          headers: headers,
          responseType: ResponseType.json,
        ),
      );

      if (response.statusCode == 200 && response.data['status'] == 200) {
        List<dynamic> devicesJson = response.data['result'] ?? [];
        return devicesJson.cast<Map<String, dynamic>>();
      } else {
        throw Exception('获取设备列表失败: ${response.data['message'] ?? '未知错误'}');
      }
    } catch (e) {
      _bleService.addLog('【项目服务】获取设备列表错误: $e');
      throw Exception('获取设备列表错误: $e');
    }
  }
}
