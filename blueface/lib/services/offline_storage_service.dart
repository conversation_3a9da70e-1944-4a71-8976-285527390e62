import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/offline_data_model.dart';

class OfflineStorageService {
  static final OfflineStorageService _instance =
      OfflineStorageService._internal();
  factory OfflineStorageService() => _instance;
  OfflineStorageService._internal();

  static const String _offlineDataKey = 'offline_data';
  static const String _offlineModeStateKey = 'offline_mode_state';
  static const String _weldingDataKey = 'welding_data';

  Directory? _applicationDirectory;

  // 初始化
  Future<void> initialize() async {
    _applicationDirectory = await getApplicationDocumentsDirectory();
    await _createDirectories();
  }

  // 创建必要的目录
  Future<void> _createDirectories() async {
    if (_applicationDirectory == null) return;

    final dirs = ['images', 'face', 'device', 'pipe', 'welding'];
    for (final dir in dirs) {
      final directory = Directory('${_applicationDirectory!.path}/$dir');
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
    }
  }

  // 获取离线模式状态
  Future<OfflineModeState> getOfflineModeState() async {
    final prefs = await SharedPreferences.getInstance();
    final stateJson = prefs.getString(_offlineModeStateKey);

    if (stateJson != null) {
      try {
        final Map<String, dynamic> data = json.decode(stateJson);
        return OfflineModeState.fromJson(data);
      } catch (e) {
        print('解析离线模式状态失败: $e');
      }
    }

    return OfflineModeState(
      isOfflineMode: false,
      pendingUploads: 0,
    );
  }

  // 保存离线模式状态
  Future<void> saveOfflineModeState(OfflineModeState state) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_offlineModeStateKey, json.encode(state.toJson()));
  }

  // 保存离线数据
  Future<String> saveOfflineData(OfflineDataModel data) async {
    final prefs = await SharedPreferences.getInstance();

    // 获取现有数据
    final existingDataJson = prefs.getString(_offlineDataKey) ?? '[]';
    final List<dynamic> existingData = json.decode(existingDataJson);

    // 添加新数据
    existingData.add(data.toJson());

    // 保存
    await prefs.setString(_offlineDataKey, json.encode(existingData));

    // 更新离线模式状态
    final state = await getOfflineModeState();
    await saveOfflineModeState(state.copyWith(
      pendingUploads: existingData.where((item) => !item['isUploaded']).length,
    ));

    return data.id;
  }

  // 获取所有离线数据
  Future<List<OfflineDataModel>> getAllOfflineData() async {
    final prefs = await SharedPreferences.getInstance();
    final dataJson = prefs.getString(_offlineDataKey) ?? '[]';

    try {
      final List<dynamic> dataList = json.decode(dataJson);
      return dataList.map((item) => OfflineDataModel.fromJson(item)).toList();
    } catch (e) {
      print('获取离线数据失败: $e');
      return [];
    }
  }

  // 获取未上传的离线数据
  Future<List<OfflineDataModel>> getPendingUploadData() async {
    final allData = await getAllOfflineData();
    return allData.where((item) => !item.isUploaded).toList();
  }

  // 标记数据为已上传
  Future<void> markDataAsUploaded(String dataId) async {
    final prefs = await SharedPreferences.getInstance();
    final dataJson = prefs.getString(_offlineDataKey) ?? '[]';

    try {
      final List<dynamic> dataList = json.decode(dataJson);

      for (int i = 0; i < dataList.length; i++) {
        if (dataList[i]['id'] == dataId) {
          dataList[i]['isUploaded'] = true;
          dataList[i]['uploadedAt'] = DateTime.now().toIso8601String();
          break;
        }
      }

      await prefs.setString(_offlineDataKey, json.encode(dataList));

      // 更新待上传计数
      final pendingCount = dataList.where((item) => !item['isUploaded']).length;
      final state = await getOfflineModeState();
      await saveOfflineModeState(state.copyWith(
        pendingUploads: pendingCount,
      ));
    } catch (e) {
      print('标记数据为已上传失败: $e');
    }
  }

  // 保存图片文件
  Future<String> saveImage(
      String base64Image, String type, String fileName) async {
    if (_applicationDirectory == null) {
      throw Exception('应用目录未初始化');
    }

    final imageBytes = base64Decode(base64Image);
    final filePath = '${_applicationDirectory!.path}/images/$type/$fileName';
    final file = File(filePath);

    // 确保目录存在
    await file.parent.create(recursive: true);

    // 保存文件
    await file.writeAsBytes(imageBytes);

    return filePath;
  }

  // 读取图片文件
  Future<String?> getImageBase64(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        final bytes = await file.readAsBytes();
        return base64Encode(bytes);
      }
    } catch (e) {
      print('读取图片失败: $e');
    }
    return null;
  }

  // 保存焊接数据
  Future<void> saveWeldingData(OfflineWeldingData weldingData) async {
    final prefs = await SharedPreferences.getInstance();

    // 获取现有焊接数据
    final existingDataJson = prefs.getString(_weldingDataKey) ?? '[]';
    final List<dynamic> existingData = json.decode(existingDataJson);

    // 添加新数据
    existingData.add(weldingData.toJson());

    // 保存
    await prefs.setString(_weldingDataKey, json.encode(existingData));
  }

  // 获取所有焊接数据
  Future<List<OfflineWeldingData>> getAllWeldingData() async {
    final prefs = await SharedPreferences.getInstance();
    final dataJson = prefs.getString(_weldingDataKey) ?? '[]';

    try {
      final List<dynamic> dataList = json.decode(dataJson);
      return dataList.map((item) => OfflineWeldingData.fromJson(item)).toList();
    } catch (e) {
      print('获取焊接数据失败: $e');
      return [];
    }
  }

  // 获取未上传的焊接数据
  Future<List<OfflineWeldingData>> getPendingWeldingData() async {
    final allData = await getAllWeldingData();
    return allData.where((item) => !item.isUploaded).toList();
  }

  // 标记焊接数据为已上传
  Future<void> markWeldingDataAsUploaded(String dataId) async {
    final prefs = await SharedPreferences.getInstance();
    final dataJson = prefs.getString(_weldingDataKey) ?? '[]';

    try {
      final List<dynamic> dataList = json.decode(dataJson);

      for (int i = 0; i < dataList.length; i++) {
        if (dataList[i]['id'] == dataId) {
          dataList[i]['isUploaded'] = true;
          break;
        }
      }

      await prefs.setString(_weldingDataKey, json.encode(dataList));
    } catch (e) {
      print('标记焊接数据为已上传失败: $e');
    }
  }

  // 清理已上传的数据
  Future<void> cleanupUploadedData() async {
    final prefs = await SharedPreferences.getInstance();

    // 清理离线数据
    final dataJson = prefs.getString(_offlineDataKey) ?? '[]';
    final List<dynamic> dataList = json.decode(dataJson);
    final pendingData = dataList.where((item) => !item['isUploaded']).toList();
    await prefs.setString(_offlineDataKey, json.encode(pendingData));

    // 清理焊接数据
    final weldingJson = prefs.getString(_weldingDataKey) ?? '[]';
    final List<dynamic> weldingList = json.decode(weldingJson);
    final pendingWelding =
        weldingList.where((item) => !item['isUploaded']).toList();
    await prefs.setString(_weldingDataKey, json.encode(pendingWelding));

    // 更新状态
    final state = await getOfflineModeState();
    await saveOfflineModeState(state.copyWith(
      pendingUploads: pendingData.length + pendingWelding.length,
      lastSyncTime: DateTime.now(),
    ));
  }

  // 获取存储状态统计
  Future<Map<String, int>> getStorageStats() async {
    final offlineData = await getAllOfflineData();
    final weldingData = await getAllWeldingData();

    return {
      'totalOfflineData': offlineData.length,
      'pendingOfflineData':
          offlineData.where((item) => !item.isUploaded).length,
      'totalWeldingData': weldingData.length,
      'pendingWeldingData':
          weldingData.where((item) => !item.isUploaded).length,
    };
  }
}
