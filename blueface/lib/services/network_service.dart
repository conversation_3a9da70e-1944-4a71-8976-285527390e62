import 'dart:async';
import 'dart:io';

class NetworkService {
  static final NetworkService _instance = NetworkService._internal();
  factory NetworkService() => _instance;
  NetworkService._internal();

  late StreamController<bool> _networkStatusController;
  late Stream<bool> networkStatusStream;

  bool _isOnline = false;
  bool get isOnline => _isOnline;
  Timer? _periodicCheck;

  // 初始化网络监听
  Future<void> initialize() async {
    _networkStatusController = StreamController<bool>.broadcast();
    networkStatusStream = _networkStatusController.stream;

    // 初始检查网络状态
    await checkNetworkStatus();

    // 每30秒检查一次网络状态
    _periodicCheck = Timer.periodic(Duration(seconds: 30), (timer) {
      checkNetworkStatus();
    });
  }

  // 检查实际的互联网访问能力
  Future<bool> _checkInternetAccess() async {
    try {
      // 尝试访问可靠的服务器来验证网络连接
      final result = await InternetAddress.lookup('www.baidu.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      print('网络连接检查失败: $e');
      return false;
    }
  }

  // 更新网络状态并通知监听者
  void _updateNetworkStatus(bool isOnline) {
    if (_isOnline != isOnline) {
      _isOnline = isOnline;
      _networkStatusController.add(isOnline);
      print('网络状态变化: ${isOnline ? "在线" : "离线"}');
    }
  }

  // 手动检查网络状态
  Future<bool> checkNetworkStatus() async {
    try {
      final hasInternet = await _checkInternetAccess();
      _updateNetworkStatus(hasInternet);
      return hasInternet;
    } catch (e) {
      print('检查网络状态失败: $e');
      _updateNetworkStatus(false);
      return false;
    }
  }

  // 获取网络连接类型（简化版本）
  Future<String> getConnectionType() async {
    try {
      if (await _checkInternetAccess()) {
        return '网络已连接';
      } else {
        return '无网络';
      }
    } catch (e) {
      print('获取连接类型失败: $e');
      return '未知';
    }
  }

  // 销毁资源
  void dispose() {
    _periodicCheck?.cancel();
    _networkStatusController.close();
  }
}
