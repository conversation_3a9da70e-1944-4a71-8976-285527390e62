import 'dart:convert';
import 'dart:typed_data';
import 'package:gbk_codec/gbk_codec.dart';
import 'bluetooth_service.dart';
import '../models/project_model.dart';

class CommandService {
  static final CommandService _instance = CommandService._internal();
  final BleService _bleService = BleService();

  factory CommandService() => _instance;

  CommandService._internal();

  // 构建项目地址命令
  List<int> buildProjectAddressCommand(String address) {
    try {
      _bleService.addLog('【命令服务】构建项目地址命令:');
      _bleService.addLog('【命令服务】原始项目地址: $address');

      // 1. 将字符串转换为GBK编码的字节
      List<int> gbkBytes = gbk.encode(address);
      _bleService.addLog('【命令服务】GBK编码后字节长度: ${gbkBytes.length}');
      _bleService.addLog(
          '【命令服务】GBK编码内容: ${gbkBytes.map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase()).join(' ')}');

      // 2. 检查长度和填充
      const int REQUIRED_LENGTH = 32; // 固定长度
      List<int> data = List<int>.from(gbkBytes);

      // 3. 如果长度不足，进行填充
      if (data.length < REQUIRED_LENGTH) {
        _bleService.addLog(
            '【命令服务】数据需要填充，当前长度: ${data.length}, 需要长度: $REQUIRED_LENGTH');
        // 使用0填充到指定长度
        data = data + List<int>.filled(REQUIRED_LENGTH - data.length, 0);
      }
      // 4. 如果超过长度，需要截断
      else if (data.length > REQUIRED_LENGTH) {
        _bleService.addLog(
            '【命令服务】数据超出长度限制，需要截断，当前长度: ${data.length}, 限制长度: $REQUIRED_LENGTH');
        data = data.sublist(0, REQUIRED_LENGTH);
      }

      _bleService.addLog('【命令服务】处理后数据长度: ${data.length}');

      // 5. 构建最终命令
      List<int> finalCommand = [];

      // 命令头 0x82 (项目地址命令)
      finalCommand.add(0x82);

      // 添加数据
      finalCommand.addAll(data);

      // 计算CRC校验码并添加到命令尾部
      int crc = calculateCRC16Modbus(finalCommand);
      _bleService.addLog(
          '【命令服务】CRC校验码: 0x${crc.toRadixString(16).padLeft(4, '0').toUpperCase()}');

      // 低字节在前，高字节在后
      finalCommand.add(crc & 0xFF);
      finalCommand.add((crc >> 8) & 0xFF);

      // 打印最终命令信息
      String commandHex = finalCommand
          .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
          .join(' ');
      _bleService.addLog("【命令服务】命令格式: $commandHex");
      _bleService.addLog("【命令服务】命令总长度: ${finalCommand.length}字节");

      // 无空格格式
      String noSpaceHex = finalCommand
          .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
          .join('');
      _bleService.addLog("【命令服务】发送的完整命令(无空格): $noSpaceHex");
      _bleService.addLog(
          "【命令服务】无空格命令长度: ${noSpaceHex.length}字符 = ${finalCommand.length}字节");

      return finalCommand;
    } catch (e) {
      _bleService.addLog('【命令服务】构建项目地址命令失败: $e');
      return [];
    }
  }

  // 构建项目编号命令
  List<int> buildProjectCodeCommand(String code) {
    try {
      _bleService.addLog('【命令服务】构建项目编号命令:');
      _bleService.addLog('【命令服务】原始项目编号: $code');

      // 1. 将字符串转换为GBK编码的字节
      List<int> gbkBytes = gbk.encode(code);
      _bleService.addLog('【命令服务】GBK编码后字节长度: ${gbkBytes.length}');
      _bleService.addLog(
          '【命令服务】GBK编码内容: ${gbkBytes.map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase()).join(' ')}');

      // 2. 检查长度和填充
      const int REQUIRED_LENGTH = 16; // 固定长度
      List<int> data = List<int>.from(gbkBytes);

      // 3. 如果长度不足，进行填充
      if (data.length < REQUIRED_LENGTH) {
        _bleService.addLog(
            '【命令服务】数据需要填充，当前长度: ${data.length}, 需要长度: $REQUIRED_LENGTH');
        // 使用0填充到指定长度
        data = data + List<int>.filled(REQUIRED_LENGTH - data.length, 0);
      }
      // 4. 如果超过长度，需要截断
      else if (data.length > REQUIRED_LENGTH) {
        _bleService.addLog(
            '【命令服务】数据超出长度限制，需要截断，当前长度: ${data.length}, 限制长度: $REQUIRED_LENGTH');
        data = data.sublist(0, REQUIRED_LENGTH);
      }

      _bleService.addLog('【命令服务】处理后数据长度: ${data.length}');

      // 5. 构建最终命令
      List<int> finalCommand = [];

      // 命令头 0x81 (项目编号命令)
      finalCommand.add(0x81);

      // 添加数据
      finalCommand.addAll(data);

      // 计算CRC校验码并添加到命令尾部
      int crc = calculateCRC16Modbus(finalCommand);
      _bleService.addLog(
          '【命令服务】CRC校验码: 0x${crc.toRadixString(16).padLeft(4, '0').toUpperCase()}');

      // 低字节在前，高字节在后
      finalCommand.add(crc & 0xFF);
      finalCommand.add((crc >> 8) & 0xFF);

      // 打印最终命令信息
      String commandHex = finalCommand
          .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
          .join(' ');
      _bleService.addLog("【命令服务】命令格式: $commandHex");
      _bleService.addLog("【命令服务】命令总长度: ${finalCommand.length}字节");

      return finalCommand;
    } catch (e) {
      _bleService.addLog('【命令服务】构建项目编号命令失败: $e');
      return [];
    }
  }

  // 构建项目名称命令
  List<int> buildProjectNameCommand(String name) {
    try {
      _bleService.addLog('【命令服务】构建项目名称命令:');
      _bleService.addLog('【命令服务】原始项目名称: $name');

      // 1. 将字符串转换为GBK编码的字节
      List<int> gbkBytes = gbk.encode(name);
      _bleService.addLog('【命令服务】GBK编码后字节长度: ${gbkBytes.length}');
      _bleService.addLog(
          '【命令服务】GBK编码内容: ${gbkBytes.map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase()).join(' ')}');

      // 2. 检查长度和填充
      const int REQUIRED_LENGTH = 32; // 固定长度
      List<int> data = List<int>.from(gbkBytes);

      // 3. 如果长度不足，进行填充
      if (data.length < REQUIRED_LENGTH) {
        _bleService.addLog(
            '【命令服务】数据需要填充，当前长度: ${data.length}, 需要长度: $REQUIRED_LENGTH');
        // 使用0填充到指定长度
        data = data + List<int>.filled(REQUIRED_LENGTH - data.length, 0);
      }
      // 4. 如果超过长度，需要截断
      else if (data.length > REQUIRED_LENGTH) {
        _bleService.addLog(
            '【命令服务】数据超出长度限制，需要截断，当前长度: ${data.length}, 限制长度: $REQUIRED_LENGTH');
        data = data.sublist(0, REQUIRED_LENGTH);
      }

      _bleService.addLog('【命令服务】处理后数据长度: ${data.length}');

      // 5. 构建最终命令
      List<int> finalCommand = [];

      // 命令头 0x83 (项目名称命令)
      finalCommand.add(0x83);

      // 添加数据
      finalCommand.addAll(data);

      // 计算CRC校验码并添加到命令尾部
      int crc = calculateCRC16Modbus(finalCommand);
      _bleService.addLog(
          '【命令服务】CRC校验码: 0x${crc.toRadixString(16).padLeft(4, '0').toUpperCase()}');

      // 低字节在前，高字节在后
      finalCommand.add(crc & 0xFF);
      finalCommand.add((crc >> 8) & 0xFF);

      // 打印最终命令信息
      String commandHex = finalCommand
          .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
          .join(' ');
      _bleService.addLog("【命令服务】命令格式: $commandHex");
      _bleService.addLog("【命令服务】命令总长度: ${finalCommand.length}字节");

      return finalCommand;
    } catch (e) {
      _bleService.addLog('【命令服务】构建项目名称命令失败: $e');
      return [];
    }
  }

  // 构建焊口编号命令
  List<int> buildWeldingJointCommand(String jointCode) {
    try {
      _bleService.addLog('【命令服务】构建焊口编号命令:');
      _bleService.addLog('【命令服务】原始焊口编号: $jointCode');

      // 1. 将字符串转换为GBK编码的字节
      List<int> gbkBytes = gbk.encode(jointCode);
      _bleService.addLog('【命令服务】GBK编码后字节长度: ${gbkBytes.length}');
      _bleService.addLog(
          '【命令服务】GBK编码内容: ${gbkBytes.map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase()).join(' ')}');

      // 2. 检查长度和填充
      const int REQUIRED_LENGTH = 16; // 固定长度
      List<int> data = List<int>.from(gbkBytes);

      // 3. 如果长度不足，进行填充
      if (data.length < REQUIRED_LENGTH) {
        _bleService.addLog(
            '【命令服务】数据需要填充，当前长度: ${data.length}, 需要长度: $REQUIRED_LENGTH');
        // 使用0填充到指定长度
        data = data + List<int>.filled(REQUIRED_LENGTH - data.length, 0);
      }
      // 4. 如果超过长度，需要截断
      else if (data.length > REQUIRED_LENGTH) {
        _bleService.addLog(
            '【命令服务】数据超出长度限制，需要截断，当前长度: ${data.length}, 限制长度: $REQUIRED_LENGTH');
        data = data.sublist(0, REQUIRED_LENGTH);
      }

      _bleService.addLog('【命令服务】处理后数据长度: ${data.length}');

      // 5. 构建最终命令
      List<int> finalCommand = [];

      // 命令头 0x84 (焊口编号命令)
      finalCommand.add(0x84);

      // 添加数据
      finalCommand.addAll(data);

      // 计算CRC校验码并添加到命令尾部
      int crc = calculateCRC16Modbus(finalCommand);
      _bleService.addLog(
          '【命令服务】CRC校验码: 0x${crc.toRadixString(16).padLeft(4, '0').toUpperCase()}');

      // 低字节在前，高字节在后
      finalCommand.add(crc & 0xFF);
      finalCommand.add((crc >> 8) & 0xFF);

      // 打印最终命令信息
      String commandHex = finalCommand
          .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
          .join(' ');
      _bleService.addLog("【命令服务】命令格式: $commandHex");
      _bleService.addLog("【命令服务】命令总长度: ${finalCommand.length}字节");

      return finalCommand;
    } catch (e) {
      _bleService.addLog('【命令服务】构建焊口编号命令失败: $e');
      return [];
    }
  }

  // Modbus CRC16计算
  int calculateCRC16Modbus(List<int> data) {
    _bleService.addLog('【命令服务】计算CRC16:');
    _bleService.addLog('【命令服务】输入数据长度: ${data.length}');
    _bleService.addLog(
        '【命令服务】输入数据: ${data.map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase()).join(' ')}');

    int crc = 0xFFFF;
    for (int byte in data) {
      crc ^= byte;
      for (int i = 0; i < 8; i++) {
        if ((crc & 0x0001) != 0) {
          crc = (crc >> 1) ^ 0xA001;
        } else {
          crc = crc >> 1;
        }
      }
    }

    _bleService.addLog(
        '【命令服务】计算结果: 0x${crc.toRadixString(16).padLeft(4, '0').toUpperCase()}');
    return crc;
  }

  // 测试CRC16函数是否正确
  void testCRC16Calculation() {
    // 测试数据
    List<int> testData = [0x01, 0x03, 0x00, 0x00, 0x00, 0x0A];
    int calculatedCRC = calculateCRC16Modbus(testData);
    int expectedCRC = 0xC50B; // Modbus CRC16的期望值

    _bleService.addLog(
        '【命令服务】测试数据: ${testData.map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase()).join(' ')}');
    _bleService.addLog(
        '【命令服务】计算的CRC: 0x${calculatedCRC.toRadixString(16).padLeft(4, '0').toUpperCase()}');
    _bleService.addLog(
        '【命令服务】期望的CRC: 0x${expectedCRC.toRadixString(16).padLeft(4, '0').toUpperCase()}');
    _bleService
        .addLog('【命令服务】CRC计算结果: ${calculatedCRC == expectedCRC ? '正确' : '错误'}');

    // 如果不匹配，可能需要检查字节顺序
    if (calculatedCRC != expectedCRC) {
      int swappedCRC =
          ((calculatedCRC & 0xFF) << 8) | ((calculatedCRC >> 8) & 0xFF);
      _bleService.addLog(
          '【命令服务】字节顺序交换后: 0x${swappedCRC.toRadixString(16).padLeft(4, '0').toUpperCase()}');
      _bleService.addLog(
          '【命令服务】与期望值比较(交换后): ${swappedCRC == expectedCRC ? '正确' : '错误'}');
    }
  }

  // 解析十六进制字符串为字节数组
  List<int> hexStringToBytes(String hexString) {
    // 移除所有空格
    hexString = hexString.replaceAll(' ', '');

    List<int> bytes = [];
    for (int i = 0; i < hexString.length; i += 2) {
      if (i + 2 <= hexString.length) {
        String byteString = hexString.substring(i, i + 2);
        bytes.add(int.parse(byteString, radix: 16));
      }
    }

    return bytes;
  }

  // 将字节数组格式化为带空格的十六进制字符串
  String bytesToHexString(List<int> bytes, {bool withSpaces = true}) {
    String separator = withSpaces ? ' ' : '';

    // 确保每个值都在正确的字节范围内
    List<int> validBytes = [];
    for (int byte in bytes) {
      if (byte > 255) {
        // 处理可能的多字节值
        validBytes.add((byte >> 8) & 0xFF); // 高字节
        validBytes.add(byte & 0xFF); // 低字节
      } else {
        validBytes.add(byte & 0xFF); // 确保在0-255范围内
      }
    }

    // 记录输入与处理后的差异
    if (validBytes.length != bytes.length) {
      _bleService.addLog(
          '【命令服务】bytesToHexString: 注意 - 输入${bytes.length}字节，处理后${validBytes.length}字节');
    }

    return validBytes
        .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
        .join(separator);
  }

  // 解析命令并格式化显示
  String formatCommand(List<int> command, {bool showDetails = true}) {
    if (command.isEmpty) {
      return "空命令";
    }

    try {
      // 基本信息 - 命令类型和长度
      String result =
          "命令类型: 0x${command[0].toRadixString(16).padLeft(2, '0').toUpperCase()}, 长度: ${command.length}字节";

      if (!showDetails) {
        return result;
      }

      // 解析命令类型
      String commandType = "未知";
      switch (command[0]) {
        case 0x81:
          commandType = "项目编号";
          break;
        case 0x82:
          commandType = "项目地址";
          break;
        case 0x83:
          commandType = "项目名称";
          break;
        case 0x84:
          commandType = "焊口编号";
          break;
      }

      result += "\n命令含义: $commandType";

      // 命令内容
      if (command.length > 3) {
        // 至少有数据部分
        // 获取命令数据部分（去掉命令头和CRC）
        List<int> dataBytes = command.sublist(1, command.length - 2);

        // 尝试解码为文本
        String? decodedText;
        try {
          // 去掉结尾的0填充
          while (dataBytes.isNotEmpty && dataBytes.last == 0) {
            dataBytes.removeLast();
          }

          // 尝试GBK解码
          decodedText = gbk.decode(dataBytes);
        } catch (e) {
          decodedText = null;
        }

        // 十六进制显示
        String hexData = dataBytes
            .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
            .join(' ');

        result += "\n数据部分(HEX): $hexData";
        if (decodedText != null && decodedText.isNotEmpty) {
          result += "\n数据内容(文本): $decodedText";
        }

        // CRC部分
        if (command.length >= 2) {
          int crc = (command[command.length - 2] & 0xFF) |
              ((command[command.length - 1] & 0xFF) << 8);

          String crcHex = crc.toRadixString(16).padLeft(4, '0').toUpperCase();
          result += "\nCRC校验: 0x$crcHex (低字节在前)";
        }
      }

      return result;
    } catch (e) {
      return "命令解析错误: $e";
    }
  }

  // 打印项目命令详情到日志
  void logProjectCommands(
      String projectCode, String projectName, String projectAddress) {
    _bleService.addLog('【命令服务】准备构建项目信息命令');

    // 构建三个命令
    List<int> codeCommand = buildProjectCodeCommand(projectCode);
    List<int> nameCommand = buildProjectNameCommand(projectName);
    List<int> addressCommand = buildProjectAddressCommand(projectAddress);

    // 记录命令详情
    _bleService.addLog('【命令服务】===== 项目编号命令 =====');
    String codeDetails = formatCommand(codeCommand);
    _bleService.addLog(codeDetails);

    _bleService.addLog('【命令服务】===== 项目名称命令 =====');
    String nameDetails = formatCommand(nameCommand);
    _bleService.addLog(nameDetails);

    _bleService.addLog('【命令服务】===== 项目地址命令 =====');
    String addressDetails = formatCommand(addressCommand);
    _bleService.addLog(addressDetails);

    _bleService.addLog('【命令服务】项目命令构建完成');
  }

  // 构建Modbus命令：发送 01 06 00 03 00 01 B8 0A
  List<int> buildInitialCommand() {
    final List<int> command = [0x01, 0x06, 0x00, 0x03, 0x00, 0x01, 0xB8, 0x0A];
    _bleService.addLog('【命令服务】发送初始命令: 01 06 00 03 00 01 B8 0A');
    return command;
  }

  // 构建Modbus命令：发送 01 03 00 05 00 05 95 C8
  List<int> buildQueryMachineNumberCommand() {
    final List<int> command = [0x01, 0x03, 0x00, 0x05, 0x00, 0x05, 0x95, 0xC8];
    _bleService.addLog('【命令服务】发送查询焊机编号命令: 01 03 00 05 00 05 95 C8');
    return command;
  }

  // 构建Modbus命令：读取焊接标准 01 03 00 73 00 07 F5 D3
  List<int> buildQueryWeldingStandardCommand() {
    final List<int> command = [0x01, 0x03, 0x00, 0x73, 0x00, 0x07, 0xF5, 0xD3];
    _bleService.addLog('【命令服务】发送查询焊接标准命令: 01 03 00 73 00 07 F5 D3');
    return command;
  }

  // 构建Modbus命令：读取焊机机型 01 03 00 7B 00 01 F4 13
  List<int> buildQueryMachineTypeCommand() {
    final List<int> command = [0x01, 0x03, 0x00, 0x7B, 0x00, 0x01, 0xF4, 0x13];
    _bleService.addLog('【命令服务】发送查询焊机机型命令: 01 03 00 7B 00 01 F4 13');
    return command;
  }

  // 构建Modbus命令：读取油缸面积 01 03 00 7C 00 01 45 D2
  List<int> buildQueryCylinderAreaCommand() {
    final List<int> command = [0x01, 0x03, 0x00, 0x7C, 0x00, 0x01, 0x45, 0xD2];
    _bleService.addLog('【命令服务】发送查询油缸面积命令: 01 03 00 7C 00 01 45 D2');
    return command;
  }

  // 构建Modbus命令：写入连接指示位 01 06 00 03 00 01 B8 0A
  List<int> buildWriteConnectionStatusCommand() {
    final List<int> command = [0x01, 0x06, 0x00, 0x03, 0x00, 0x01, 0xB8, 0x0A];
    _bleService.addLog('【命令服务】发送写入连接指示位命令: 01 06 00 03 00 01 B8 0A');
    return command;
  }

  // 解析焊机编号响应: 01 03 0A XX XX XX XX XX XX XX XX XX XX CRC
  String parseMachineNumberResponse(List<int> response) {
    if (response.length < 13) {
      _bleService.addLog('【命令服务】焊机编号响应格式错误，长度不足');
      return '';
    }

    if (response[0] != 0x01 || response[1] != 0x03) {
      _bleService.addLog('【命令服务】焊机编号响应格式错误，功能码不匹配');
      return '';
    }

    // 提取数据部分（跳过头两个字节和最后两个CRC字节）
    List<int> dataBytes = response.sublist(3, response.length - 2);

    // 尝试将字节转换为十六进制字符串显示
    String machineNumber = dataBytes
        .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
        .join(' ');
    _bleService.addLog('【命令服务】解析到焊机编号: $machineNumber');

    return machineNumber;
  }

  // 构建发送用户ID的命令: 01 10 00 0F 00 05 0A XX...XX CRC
  List<int> buildUserIdCommand(String userId) {
    try {
      _bleService.addLog('【命令服务】构建用户ID命令:');
      _bleService.addLog('【命令服务】原始用户ID: $userId');

      // 将用户ID转换为字节数组
      // 根据需要可以使用不同的编码方式，这里假设是16进制字符串
      List<int> userIdBytes = [];
      if (userId.contains(" ")) {
        // 空格分隔的十六进制
        userIdBytes =
            userId.split(" ").map((hex) => int.parse(hex, radix: 16)).toList();
      } else {
        // 可能是GBK编码等其他格式，需根据实际情况调整
        userIdBytes = gbk.encode(userId);
      }

      // 构建Modbus命令
      List<int> command = [
        0x01, // 设备地址
        0x10, // 功能码：写多个寄存器
        0x00, 0x0F, // 起始寄存器地址
        0x00, 0x05, // 寄存器数量
        0x0A // 字节数 (后续数据的字节数)
      ];

      // 添加用户ID数据
      command.addAll(userIdBytes);

      // 计算并添加CRC
      int crc = calculateCRC16Modbus(command);
      command.add(crc & 0xFF); // CRC低字节
      command.add((crc >> 8) & 0xFF); // CRC高字节

      // 记录日志
      String commandHex = command
          .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
          .join(' ');
      _bleService.addLog('【命令服务】用户ID命令: $commandHex');

      return command;
    } catch (e) {
      _bleService.addLog('【命令服务】构建用户ID命令失败: $e');
      return [];
    }
  }

  // 智能GBK编码方法 - 通用计算式解决方案
  List<int> safeGbkEncode(String text) {
    try {
      _bleService.addLog('【命令服务】开始智能GBK编码: $text');

      // 第一级：尝试使用gbk_codec库并验证结果
      List<int> result = _tryGbkCodecLibrary(text);
      if (result.isNotEmpty) {
        _bleService.addLog('【命令服务】GBK库编码成功: ${text} -> ${result.length}字节');
        return result;
      }

      // 第二级：计算式GBK编码（基于Unicode码点计算）
      result = _calculateGbkEncoding(text);
      if (result.isNotEmpty) {
        _bleService.addLog('【命令服务】计算式GBK编码成功: ${text} -> ${result.length}字节');
        return result;
      }

      // 第三级：ASCII直接转换
      if (_isAsciiOnly(text)) {
        result = text.codeUnits;
        _bleService.addLog('【命令服务】ASCII编码: ${text} -> ${result.length}字节');
        return result;
      }

      // 第四级：编码失败
      _bleService.addLog('【命令服务】错误：所有GBK编码方法都失败了，文本: $text');
      return [];
    } catch (e) {
      _bleService.addLog('【命令服务】GBK编码异常: $e');
      // 编码失败，返回空数组
      return [];
    }
  }

  // 第一级：正确的GBK编码实现
  List<int> _tryGbkCodecLibrary(String text) {
    try {
      _bleService.addLog('【命令服务】开始正确的GBK编码: $text');

      // 直接实现正确的GBK编码映射
      List<int> result = [];

      for (int i = 0; i < text.length; i++) {
        String char = text[i];
        List<int> gbkBytes = _getCorrectGbkBytes(char);

        if (gbkBytes.isNotEmpty) {
          result.addAll(gbkBytes);
          _bleService.addLog(
              '【命令服务】字符 "$char" -> GBK: ${gbkBytes.map((b) => b.toRadixString(16).toUpperCase().padLeft(2, '0')).join(' ')}');
        } else {
          // 如果GBK编码失败，记录错误但不降级到UTF-8
          _bleService.addLog('【命令服务】错误：字符 "$char" GBK编码失败，跳过该字符');
        }
      }

      _bleService.addLog('【命令服务】GBK编码完成，总长度: ${result.length}字节');
      _bleService.addLog(
          '【命令服务】GBK编码结果: ${result.map((b) => b.toRadixString(16).toUpperCase().padLeft(2, '0')).join(' ')}');

      return result;
    } catch (e) {
      _bleService.addLog('【命令服务】GBK编码异常: $e');
      return [];
    }
  }

  // 获取正确的GBK字节编码 - 通用转换方法
  List<int> _getCorrectGbkBytes(String char) {
    try {
      // ASCII字符直接返回
      if (char.codeUnitAt(0) < 128) {
        return [char.codeUnitAt(0)];
      }

      // 使用gbk库编码单个字符
      var encoded = gbk.encode(char);
      List<int> result = [];

      for (var item in encoded) {
        int rawValue = (item is int) ? item : (item as num).toInt();

        // 处理可能的16位值
        if (rawValue > 255) {
          // 16位值需要拆分为两个字节
          int highByte = (rawValue >> 8) & 0xFF;
          int lowByte = rawValue & 0xFF;

          // GBK编码通常高字节在前
          result.add(highByte);
          result.add(lowByte);

          _bleService.addLog(
              '【命令服务】字符"$char"的16位值$rawValue拆分为: 0x${highByte.toRadixString(16).toUpperCase().padLeft(2, '0')} 0x${lowByte.toRadixString(16).toUpperCase().padLeft(2, '0')}');
        } else if (rawValue >= 0 && rawValue <= 255) {
          result.add(rawValue);
        } else {
          _bleService.addLog('【命令服务】字符"$char"产生无效字节值: $rawValue');
        }
      }

      // 直接返回拆分后的结果，跳过验证（因为gbk库的decode有问题）
      if (result.isNotEmpty) {
        _bleService.addLog(
            '【命令服务】字符"$char"GBK编码成功: ${result.map((b) => '0x${b.toRadixString(16).toUpperCase().padLeft(2, '0')}').join(' ')}');
        return result;
      }

      return [];
    } catch (e) {
      _bleService.addLog('【命令服务】字符"$char"GBK编码异常: $e');
      return [];
    }
  }

  // 第二级：计算式GBK编码（基于正确的Unicode到GBK映射）
  List<int> _calculateGbkEncoding(String text) {
    try {
      _bleService.addLog('【命令服务】开始计算式GBK编码');

      List<int> result = [];

      for (int i = 0; i < text.length; i++) {
        String char = text[i];
        int unicode = char.codeUnitAt(0);

        // ASCII字符直接转换
        if (unicode < 128) {
          result.add(unicode);
          continue;
        }

        // 中文字符通过正确的GBK编码映射
        List<int> gbkBytes = _getCorrectGbkEncoding(char);
        if (gbkBytes.isNotEmpty) {
          result.addAll(gbkBytes);
          _bleService.addLog(
              '【命令服务】字符 $char 正确GBK编码: ${gbkBytes.map((b) => b.toRadixString(16).toUpperCase().padLeft(2, '0')).join(' ')}');
        } else {
          _bleService.addLog('【命令服务】错误：字符 $char 无GBK编码，跳过该字符');
        }
      }

      _bleService.addLog('【命令服务】计算式GBK编码完成，总长度: ${result.length}字节');
      return result;
    } catch (e) {
      _bleService.addLog('【命令服务】计算式GBK编码异常: $e');
      return [];
    }
  }

  // 获取正确的GBK编码（通过修复gbk库的问题）
  List<int> _getCorrectGbkEncoding(String char) {
    try {
      // 尝试单个字符的GBK编码并修复
      var encoded = gbk.encode(char);

      List<int> bytes = [];
      for (var item in encoded) {
        int rawValue = (item is int) ? item : (item as num).toInt();

        // 修复16位值问题
        if (rawValue > 255) {
          // 16位值拆分为两个字节
          int highByte = (rawValue >> 8) & 0xFF;
          int lowByte = rawValue & 0xFF;

          if (highByte > 0) bytes.add(highByte);
          if (lowByte > 0) bytes.add(lowByte);
        } else if (rawValue >= 0) {
          bytes.add(rawValue);
        }
      }

      // 验证单个字符的编码结果
      if (bytes.isNotEmpty) {
        try {
          String decoded = gbk.decode(bytes);
          if (decoded == char) {
            return bytes;
          }
        } catch (e) {
          // 解码失败，继续尝试其他方法
        }
      }

      return [];
    } catch (e) {
      return [];
    }
  }

  // Unicode码点到GBK编码的数学计算 - 完全动态计算方法
  List<int> _unicodeToGbk(int unicode) {
    // ASCII字符直接返回
    if (unicode < 128) {
      return [unicode];
    }

    // 尝试通过系统编码能力动态计算
    try {
      String char = String.fromCharCode(unicode);

      // 使用系统的编码转换能力
      List<int> utf8Bytes = utf8.encode(char);

      // 如果是单字节UTF-8（ASCII），直接返回
      if (utf8Bytes.length == 1) {
        return utf8Bytes;
      }

      // 对于多字节字符，尝试通过gbk库的编码能力
      try {
        var gbkEncoded = gbk.encode(char);
        List<int> result = [];

        for (var item in gbkEncoded) {
          int byteValue;
          if (item is int) {
            byteValue = item;
          } else {
            byteValue = (item as num).toInt();
          }

          // 确保字节值在有效范围内
          if (byteValue >= 0 && byteValue <= 255) {
            result.add(byteValue);
          } else {
            // 如果gbk库产生无效字节，记录错误
            _bleService.addLog('【命令服务】GBK库对字符 $char 产生无效字节: $byteValue');
            return [];
          }
        }

        // 验证编码结果
        try {
          String decoded = gbk.decode(result);
          if (decoded == char) {
            return result;
          } else {
            _bleService.addLog('【命令服务】GBK编码验证失败，字符: $char');
            return [];
          }
        } catch (e) {
          _bleService.addLog('【命令服务】GBK解码验证异常，字符: $char: $e');
          return [];
        }
      } catch (e) {
        _bleService.addLog('【命令服务】GBK编码异常，字符: $char: $e');
        return [];
      }
    } catch (e) {
      _bleService
          .addLog('【命令服务】字符处理异常，Unicode: 0x${unicode.toRadixString(16)}: $e');
      return [];
    }
  }

  // 验证GBK编码结果的正确性
  bool _validateGbkEncoding(String originalText, List<int> gbkBytes) {
    try {
      // 尝试解码验证
      String decoded = gbk.decode(gbkBytes);
      return decoded == originalText;
    } catch (e) {
      _bleService.addLog('【命令服务】GBK编码验证异常: $e');
      return false;
    }
  }

  // 检查字符串是否只包含ASCII字符
  bool _isAsciiOnly(String text) {
    for (int i = 0; i < text.length; i++) {
      if (text.codeUnitAt(i) > 127) {
        return false;
      }
    }
    return true;
  }

  // 记录字节数组的详细信息，确保每个字节都在正确范围内
  void logByteDetails(String tag, List<int> bytes) {
    _bleService.addLog('【命令服务】$tag: 长度=${bytes.length}字节');

    // 检查是否有超出单字节范围的值
    bool hasInvalidByte = false;
    for (int i = 0; i < bytes.length; i++) {
      if (bytes[i] < 0 || bytes[i] > 255) {
        _bleService.addLog('【命令服务】警告: 位置$i的值${bytes[i]}超出单字节范围(0-255)!');
        hasInvalidByte = true;
      }
    }

    if (hasInvalidByte) {
      _bleService.addLog('【命令服务】警告: 数据包含无效字节值，可能导致十六进制表示不正确!');
    }

    // 显示正确的十六进制表示
    String hexString = '';
    for (int i = 0; i < bytes.length; i++) {
      // 确保值在0-255范围内
      int byteValue = bytes[i] & 0xFF;
      hexString += byteValue.toRadixString(16).padLeft(2, '0').toUpperCase();
      if (i < bytes.length - 1 && i % 16 != 15) {
        hexString += ' ';
      }
      if (i % 16 == 15) {
        hexString += '\n';
      }
    }

    _bleService.addLog('【命令服务】$tag 十六进制表示:\n$hexString');
    _bleService.addLog(
        '【命令服务】$tag 十六进制字符数: ${hexString.replaceAll(' ', '').replaceAll('\n', '').length}');
  }

  // 构建Modbus项目地点命令: 01 10 00 19 00 14 28 XX...XX CRC
  List<int> buildProjectLocationModbusCommand(String location) {
    try {
      _bleService.addLog('【命令服务】构建项目地点Modbus命令:');
      _bleService.addLog('【命令服务】原始项目地点: $location');

      // 将项目地点字符串转换为GBK编码的字节
      List<int> gbkBytes = safeGbkEncode(location);
      _bleService.addLog('【命令服务】GBK编码后字节长度: ${gbkBytes.length}');

      // 验证GBK编码结果
      _bleService.addLog('【命令服务】原始项目地点: "$location"');
      try {
        String decoded = gbk.decode(gbkBytes);
        _bleService.addLog('【命令服务】GBK解码验证: "$decoded"');
        _bleService.addLog('【命令服务】编码解码一致: ${location == decoded}');
      } catch (e) {
        _bleService.addLog('【命令服务】GBK解码验证失败: $e');
      }

      // 记录处理后的字节详情
      logByteDetails('GBK编码后', gbkBytes);

      // 根据需求，项目地点需要固定40个字节（80个十六进制字符）
      const int REQUIRED_BYTES = 40; // 40个字节
      List<int> data = List<int>.from(gbkBytes);

      // 如果长度不足，进行填充
      if (data.length < REQUIRED_BYTES) {
        _bleService.addLog(
            '【命令服务】数据需要填充，当前长度: ${data.length}字节, 需要长度: $REQUIRED_BYTES字节');
        // 使用0填充到指定长度
        int fillBytes = REQUIRED_BYTES - data.length;
        _bleService.addLog('【命令服务】需要填充: $fillBytes 字节');

        // 清空之前可能的数据填充
        data = List<int>.from(gbkBytes);
        // 填充0到指定长度
        for (int i = 0; i < fillBytes; i++) {
          data.add(0);
        }

        // 验证填充后长度
        _bleService.addLog('【命令服务】填充后数据长度: ${data.length}字节');
        if (data.length != REQUIRED_BYTES) {
          _bleService.addLog('【命令服务】错误：填充后长度不符合要求！');
        }
      }
      // 如果超过长度，需要截断
      else if (data.length > REQUIRED_BYTES) {
        _bleService.addLog(
            '【命令服务】数据超出长度限制，需要截断，当前长度: ${data.length}字节, 限制长度: $REQUIRED_BYTES字节');
        data = data.sublist(0, REQUIRED_BYTES);
      }

      // 记录处理后的数据详情
      logByteDetails('处理后数据', data);

      // 构建Modbus命令头
      List<int> command = [
        0x01, // 设备地址
        0x10, // 功能码：写多个寄存器
        0x00, 0x19, // 起始寄存器地址
        0x00, 0x14, // 寄存器数量 (20个寄存器 = 40字节)
        0x28 // 字节数 (40字节)
      ];

      // 添加数据 - 确保每个字节都在0-255范围内
      for (int byte in data) {
        command.add(byte & 0xFF);
      }

      // 计算并添加CRC
      int crc = calculateCRC16Modbus(command);
      command.add(crc & 0xFF); // CRC低字节
      command.add((crc >> 8) & 0xFF); // CRC高字节

      // 记录完整命令详情
      logByteDetails('完整命令', command);

      return command;
    } catch (e) {
      _bleService.addLog('【命令服务】构建项目地点Modbus命令失败: $e');
      return [];
    }
  }

  // 构建Modbus项目名称命令
  List<int> buildProjectNameModbusCommand(String name) {
    try {
      _bleService.addLog('【命令服务】构建项目名称Modbus命令:');
      _bleService.addLog('【命令服务】原始项目名称: $name');

      // 将项目名称字符串转换为GBK编码的字节
      List<int> gbkBytes = safeGbkEncode(name);
      _bleService.addLog('【命令服务】GBK编码后字节长度: ${gbkBytes.length}');

      // 验证GBK编码结果
      _bleService.addLog('【命令服务】原始项目名称: "$name"');
      try {
        String decoded = gbk.decode(gbkBytes);
        _bleService.addLog('【命令服务】GBK解码验证: "$decoded"');
        _bleService.addLog('【命令服务】编码解码一致: ${name == decoded}');
      } catch (e) {
        _bleService.addLog('【命令服务】GBK解码验证失败: $e');
      }

      // 记录处理后的字节详情
      logByteDetails('GBK编码后', gbkBytes);

      // 根据需求，项目名称需要固定60个字节（120个十六进制字符）
      const int REQUIRED_BYTES = 60; // 60个字节
      List<int> data = List<int>.from(gbkBytes);

      // 如果长度不足，进行填充
      if (data.length < REQUIRED_BYTES) {
        _bleService.addLog(
            '【命令服务】数据需要填充，当前长度: ${data.length}字节, 需要长度: $REQUIRED_BYTES字节');
        // 使用0填充到指定长度
        int fillBytes = REQUIRED_BYTES - data.length;
        _bleService.addLog('【命令服务】需要填充: $fillBytes 字节');

        // 清空之前可能的数据填充
        data = List<int>.from(gbkBytes);
        // 填充0到指定长度
        for (int i = 0; i < fillBytes; i++) {
          data.add(0);
        }

        // 验证填充后长度
        _bleService.addLog('【命令服务】填充后数据长度: ${data.length}字节');
        if (data.length != REQUIRED_BYTES) {
          _bleService.addLog('【命令服务】错误：填充后长度不符合要求！');
        }
      }
      // 如果超过长度，需要截断
      else if (data.length > REQUIRED_BYTES) {
        _bleService.addLog(
            '【命令服务】数据超出长度限制，需要截断，当前长度: ${data.length}字节, 限制长度: $REQUIRED_BYTES字节');
        data = data.sublist(0, REQUIRED_BYTES);
      }

      // 记录处理后的数据详情
      logByteDetails('处理后数据', data);

      // 构建Modbus命令头
      List<int> command = [
        0x01, // 设备地址
        0x10, // 功能码：写多个寄存器
        0x00, 0x3C, // 起始寄存器地址 (项目名称寄存器)
        0x00, 0x1E, // 寄存器数量 (30个寄存器 = 60字节)
        0x3C // 字节数 (60字节)
      ];

      // 添加数据 - 确保每个字节都在0-255范围内
      for (int byte in data) {
        command.add(byte & 0xFF);
      }

      // 计算并添加CRC
      int crc = calculateCRC16Modbus(command);
      command.add(crc & 0xFF); // CRC低字节
      command.add((crc >> 8) & 0xFF); // CRC高字节

      // 记录完整命令详情
      logByteDetails('完整命令', command);

      return command;
    } catch (e) {
      _bleService.addLog('【命令服务】构建项目名称Modbus命令失败: $e');
      return [];
    }
  }

  // 构建Modbus项目编号命令
  List<int> buildProjectCodeModbusCommand(String code) {
    try {
      _bleService.addLog('【命令服务】构建项目编号Modbus命令:');
      _bleService.addLog('【命令服务】原始项目编号: $code');

      // 将项目编号字符串转换为GBK编码的字节
      List<int> gbkBytes = safeGbkEncode(code);
      _bleService.addLog('【命令服务】GBK编码后字节长度: ${gbkBytes.length}');

      // 验证GBK编码结果
      _bleService.addLog('【命令服务】原始项目编号: "$code"');
      try {
        String decoded = gbk.decode(gbkBytes);
        _bleService.addLog('【命令服务】GBK解码验证: "$decoded"');
        _bleService.addLog('【命令服务】编码解码一致: ${code == decoded}');
      } catch (e) {
        _bleService.addLog('【命令服务】GBK解码验证失败: $e');
      }

      // 记录处理后的字节详情
      logByteDetails('GBK编码后', gbkBytes);

      // 根据需求，项目编号需要固定20个字节（40个十六进制字符）
      const int REQUIRED_BYTES = 20; // 20个字节
      List<int> data = List<int>.from(gbkBytes);

      // 如果长度不足，进行填充
      if (data.length < REQUIRED_BYTES) {
        _bleService.addLog(
            '【命令服务】数据需要填充，当前长度: ${data.length}字节, 需要长度: $REQUIRED_BYTES字节');
        // 使用0填充到指定长度
        int fillBytes = REQUIRED_BYTES - data.length;
        _bleService.addLog('【命令服务】需要填充: $fillBytes 字节');

        // 清空之前可能的数据填充
        data = List<int>.from(gbkBytes);
        // 填充0到指定长度
        for (int i = 0; i < fillBytes; i++) {
          data.add(0);
        }

        // 验证填充后长度
        _bleService.addLog('【命令服务】填充后数据长度: ${data.length}字节');
        if (data.length != REQUIRED_BYTES) {
          _bleService.addLog('【命令服务】错误：填充后长度不符合要求！');
        }
      }
      // 如果超过长度，需要截断
      else if (data.length > REQUIRED_BYTES) {
        _bleService.addLog(
            '【命令服务】数据超出长度限制，需要截断，当前长度: ${data.length}字节, 限制长度: $REQUIRED_BYTES字节');
        data = data.sublist(0, REQUIRED_BYTES);
      }

      // 记录处理后的数据详情
      logByteDetails('处理后数据', data);

      // 构建Modbus命令头
      List<int> command = [
        0x01, // 设备地址
        0x10, // 功能码：写多个寄存器
        0x00, 0x32, // 起始寄存器地址 (项目编号寄存器)
        0x00, 0x0A, // 寄存器数量 (10个寄存器 = 20字节)
        0x14 // 字节数 (20字节)
      ];

      // 添加数据 - 确保每个字节都在0-255范围内
      for (int byte in data) {
        command.add(byte & 0xFF);
      }

      // 计算并添加CRC
      int crc = calculateCRC16Modbus(command);
      command.add(crc & 0xFF); // CRC低字节
      command.add((crc >> 8) & 0xFF); // CRC高字节

      // 记录完整命令详情
      logByteDetails('完整命令', command);

      return command;
    } catch (e) {
      _bleService.addLog('【命令服务】构建项目编号Modbus命令失败: $e');
      return [];
    }
  }

  // 构建Modbus项目ID命令
  List<int> buildProjectIdModbusCommand(String projectId) {
    try {
      _bleService.addLog('【命令服务】构建项目ID Modbus命令:');
      _bleService.addLog('【命令服务】原始项目ID: $projectId');

      // 将项目ID字符串转换为GBK编码的字节
      List<int> gbkBytes = safeGbkEncode(projectId);
      _bleService.addLog('【命令服务】GBK编码后字节长度: ${gbkBytes.length}');

      // 验证GBK编码结果
      _bleService.addLog('【命令服务】原始项目ID: "$projectId"');
      try {
        String decoded = gbk.decode(gbkBytes);
        _bleService.addLog('【命令服务】GBK解码验证: "$decoded"');
        _bleService.addLog('【命令服务】编码解码一致: ${projectId == decoded}');
      } catch (e) {
        _bleService.addLog('【命令服务】GBK解码验证失败: $e');
      }

      // 记录处理后的字节详情
      logByteDetails('GBK编码后', gbkBytes);

      // 根据需求，项目ID需要固定20个字节（40个十六进制字符）
      const int REQUIRED_BYTES = 20; // 20个字节
      List<int> data = List<int>.from(gbkBytes);

      // 如果长度不足，进行填充
      if (data.length < REQUIRED_BYTES) {
        _bleService.addLog(
            '【命令服务】数据需要填充，当前长度: ${data.length}字节, 需要长度: $REQUIRED_BYTES字节');
        // 使用0填充到指定长度
        int fillBytes = REQUIRED_BYTES - data.length;
        _bleService.addLog('【命令服务】需要填充: $fillBytes 字节');

        // 清空之前可能的数据填充
        data = List<int>.from(gbkBytes);
        // 填充0到指定长度
        for (int i = 0; i < fillBytes; i++) {
          data.add(0);
        }

        // 验证填充后长度
        _bleService.addLog('【命令服务】填充后数据长度: ${data.length}字节');
        if (data.length != REQUIRED_BYTES) {
          _bleService.addLog('【命令服务】错误：填充后长度不符合要求！');
        }
      }
      // 如果超过长度，需要截断
      else if (data.length > REQUIRED_BYTES) {
        _bleService.addLog(
            '【命令服务】数据超出长度限制，需要截断，当前长度: ${data.length}字节, 限制长度: $REQUIRED_BYTES字节');
        data = data.sublist(0, REQUIRED_BYTES);
      }

      // 记录处理后的数据详情
      logByteDetails('处理后数据', data);

      // 构建Modbus命令头
      List<int> command = [
        0x01, // 设备地址
        0x10, // 功能码：写多个寄存器
        0x00, 0x5F, // 起始寄存器地址 (项目ID寄存器)
        0x00, 0x0A, // 寄存器数量 (10个寄存器 = 20字节)
        0x14 // 字节数 (20字节)
      ];

      // 添加数据 - 确保每个字节都在0-255范围内
      for (int byte in data) {
        command.add(byte & 0xFF);
      }

      // 计算并添加CRC
      int crc = calculateCRC16Modbus(command);
      command.add(crc & 0xFF); // CRC低字节
      command.add((crc >> 8) & 0xFF); // CRC高字节

      // 记录完整命令详情
      logByteDetails('完整命令', command);

      return command;
    } catch (e) {
      _bleService.addLog('【命令服务】构建项目ID Modbus命令失败: $e');
      return [];
    }
  }

  // 发送所有项目信息到焊机
  Future<bool> sendProjectInfoToWeldingMachine(Project project) async {
    try {
      _bleService.addLog('【命令服务】开始发送项目信息到焊机...');

      // 1. 发送项目地点信息
      _bleService.addLog('【命令服务】准备发送项目地点信息...');
      List<int> locationCommand =
          buildProjectLocationModbusCommand(project.address);
      bool locationSent = await _bleService.sendData(locationCommand);
      _bleService.addLog('【命令服务】项目地点信息发送${locationSent ? '成功' : '失败'}');

      // 确保足够的处理时间，无论成功或失败都等待1秒再发送下一条
      _bleService.addLog('【命令服务】等待1秒，确保接收端处理完成...');
      await Future.delayed(Duration(milliseconds: 1000));

      // 2. 发送项目名称信息
      _bleService.addLog('【命令服务】准备发送项目名称信息...');
      List<int> nameCommand = buildProjectNameModbusCommand(project.name);
      bool nameSent = await _bleService.sendData(nameCommand);
      _bleService.addLog('【命令服务】项目名称信息发送${nameSent ? '成功' : '失败'}');

      // 再次等待1秒确保接收端处理完成
      _bleService.addLog('【命令服务】等待1秒，确保接收端处理完成...');
      await Future.delayed(Duration(milliseconds: 1000));

      // 3. 发送项目编号信息
      _bleService.addLog('【命令服务】准备发送项目编号信息...');
      List<int> codeCommand = buildProjectCodeModbusCommand(project.code);
      bool codeSent = await _bleService.sendData(codeCommand);
      _bleService.addLog('【命令服务】项目编号信息发送${codeSent ? '成功' : '失败'}');

      // 再次等待1秒确保接收端处理完成
      _bleService.addLog('【命令服务】等待1秒，确保接收端处理完成...');
      await Future.delayed(Duration(milliseconds: 1000));

      // 4. 发送项目ID信息
      _bleService.addLog('【命令服务】准备发送项目ID信息...');
      List<int> idCommand = buildProjectIdModbusCommand(project.id);
      bool idSent = await _bleService.sendData(idCommand);
      _bleService.addLog('【命令服务】项目ID信息发送${idSent ? '成功' : '失败'}');

      // 返回整体结果
      bool overallResult = locationSent && nameSent && codeSent && idSent;
      _bleService.addLog('【命令服务】项目信息发送${overallResult ? '全部成功' : '部分失败'}');

      return overallResult;
    } catch (e) {
      _bleService.addLog('【命令服务】发送项目信息到焊机出错: $e');
      return false;
    }
  }

  // 测试方法：验证字节填充是否正确
  void testDataFilling() {
    _bleService.addLog('【命令服务】========= 测试字节填充 =========');

    // 测试数据
    String testLocation = "陕西省/杨凌市/杨陵区";
    _bleService.addLog('【命令服务】测试数据: $testLocation');

    // 转GBK编码
    List<int> gbkBytes = gbk.encode(testLocation);
    _bleService.addLog('【命令服务】GBK编码后字节数: ${gbkBytes.length}');
    _bleService.addLog('【命令服务】GBK十六进制: ${bytesToHexString(gbkBytes)}');

    // 固定填充到40字节
    const int REQUIRED_BYTES = 40;
    List<int> paddedData = List<int>.from(gbkBytes);
    int fillBytes = REQUIRED_BYTES - paddedData.length;
    _bleService.addLog('【命令服务】需要填充: $fillBytes 字节');

    // 填充方式1：逐个添加
    List<int> paddedData1 = List<int>.from(gbkBytes);
    for (int i = 0; i < fillBytes; i++) {
      paddedData1.add(0);
    }
    _bleService.addLog('【命令服务】方式1: 逐个添加0');
    _bleService.addLog('【命令服务】填充后长度: ${paddedData1.length}字节');
    String hex1 = paddedData1
        .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
        .join('');
    _bleService.addLog('【命令服务】十六进制(无空格): $hex1');
    _bleService.addLog('【命令服务】十六进制长度: ${hex1.length}字符');

    // 填充方式2：使用List.filled创建填充
    List<int> paddedData2 = List<int>.from(gbkBytes);
    paddedData2.addAll(List<int>.filled(fillBytes, 0));
    _bleService.addLog('【命令服务】方式2: List.filled填充');
    _bleService.addLog('【命令服务】填充后长度: ${paddedData2.length}字节');
    String hex2 = paddedData2
        .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
        .join('');
    _bleService.addLog('【命令服务】十六进制(无空格): $hex2');
    _bleService.addLog('【命令服务】十六进制长度: ${hex2.length}字符');

    // 对比两种方式
    _bleService.addLog('【命令服务】两种方式结果相同: ${hex1 == hex2}');
    _bleService.addLog('【命令服务】========= 测试结束 =========');
  }

  // 构建读取焊接参数命令
  List<int> buildReadWeldingParametersCommand() {
    try {
      _bleService.addLog('【命令服务】构建读取焊接参数命令');

      // 构建Modbus Read Holding Registers命令 (功能码 0x03)
      // 01 03 00 14 00 0A CRC(2字节)
      // 01: 从站地址 (默认使用01)
      // 03: 功能码 (读取保持寄存器)
      // 00 14: 起始地址 (0x0014, 十进制20, 存储焊接参数的起始地址)
      // 00 0A: 寄存器数量 (读取10个寄存器)
      List<int> command = [0x01, 0x03, 0x00, 0x14, 0x00, 0x0A];

      // 计算CRC校验码
      int crc = calculateCRC16Modbus(command);

      // 添加CRC (低字节在前，高字节在后)
      command.add(crc & 0xFF);
      command.add((crc >> 8) & 0xFF);

      // 记录日志
      String hexCommand = command
          .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
          .join(' ');
      _bleService.addLog('【命令服务】读取焊接参数命令: $hexCommand');

      return command;
    } catch (e) {
      _bleService.addLog('【命令服务】构建读取焊接参数命令失败: $e');
      return [];
    }
  }

  // 构建读取焊接状态命令
  List<int> buildReadWeldingStatusCommand() {
    try {
      _bleService.addLog('【命令服务】构建读取焊接状态命令');

      // 构建Modbus Read Holding Registers命令 (功能码 0x03)
      // 01 03 00 00 00 04 CRC(2字节)
      // 01: 从站地址 (默认使用01)
      // 03: 功能码 (读取保持寄存器)
      // 00 00: 起始地址 (0x0000, 存储焊接状态的起始地址)
      // 00 04: 寄存器数量 (读取4个寄存器)
      List<int> command = [0x01, 0x03, 0x00, 0x00, 0x00, 0x04];

      // 计算CRC校验码
      int crc = calculateCRC16Modbus(command);

      // 添加CRC (低字节在前，高字节在后)
      command.add(crc & 0xFF);
      command.add((crc >> 8) & 0xFF);

      // 记录日志
      String hexCommand = command
          .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
          .join(' ');
      _bleService.addLog('【命令服务】读取焊接状态命令: $hexCommand');

      return command;
    } catch (e) {
      _bleService.addLog('【命令服务】构建读取焊接状态命令失败: $e');
      return [];
    }
  }

  // 构建读取焊接机器编号命令
  List<int> buildReadMachineNumberCommand() {
    try {
      _bleService.addLog('【命令服务】构建读取焊接机器编号命令');

      // 构建Modbus Read Holding Registers命令 (功能码 0x03)
      // 01 03 00 05 00 05 95 C8
      // 01: 从站地址 (默认使用01)
      // 03: 功能码 (读取保持寄存器)
      // 00 05: 起始地址 (0x0005, 存储焊接机器编号的起始地址)
      // 00 05: 寄存器数量 (读取5个寄存器)
      List<int> command = [0x01, 0x03, 0x00, 0x05, 0x00, 0x05];

      // 计算CRC校验码
      int crc = calculateCRC16Modbus(command);

      // 添加CRC (低字节在前，高字节在后)
      command.add(crc & 0xFF);
      command.add((crc >> 8) & 0xFF);

      // 记录日志
      String hexCommand = command
          .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
          .join(' ');
      _bleService.addLog('【命令服务】读取焊接机器编号命令: $hexCommand');

      return command;
    } catch (e) {
      _bleService.addLog('【命令服务】构建读取焊接机器编号命令失败: $e');
      return [];
    }
  }

  // 解析焊接参数响应
  Map<String, dynamic> parseWeldingParametersResponse(List<int> responseData) {
    try {
      _bleService.addLog('【命令服务】解析焊接参数响应');

      // 检查响应数据的有效性
      if (responseData.length < 25) {
        // 1(地址) + 1(功能码) + 1(字节数) + 20(数据) + 2(CRC) = 25
        _bleService.addLog('【命令服务】响应数据长度不足，无法解析');
        return {'success': false, 'message': '数据长度不足'};
      }

      // 检查功能码
      if (responseData[1] != 0x03) {
        _bleService.addLog('【命令服务】响应功能码错误');
        return {'success': false, 'message': '功能码错误'};
      }

      // 检查字节数
      if (responseData[2] != 20) {
        // 10个寄存器，每个2字节
        _bleService.addLog('【命令服务】响应字节数不匹配');
        return {'success': false, 'message': '字节数不匹配'};
      }

      // 提取数据值
      Map<String, dynamic> result = {
        'success': true,
        'currentSetpoint':
            (responseData[3] << 8 | responseData[4]) / 10.0, // 电流设定值，除以10转换为实际值
        'voltageSetpoint':
            (responseData[5] << 8 | responseData[6]) / 10.0, // 电压设定值，除以10转换为实际值
        'wireSpeed':
            (responseData[7] << 8 | responseData[8]) / 10.0, // 送丝速度，除以10转换为实际值
        'arcLength': (responseData[9] << 8 | responseData[10]), // 弧长调节
        'inductance': (responseData[11] << 8 | responseData[12]), // 电感设定
        'preflowTime': (responseData[13] << 8 | responseData[14]) /
            10.0, // 气体预流时间，除以10转换为实际值(秒)
        'postflowTime': (responseData[15] << 8 | responseData[16]) /
            10.0, // 气体后流时间，除以10转换为实际值(秒)
        'slowFeedSpeed': (responseData[17] << 8 | responseData[18]) /
            10.0, // 缓慢送丝速度，除以10转换为实际值
        'burnbackTime': (responseData[19] << 8 | responseData[20]) /
            10.0, // 回烧时间，除以10转换为实际值(秒)
        'spotWeldTime': (responseData[21] << 8 | responseData[22]) /
            10.0, // 点焊时间，除以10转换为实际值(秒)
      };

      _bleService.addLog('【命令服务】焊接参数解析结果: $result');
      return result;
    } catch (e) {
      _bleService.addLog('【命令服务】解析焊接参数响应失败: $e');
      return {'success': false, 'message': '解析错误：$e'};
    }
  }

  // 解析焊接状态响应
  Map<String, dynamic> parseWeldingStatusResponse(List<int> responseData) {
    try {
      _bleService.addLog('【命令服务】解析焊接状态响应');

      // 检查响应数据的有效性
      if (responseData.length < 13) {
        // 1(地址) + 1(功能码) + 1(字节数) + 8(数据) + 2(CRC) = 13
        _bleService.addLog('【命令服务】响应数据长度不足，无法解析');
        return {'success': false, 'message': '数据长度不足'};
      }

      // 检查功能码
      if (responseData[1] != 0x03) {
        _bleService.addLog('【命令服务】响应功能码错误');
        return {'success': false, 'message': '功能码错误'};
      }

      // 检查字节数
      if (responseData[2] != 8) {
        // 4个寄存器，每个2字节
        _bleService.addLog('【命令服务】响应字节数不匹配');
        return {'success': false, 'message': '字节数不匹配'};
      }

      // 提取数据值
      Map<String, dynamic> result = {
        'success': true,
        'currentValue':
            (responseData[3] << 8 | responseData[4]) / 10.0, // 实际电流值，除以10转换为实际值
        'voltageValue':
            (responseData[5] << 8 | responseData[6]) / 10.0, // 实际电压值，除以10转换为实际值
        'machineStatus': responseData[7] << 8 | responseData[8], // 机器状态
        'errorCode': responseData[9] << 8 | responseData[10], // 错误代码
      };

      // 解释机器状态
      int status = result['machineStatus'] as int;
      result['isIdle'] = (status & 0x0001) == 0; // 位0: 0=空闲, 1=焊接中
      result['isError'] = (status & 0x0002) != 0; // 位1: 0=正常, 1=错误
      result['isCooling'] = (status & 0x0004) != 0; // 位2: 0=正常, 1=冷却中
      result['weldingMode'] = (status >> 8) & 0xFF; // 高字节: 焊接模式

      // 添加状态描述
      result['statusDescription'] = result['isIdle'] ? '空闲' : '焊接中';
      if (result['isError']) result['statusDescription'] += ', 错误状态';
      if (result['isCooling']) result['statusDescription'] += ', 冷却中';

      // 添加模式描述
      Map<int, String> modeMap = {
        0: '手动模式',
        1: 'MIG/MAG模式',
        2: 'TIG模式',
        3: 'MMA模式',
        4: '点焊模式',
        5: '脉冲模式',
      };
      result['modeDescription'] = modeMap[result['weldingMode']] ?? '未知模式';

      _bleService.addLog('【命令服务】焊接状态解析结果: $result');
      return result;
    } catch (e) {
      _bleService.addLog('【命令服务】解析焊接状态响应失败: $e');
      return {'success': false, 'message': '解析错误：$e'};
    }
  }

  // 增强的焊接机器编号响应解析（返回更详细的信息）
  Map<String, dynamic> enhancedParseMachineNumberResponse(
      List<int> responseData) {
    try {
      _bleService.addLog('【命令服务】增强解析焊接机器编号响应');

      // 检查响应数据的有效性
      if (responseData.length < 15) {
        // 1(地址) + 1(功能码) + 1(字节数) + 10(数据) + 2(CRC) = 15
        _bleService.addLog('【命令服务】响应数据长度不足，无法解析');
        return {'success': false, 'message': '数据长度不足'};
      }

      // 检查功能码
      if (responseData[1] != 0x03) {
        _bleService.addLog('【命令服务】响应功能码错误');
        return {'success': false, 'message': '功能码错误'};
      }

      // 检查字节数
      if (responseData[2] != 10) {
        // 5个寄存器，每个2字节
        _bleService.addLog('【命令服务】响应字节数不匹配');
        return {'success': false, 'message': '字节数不匹配'};
      }

      // 提取数据值 - 这里假设机器编号存储为5个寄存器的ASCII字符
      List<int> machineNumberBytes = [];
      for (int i = 0; i < 10; i++) {
        machineNumberBytes.add(responseData[3 + i]);
      }

      // 过滤掉0值
      machineNumberBytes = machineNumberBytes.where((b) => b != 0).toList();

      // 尝试将字节解码为ASCII字符串
      String machineNumber = String.fromCharCodes(machineNumberBytes);

      // 获取原始的十六进制表示（用于兼容旧版解析）
      String machineNumberHex = machineNumberBytes
          .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
          .join(' ');

      Map<String, dynamic> result = {
        'success': true,
        'machineNumber': machineNumber,
        'machineNumberHex': machineNumberHex,
        'rawBytes': machineNumberBytes
      };

      _bleService.addLog('【命令服务】焊接机器编号解析结果: $result');
      return result;
    } catch (e) {
      _bleService.addLog('【命令服务】解析焊接机器编号响应失败: $e');
      return {'success': false, 'message': '解析错误：$e'};
    }
  }
}
