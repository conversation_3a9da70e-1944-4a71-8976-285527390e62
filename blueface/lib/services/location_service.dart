import 'dart:async';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

/// 位置数据模型
class LocationData {
  final double longitude; // 经度
  final double latitude; // 纬度
  final double altitude; // 海拔
  final DateTime timestamp; // 时间戳
  final double accuracy; // 精度

  LocationData({
    required this.longitude,
    required this.latitude,
    required this.altitude,
    required this.timestamp,
    required this.accuracy,
  });

  @override
  String toString() {
    return 'LocationData(经度: $longitude, 纬度: $latitude, 海拔: $altitude, 精度: ${accuracy}m, 时间: $timestamp)';
  }
}

/// 位置服务
/// 功能：获取手机GPS定位数据（经纬度、海拔）
/// 注意：当前使用模拟数据，实际部署时需要添加 geolocator 和 permission_handler 依赖包
class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  /// 检查位置权限
  Future<bool> checkLocationPermission() async {
    try {
      // 检查位置权限
      PermissionStatus permission = await Permission.location.status;

      if (permission.isDenied) {
        // 请求位置权限
        permission = await Permission.location.request();
      }

      if (permission.isPermanentlyDenied) {
        // 权限被永久拒绝，需要用户手动在设置中开启
        print('位置权限被永久拒绝，请到设置中手动开启');
        return false;
      }

      return permission.isGranted;
    } catch (e) {
      print('检查位置权限失败: $e');
      return false;
    }
  }

  /// 检查位置服务是否可用
  Future<bool> isLocationServiceEnabled() async {
    try {
      return await Geolocator.isLocationServiceEnabled();
    } catch (e) {
      print('检查位置服务失败: $e');
      return false;
    }
  }

  /// 获取当前位置
  Future<LocationData?> getCurrentLocation() async {
    try {
      print('开始获取真实GPS位置信息...');

      // 检查位置服务是否启用
      bool serviceEnabled = await isLocationServiceEnabled();
      if (!serviceEnabled) {
        print('位置服务未启用，请开启GPS');
        return null;
      }

      // 检查权限
      bool hasPermission = await checkLocationPermission();
      if (!hasPermission) {
        print('位置权限未授予，无法获取位置信息');
        return null;
      }

      print('权限检查通过，开始获取位置...');

      // 获取当前位置
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: Duration(seconds: 30), // 增加超时时间到30秒
      );

      print(
          'GPS位置获取成功: 经度=${position.longitude}, 纬度=${position.latitude}, 海拔=${position.altitude}');

      return LocationData(
        longitude: position.longitude,
        latitude: position.latitude,
        altitude: position.altitude ?? 0.0, // 如果海拔为null，默认为0
        timestamp:
            position.timestamp ?? DateTime.now().toLocal(), // 确保使用本地时间（东八区）
        accuracy: position.accuracy,
      );
    } catch (e) {
      print('获取真实GPS位置失败: $e');

      // 如果获取真实GPS失败，可以选择是否使用备用方案
      if (e is TimeoutException) {
        print('GPS定位超时，可能是在室内或信号不好的地方');
      } else if (e is LocationServiceDisabledException) {
        print('位置服务被禁用');
      } else if (e is PermissionDeniedException) {
        print('位置权限被拒绝');
      }

      return null;
    }
  }

  /// 获取位置流（实时更新）
  Stream<LocationData> getLocationStream() {
    return Geolocator.getPositionStream(
      locationSettings: LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // 10米变化才更新
      ),
    ).map((position) => LocationData(
          longitude: position.longitude,
          latitude: position.latitude,
          altitude: position.altitude ?? 0.0,
          timestamp:
              position.timestamp ?? DateTime.now().toLocal(), // 确保使用本地时间（东八区）
          accuracy: position.accuracy,
        ));
  }

  /// 获取最后已知位置
  Future<LocationData?> getLastKnownPosition() async {
    try {
      Position? position = await Geolocator.getLastKnownPosition();
      if (position == null) {
        print('没有最后已知位置');
        return null;
      }

      print('获取到最后已知位置: 经度=${position.longitude}, 纬度=${position.latitude}');

      return LocationData(
        longitude: position.longitude,
        latitude: position.latitude,
        altitude: position.altitude ?? 0.0,
        timestamp:
            position.timestamp ?? DateTime.now().toLocal(), // 确保使用本地时间（东八区）
        accuracy: position.accuracy,
      );
    } catch (e) {
      print('获取最后已知位置失败: $e');
      return null;
    }
  }

  /// 获取位置信息（智能获取策略 - 优化版）
  Future<LocationData?> getLocationWithFallback() async {
    try {
      print('🚀 开始快速位置获取策略...');

      // 策略1: 优先使用最后已知位置（速度最快）
      LocationData? lastKnownLocation = await getLastKnownPosition();
      if (lastKnownLocation != null) {
        print('✅ 使用最后已知位置（快速策略）');

        // 在后台异步更新当前位置，但不等待结果
        _updateLocationInBackground();

        return lastKnownLocation;
      }

      print('📍 最后已知位置不可用，尝试快速获取当前位置...');

      // 策略2: 快速获取当前位置（降低精度要求，缩短超时）
      LocationData? currentLocation = await getCurrentLocationFast();
      if (currentLocation != null) {
        return currentLocation;
      }

      print('❌ 所有快速位置获取方案都失败了');
      return null;
    } catch (e) {
      print('智能获取位置策略失败: $e');
      return null;
    }
  }

  /// 快速获取当前位置（降低精度，缩短超时）
  Future<LocationData?> getCurrentLocationFast() async {
    try {
      print('⚡ 开始快速GPS定位...');

      // 检查位置服务是否启用
      bool serviceEnabled = await isLocationServiceEnabled();
      if (!serviceEnabled) {
        print('位置服务未启用，请开启GPS');
        return null;
      }

      // 检查权限
      bool hasPermission = await checkLocationPermission();
      if (!hasPermission) {
        print('位置权限未授予，无法获取位置信息');
        return null;
      }

      // 快速获取当前位置（降低精度要求，缩短超时时间）
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.medium, // 降低精度要求
        timeLimit: Duration(seconds: 10), // 缩短超时时间到10秒
      );

      print(
          '⚡ 快速GPS定位成功: 经度=${position.longitude}, 纬度=${position.latitude}, 海拔=${position.altitude}');

      return LocationData(
        longitude: position.longitude,
        latitude: position.latitude,
        altitude: position.altitude ?? 0.0,
        timestamp: position.timestamp ?? DateTime.now().toLocal(),
        accuracy: position.accuracy,
      );
    } catch (e) {
      print('快速GPS定位失败: $e');
      return null;
    }
  }

  /// 后台异步更新位置（不阻塞主流程）
  void _updateLocationInBackground() {
    // 在后台异步获取更精确的位置，但不等待结果
    getCurrentLocation().then((location) {
      if (location != null) {
        print('🔄 后台位置更新成功: ${location.longitude}, ${location.latitude}');
      }
    }).catchError((e) {
      print('🔄 后台位置更新失败: $e');
    });
  }

  /// 将经纬度转换为8字节数据（精度优化版本）
  List<int> convertCoordinateToBytes(double coordinate) {
    // 将浮点数转换为8字节数据
    // 降低精度：将double乘以1000转为整数（保留3位小数），然后转为8字节小端序
    int value = (coordinate * 1000).round(); // 从1000000改为1000，减少3位小数精度

    List<int> bytes = [];
    for (int i = 0; i < 8; i++) {
      bytes.add((value >> (8 * i)) & 0xFF);
    }

    return bytes;
  }

  /// 将海拔转换为2字节数据
  List<int> convertAltitudeToBytes(double altitude) {
    // 将海拔转换为2字节整数（单位：米）
    int value = altitude.round();

    // 限制在16位有符号整数范围内 (-32768 到 32767)
    if (value > 32767) value = 32767;
    if (value < -32768) value = -32768;

    // 转换为2字节，高字节在前
    return [
      (value >> 8) & 0xFF, // 高字节
      value & 0xFF, // 低字节
    ];
  }

  /// 格式化位置信息用于显示
  String formatLocationData(LocationData location) {
    // 确保时间显示为东八区（北京时间）
    DateTime beijingTime = location.timestamp.toLocal();
    String formattedTime =
        '${beijingTime.year}-${beijingTime.month.toString().padLeft(2, '0')}-${beijingTime.day.toString().padLeft(2, '0')} '
        '${beijingTime.hour.toString().padLeft(2, '0')}:${beijingTime.minute.toString().padLeft(2, '0')}:${beijingTime.second.toString().padLeft(2, '0')} '
        '(北京时间)';

    return '''
位置信息：
经度: ${location.longitude.toStringAsFixed(6)}°
纬度: ${location.latitude.toStringAsFixed(6)}°
海拔: ${location.altitude.toStringAsFixed(1)}m
精度: ±${location.accuracy.toStringAsFixed(1)}m
时间: $formattedTime
''';
  }

  /// 检查位置权限状态并提供用户友好的提示
  Future<String> getLocationPermissionStatus() async {
    try {
      PermissionStatus permission = await Permission.location.status;
      bool serviceEnabled = await isLocationServiceEnabled();

      if (!serviceEnabled) {
        return '位置服务已关闭，请在设置中开启GPS';
      }

      switch (permission) {
        case PermissionStatus.granted:
          return '位置权限已授予';
        case PermissionStatus.denied:
          return '位置权限被拒绝，点击重新请求权限';
        case PermissionStatus.permanentlyDenied:
          return '位置权限被永久拒绝，请到设置中手动开启';
        case PermissionStatus.restricted:
          return '位置权限受限制';
        default:
          return '位置权限状态未知';
      }
    } catch (e) {
      return '检查位置权限状态失败: $e';
    }
  }
}
