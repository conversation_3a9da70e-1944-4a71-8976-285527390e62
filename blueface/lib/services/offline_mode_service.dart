import 'dart:async';
import 'dart:math';
import '../models/offline_data_model.dart';
import '../models/project_model.dart';
import 'offline_storage_service.dart';
import 'network_service.dart';
import 'bluetooth_service.dart';

class OfflineModeService {
  static final OfflineModeService _instance = OfflineModeService._internal();
  factory OfflineModeService() => _instance;
  OfflineModeService._internal();

  final OfflineStorageService _storageService = OfflineStorageService();
  final NetworkService _networkService = NetworkService();
  final BleService _bleService = BleService();

  late StreamController<OfflineModeState> _stateController;
  late Stream<OfflineModeState> stateStream;

  OfflineModeState _currentState = OfflineModeState(
    isOfflineMode: false,
    pendingUploads: 0,
  );

  bool _isInitialized = false;
  StreamSubscription? _networkSubscription;

  // 初始化离线模式服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    // 初始化依赖服务
    await _storageService.initialize();
    await _networkService.initialize();

    // 创建状态流
    _stateController = StreamController<OfflineModeState>.broadcast();
    stateStream = _stateController.stream;

    // 加载当前状态
    _currentState = await _storageService.getOfflineModeState();

    _isInitialized = true;
    print('离线模式服务初始化完成');
  }

  // 手动设置离线模式
  Future<void> setOfflineMode(bool isOffline) async {
    _updateState(_currentState.copyWith(
      isOfflineMode: isOffline,
    ));
    print('手动设置${isOffline ? "离线" : "在线"}模式');
  }

  // 处理网络状态变化（仅用于数据同步，不切换模式）
  void _handleNetworkStatusChange(bool isOnline) {
    // 仅在离线模式下且网络恢复时尝试同步数据
    if (_currentState.isOfflineMode && isOnline) {
      print('检测到网络恢复，可进行数据同步...');
      // 注意：不改变离线模式状态，只是提醒可以同步
    }
  }

  // 更新状态并保存
  void _updateState(OfflineModeState newState) {
    _currentState = newState;
    _stateController.add(newState);
    _storageService.saveOfflineModeState(newState);
  }

  // 获取当前状态
  OfflineModeState get currentState => _currentState;

  // 设置当前用户和项目
  Future<void> setCurrentUserAndProject(
      String? userId, String? projectId) async {
    _updateState(_currentState.copyWith(
      currentUserId: userId,
      currentProjectId: projectId,
    ));
  }

  // 保存人脸校验数据
  Future<String> saveFaceVerificationData(
      Map<String, dynamic> faceData, String base64Image) async {
    try {
      // 保存图片
      final imagePath = await _storageService.saveImage(
        base64Image,
        'face',
        'face_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );

      // 创建离线数据记录
      final offlineData = OfflineDataModel(
        id: _generateId(),
        createdAt: DateTime.now(),
        type: 'face',
        data: faceData,
        imagePaths: [imagePath],
      );

      await _storageService.saveOfflineData(offlineData);
      await _updatePendingCount();

      print('人脸校验数据已保存到离线存储');
      return offlineData.id;
    } catch (e) {
      print('保存人脸校验数据失败: $e');
      rethrow;
    }
  }

  // 保存设备检验数据
  Future<String> saveDeviceInspectionData(
      Map<String, dynamic> deviceData, List<String> base64Images) async {
    try {
      List<String> imagePaths = [];

      // 保存多张图片
      for (int i = 0; i < base64Images.length; i++) {
        final imagePath = await _storageService.saveImage(
          base64Images[i],
          'device',
          'device_${DateTime.now().millisecondsSinceEpoch}_$i.jpg',
        );
        imagePaths.add(imagePath);
      }

      // 创建离线数据记录
      final offlineData = OfflineDataModel(
        id: _generateId(),
        createdAt: DateTime.now(),
        type: 'device',
        data: deviceData,
        imagePaths: imagePaths,
      );

      await _storageService.saveOfflineData(offlineData);
      await _updatePendingCount();

      print('设备检验数据已保存到离线存储');
      return offlineData.id;
    } catch (e) {
      print('保存设备检验数据失败: $e');
      rethrow;
    }
  }

  // 保存管材检验数据
  Future<String> savePipeInspectionData(
      Map<String, dynamic> pipeData, List<String> base64Images) async {
    try {
      List<String> imagePaths = [];

      // 保存多张图片
      for (int i = 0; i < base64Images.length; i++) {
        final imagePath = await _storageService.saveImage(
          base64Images[i],
          'pipe',
          'pipe_${DateTime.now().millisecondsSinceEpoch}_$i.jpg',
        );
        imagePaths.add(imagePath);
      }

      // 创建离线数据记录
      final offlineData = OfflineDataModel(
        id: _generateId(),
        createdAt: DateTime.now(),
        type: 'pipe',
        data: pipeData,
        imagePaths: imagePaths,
      );

      await _storageService.saveOfflineData(offlineData);
      await _updatePendingCount();

      print('管材检验数据已保存到离线存储');
      return offlineData.id;
    } catch (e) {
      print('保存管材检验数据失败: $e');
      rethrow;
    }
  }

  // 保存焊接完成数据
  Future<String> saveWeldingData(Map<String, dynamic> weldingParams,
      String weldingDataHex, List<String> base64Images) async {
    try {
      List<String> imagePaths = [];

      // 保存焊接相关图片
      for (int i = 0; i < base64Images.length; i++) {
        final imagePath = await _storageService.saveImage(
          base64Images[i],
          'welding',
          'welding_${DateTime.now().millisecondsSinceEpoch}_$i.jpg',
        );
        imagePaths.add(imagePath);
      }

      // 创建焊接数据记录
      final weldingData = OfflineWeldingData(
        id: _generateId(),
        projectId: _currentState.currentProjectId ?? '',
        userId: _currentState.currentUserId ?? '',
        deviceId: _bleService.connectedDevice?.id.toString() ?? '',
        weldingParams: weldingParams,
        weldingDataHex: weldingDataHex,
        timestamp: DateTime.now(),
        imagePaths: imagePaths,
      );

      await _storageService.saveWeldingData(weldingData);
      await _updatePendingCount();

      print('焊接数据已保存到离线存储');
      return weldingData.id;
    } catch (e) {
      print('保存焊接数据失败: $e');
      rethrow;
    }
  }

  // 自动同步数据到服务器
  Future<void> _autoSyncData() async {
    if (_currentState.isOfflineMode) {
      print('当前处于离线模式，暂停数据同步');
      return;
    }

    try {
      print('开始自动同步离线数据...');

      // 获取待上传的数据
      final pendingData = await _storageService.getPendingUploadData();
      final pendingWelding = await _storageService.getPendingWeldingData();

      int successCount = 0;

      // 同步离线数据
      for (final data in pendingData) {
        if (await _uploadOfflineData(data)) {
          await _storageService.markDataAsUploaded(data.id);
          successCount++;
        }
      }

      // 同步焊接数据
      for (final welding in pendingWelding) {
        if (await _uploadWeldingData(welding)) {
          await _storageService.markWeldingDataAsUploaded(welding.id);
          successCount++;
        }
      }

      print('数据同步完成，成功上传 $successCount 条记录');

      // 更新状态
      await _updatePendingCount();
      _updateState(_currentState.copyWith(
        lastSyncTime: DateTime.now(),
      ));
    } catch (e) {
      print('自动同步数据失败: $e');
    }
  }

  // 上传离线数据到服务器
  Future<bool> _uploadOfflineData(OfflineDataModel data) async {
    try {
      // 这里需要实现实际的网络上传逻辑
      print('上传离线数据: ${data.type} - ${data.id}');

      // 模拟网络请求
      await Future.delayed(Duration(milliseconds: 1000));

      return true;
    } catch (e) {
      print('上传离线数据失败: $e');
      return false;
    }
  }

  // 上传焊接数据到服务器
  Future<bool> _uploadWeldingData(OfflineWeldingData data) async {
    try {
      // 这里需要实现实际的网络上传逻辑
      print('上传焊接数据: ${data.id}');

      // 模拟网络请求
      await Future.delayed(Duration(milliseconds: 1000));

      return true;
    } catch (e) {
      print('上传焊接数据失败: $e');
      return false;
    }
  }

  // 手动同步数据
  Future<bool> manualSyncData() async {
    // 检查实际网络状态而不是模式状态
    final isOnline = await _networkService.checkNetworkStatus();
    if (!isOnline) {
      print('网络连接不可用，无法同步数据');
      return false;
    }

    // 暂时跳过模式检查，直接执行同步
    try {
      print('开始手动同步离线数据...');

      // 获取待上传的数据
      final pendingData = await _storageService.getPendingUploadData();
      final pendingWelding = await _storageService.getPendingWeldingData();

      int successCount = 0;

      // 同步离线数据
      for (final data in pendingData) {
        if (await _uploadOfflineData(data)) {
          await _storageService.markDataAsUploaded(data.id);
          successCount++;
        }
      }

      // 同步焊接数据
      for (final welding in pendingWelding) {
        if (await _uploadWeldingData(welding)) {
          await _storageService.markWeldingDataAsUploaded(welding.id);
          successCount++;
        }
      }

      print('数据同步完成，成功上传 $successCount 条记录');

      // 更新状态
      await _updatePendingCount();
      _updateState(_currentState.copyWith(
        lastSyncTime: DateTime.now(),
      ));

      return true;
    } catch (e) {
      print('手动同步数据失败: $e');
      return false;
    }
  }

  // 更新待上传数据计数
  Future<void> _updatePendingCount() async {
    final stats = await _storageService.getStorageStats();
    final totalPending =
        stats['pendingOfflineData']! + stats['pendingWeldingData']!;

    _updateState(_currentState.copyWith(
      pendingUploads: totalPending,
    ));
  }

  // 生成唯一ID
  String _generateId() {
    return '${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(9999)}';
  }

  // 获取存储统计信息
  Future<Map<String, int>> getStorageStats() async {
    return await _storageService.getStorageStats();
  }

  // 销毁资源
  void dispose() {
    _networkSubscription?.cancel();
    _stateController.close();
    _networkService.dispose();
  }
}
