import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:uuid/uuid.dart';
import '../models/weld_record_model.dart';
import '../utils/api_config.dart';

class WeldService {
  static final WeldService _instance = WeldService._internal();
  final Uuid _uuid = Uuid();

  factory WeldService() {
    return _instance;
  }

  WeldService._internal();

  Future<List<WeldRecord>> getPendingWeldRecords() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/pending_welds.json');

      if (!await file.exists()) {
        return [];
      }

      final jsonString = await file.readAsString();
      if (jsonString.isEmpty) {
        return [];
      }

      final List<dynamic> jsonList = jsonDecode(jsonString);
      return jsonList.map((json) => WeldRecord.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error loading pending records: $e');
      return [];
    }
  }

  Future<bool> saveRecords(List<WeldRecord> records) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/pending_welds.json');

      final jsonList = records.map((record) => record.toJson()).toList();
      await file.writeAsString(jsonEncode(jsonList));
      return true;
    } catch (e) {
      debugPrint('Error saving records: $e');
      return false;
    }
  }

  Future<bool> deletePendingWeldRecord(String id) async {
    try {
      final records = await getPendingWeldRecords();
      final updatedRecords =
          records.where((record) => record.id != id).toList();
      return await saveRecords(updatedRecords);
    } catch (e) {
      debugPrint('Error deleting record: $e');
      return false;
    }
  }

  Future<bool> uploadWeldRecord(WeldRecord record) async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/welds'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(record.toJson()),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        await deletePendingWeldRecord(record.id);
        return true;
      } else {
        debugPrint(
            'Failed to upload record: ${response.statusCode}, ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('Error uploading record: $e');
      return false;
    }
  }

  Future<Map<String, dynamic>> uploadAllPendingRecords() async {
    final records = await getPendingWeldRecords();
    if (records.isEmpty) {
      return {
        'success': true,
        'message': '没有待上传的记录',
        'total': 0,
        'uploaded': 0
      };
    }

    int successCount = 0;
    List<String> failedRecords = [];

    for (final record in records) {
      final success = await uploadWeldRecord(record);
      if (success) {
        successCount++;
      } else {
        failedRecords.add(record.jointId);
      }
    }

    final bool allSuccess = successCount == records.length;
    String message =
        allSuccess ? '所有记录上传成功' : '部分记录上传失败: ${failedRecords.join(', ')}';

    return {
      'success': allSuccess,
      'message': message,
      'total': records.length,
      'uploaded': successCount,
    };
  }

  Future<WeldRecord> createWeldRecord({
    required String jointId,
    required String projectName,
    required String projectId,
    required String processType,
    required String operatorName,
    required String deviceId,
    String? notes,
    bool hasImages = false,
    int imageCount = 0,
  }) async {
    final newRecord = WeldRecord(
      id: _uuid.v4(),
      jointId: jointId,
      projectName: projectName,
      projectId: projectId,
      processType: processType,
      operatorName: operatorName,
      deviceId: deviceId,
      createdAt: DateTime.now(),
      hasImages: hasImages,
      imageCount: imageCount,
      notes: notes,
    );

    final records = await getPendingWeldRecords();
    records.add(newRecord);
    await saveRecords(records);

    return newRecord;
  }
}
