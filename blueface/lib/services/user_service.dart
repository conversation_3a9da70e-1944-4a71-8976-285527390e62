import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/des_util.dart';

class UserService {
  static final UserService _instance = UserService._internal();
  static const String API_BASE_URL = 'http://121.40.60.17/apk';

  static const String KEY_TOKEN = 'token';
  static const String KEY_USER_ID = 'user_id';
  static const String KEY_USER_NAME = 'user_name';
  static const String KEY_LOGIN_TIME = 'login_time';
  static const String KEY_CURRENT_PROJECT = 'current_project';
  static const String KEY_LAST_SYNC_TIME = 'last_sync_time';

  static late SharedPreferences _prefs;

  factory UserService() => _instance;

  UserService._internal();

  // 初始化服务
  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // 用户登录
  Future<Map<String, dynamic>> login(String username, String password) async {
    try {
      print('登录信息:');
      print('用户名: $username');
      print('原始密码: $password');

      String encryptedPassword = DesUtil.encode(password, "qaz");
      print('加密后密码: $encryptedPassword');

      final requestBody = {"loginId": username, "password": encryptedPassword};
      print('请求体: ${json.encode(requestBody)}');

      var response = await http.post(
        Uri.parse('$API_BASE_URL/privilege/v1/auth/idpassword'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(requestBody),
      );

      print('响应状态码: ${response.statusCode}');
      print('响应内容: ${response.body}');

      if (response.statusCode == 200) {
        var responseData = json.decode(response.body);
        print('解析响应: $responseData');

        if (responseData['status'] == 200) {
          // 保存用户信息
          await saveUserInfo(
            token: responseData['result']['token'],
            userId: responseData['result']['userId'].toString(),
            userName: username,
          );

          return {
            'success': true,
            'message': '登录成功',
            'data': responseData['result'],
          };
        } else {
          return {
            'success': false,
            'message': responseData['message'] ?? '登录失败',
          };
        }
      } else {
        return {
          'success': false,
          'message': '网络错误: ${response.statusCode}',
        };
      }
    } catch (e) {
      print('登录错误: $e');
      return {
        'success': false,
        'message': '登录异常: $e',
      };
    }
  }

  // 保存用户信息
  Future<void> saveUserInfo({
    required String token,
    required String userId,
    String? userName,
  }) async {
    await _prefs.setString(KEY_TOKEN, token);
    await _prefs.setString(KEY_USER_ID, userId);
    if (userName != null) {
      await _prefs.setString(KEY_USER_NAME, userName);
    }
    await _prefs.setString(KEY_LOGIN_TIME, DateTime.now().toIso8601String());
  }

  // 检查用户是否已登录
  Future<bool> isLoggedIn() async {
    String? token = await getToken();
    return token != null && token.isNotEmpty;
  }

  // 获取令牌
  Future<String?> getToken() async {
    return _prefs.getString(KEY_TOKEN);
  }

  // 获取用户ID
  Future<String?> getUserId() async {
    return _prefs.getString(KEY_USER_ID);
  }

  // 获取用户名
  Future<String?> getUserName() async {
    return _prefs.getString(KEY_USER_NAME);
  }

  // 保存当前项目
  Future<void> saveCurrentProject(Map<String, dynamic> project) async {
    await _prefs.setString(KEY_CURRENT_PROJECT, json.encode(project));
  }

  // 获取当前项目
  Future<Map<String, dynamic>?> getCurrentProject() async {
    String? projectJson = _prefs.getString(KEY_CURRENT_PROJECT);
    if (projectJson != null && projectJson.isNotEmpty) {
      return json.decode(projectJson);
    }
    return null;
  }

  // 用户注销
  Future<void> logout() async {
    await _prefs.remove(KEY_TOKEN);
    await _prefs.remove(KEY_USER_ID);
    await _prefs.remove(KEY_USER_NAME);
    await _prefs.remove(KEY_LOGIN_TIME);
    // 可以选择是否清除当前项目信息
    // await _prefs.remove(KEY_CURRENT_PROJECT);
  }

  // 获取标准API请求头
  Future<Map<String, String>> getApiHeaders() async {
    String? token = await getToken();
    return {
      'token': token ?? '',
      'Content-Type': 'application/json',
      'appKey': '',
      'product': ''
    };
  }

  // 获取表单API请求头
  Future<Map<String, String>> getFormApiHeaders() async {
    String? token = await getToken();
    return {
      'token': token ?? '',
      'Content-Type': 'application/x-www-form-urlencoded',
      'appKey': '',
      'product': ''
    };
  }

  // 保存最后同步时间
  Future<void> saveLastSyncTime(String time) async {
    await _prefs.setString(KEY_LAST_SYNC_TIME, time);
  }

  // 获取最后同步时间
  Future<String?> getLastSyncTime() async {
    return _prefs.getString(KEY_LAST_SYNC_TIME);
  }
}
