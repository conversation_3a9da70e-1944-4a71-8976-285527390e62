import 'dart:convert';
import 'package:dio/dio.dart';
import '../models/welding_joint_model.dart';
import '../models/device_model.dart';
import '../models/user_model.dart';
import 'user_service.dart';

class QueryService {
  static final QueryService _instance = QueryService._internal();
  static const String API_BASE_URL = 'http://121.40.60.17/apk';

  final UserService _userService = UserService();
  final Dio _dio = Dio();

  factory QueryService() => _instance;

  QueryService._internal();

  // 通过焊口编号查询焊口信息
  Future<WeldingJoint> getWeldingJointByCode(String code) async {
    try {
      var headers = await _userService.getFormApiHeaders();

      var response = await _dio.get(
        '$API_BASE_URL/ugp/welding-joints/by-code/$code',
        options: Options(
          headers: headers,
          responseType: ResponseType.json,
        ),
      );

      if (response.statusCode == 200 && response.data['status'] == 200) {
        return WeldingJoint.fromJson(response.data['result']);
      } else {
        throw Exception('获取焊口信息失败: ${response.data['message'] ?? '未知错误'}');
      }
    } catch (e) {
      print('获取焊口信息错误: $e');
      throw Exception('获取焊口信息错误: $e');
    }
  }

  // 通过项目ID查询设备列表
  Future<List<Device>> getDevicesByProjectId(String projectId) async {
    try {
      var headers = await _userService.getFormApiHeaders();

      var response = await _dio.get(
        '$API_BASE_URL/ugp/devices/by-project/$projectId',
        options: Options(
          headers: headers,
          responseType: ResponseType.json,
        ),
      );

      if (response.statusCode == 200 && response.data['status'] == 200) {
        List<dynamic> devicesJson = response.data['result'] ?? [];
        return devicesJson.map((json) => Device.fromJson(json)).toList();
      } else {
        throw Exception('获取设备列表失败: ${response.data['message'] ?? '未知错误'}');
      }
    } catch (e) {
      print('获取设备列表错误: $e');
      throw Exception('获取设备列表错误: $e');
    }
  }

  // 通过设备序列号查询设备信息
  Future<Device> getDeviceBySerialNumber(String serialNumber) async {
    try {
      var headers = await _userService.getFormApiHeaders();

      var response = await _dio.get(
        '$API_BASE_URL/ugp/devices/by-serial/$serialNumber',
        options: Options(
          headers: headers,
          responseType: ResponseType.json,
        ),
      );

      if (response.statusCode == 200 && response.data['status'] == 200) {
        return Device.fromJson(response.data['result']);
      } else {
        throw Exception('获取设备信息失败: ${response.data['message'] ?? '未知错误'}');
      }
    } catch (e) {
      print('获取设备信息错误: $e');
      throw Exception('获取设备信息错误: $e');
    }
  }

  // 通过用户ID查询用户信息
  Future<User> getUserById(String userId) async {
    try {
      var headers = await _userService.getFormApiHeaders();

      var response = await _dio.get(
        '$API_BASE_URL/ugp/users/$userId',
        options: Options(
          headers: headers,
          responseType: ResponseType.json,
        ),
      );

      if (response.statusCode == 200 && response.data['status'] == 200) {
        return User.fromJson(response.data['result']);
      } else {
        throw Exception('获取用户信息失败: ${response.data['message'] ?? '未知错误'}');
      }
    } catch (e) {
      print('获取用户信息错误: $e');
      throw Exception('获取用户信息错误: $e');
    }
  }

  // 通过用户名查询用户信息
  Future<User> getUserByUsername(String username) async {
    try {
      var headers = await _userService.getFormApiHeaders();

      var response = await _dio.get(
        '$API_BASE_URL/ugp/users/by-username/$username',
        options: Options(
          headers: headers,
          responseType: ResponseType.json,
        ),
      );

      if (response.statusCode == 200 && response.data['status'] == 200) {
        return User.fromJson(response.data['result']);
      } else {
        throw Exception('获取用户信息失败: ${response.data['message'] ?? '未知错误'}');
      }
    } catch (e) {
      print('获取用户信息错误: $e');
      throw Exception('获取用户信息错误: $e');
    }
  }

  // 生成焊口二维码数据
  Future<String> generateWeldingJointQRCode(String jointCode) async {
    try {
      var headers = await _userService.getFormApiHeaders();

      var response = await _dio.post(
        '$API_BASE_URL/ugp/qrcode/generate',
        data: {
          'type': 'welding_joint',
          'code': jointCode,
        },
        options: Options(
          headers: headers,
          responseType: ResponseType.json,
        ),
      );

      if (response.statusCode == 200 && response.data['status'] == 200) {
        return response.data['result']['qrcodeData'];
      } else {
        throw Exception('生成二维码失败: ${response.data['message'] ?? '未知错误'}');
      }
    } catch (e) {
      print('生成二维码错误: $e');
      throw Exception('生成二维码错误: $e');
    }
  }
}
