import 'package:flutter/material.dart';
import '../services/offline_mode_service.dart';
import '../services/offline_storage_service.dart';
import '../models/offline_data_model.dart';
import 'dart:async';

class OfflineDataScreen extends StatefulWidget {
  @override
  _OfflineDataScreenState createState() => _OfflineDataScreenState();
}

class _OfflineDataScreenState extends State<OfflineDataScreen>
    with TickerProviderStateMixin {
  final OfflineModeService _offlineModeService = OfflineModeService();
  final OfflineStorageService _storageService = OfflineStorageService();

  late TabController _tabController;

  List<OfflineDataModel> _offlineData = [];
  List<OfflineWeldingData> _weldingData = [];
  Map<String, int> _storageStats = {};
  OfflineModeState _offlineState = OfflineModeState(
    isOfflineMode: false,
    pendingUploads: 0,
  );

  bool _isLoading = true;
  bool _isSyncing = false;

  StreamSubscription? _offlineStateSubscription;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _offlineStateSubscription?.cancel();
    super.dispose();
  }

  Future<void> _initializeData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 初始化服务
      await _offlineModeService.initialize();

      // 监听离线状态变化
      _offlineStateSubscription =
          _offlineModeService.stateStream.listen((state) {
        setState(() {
          _offlineState = state;
        });
      });

      // 加载数据
      await _loadAllData();
    } catch (e) {
      print('初始化离线数据界面失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('初始化失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadAllData() async {
    try {
      final offlineData = await _storageService.getAllOfflineData();
      final weldingData = await _storageService.getAllWeldingData();
      final stats = await _storageService.getStorageStats();

      setState(() {
        _offlineData = offlineData;
        _weldingData = weldingData;
        _storageStats = stats;
      });
    } catch (e) {
      print('加载离线数据失败: $e');
    }
  }

  Future<void> _syncData() async {
    if (_offlineState.isOfflineMode) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('当前处于离线模式，无法同步数据'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isSyncing = true;
    });

    try {
      final result = await _offlineModeService.manualSyncData();

      if (result) {
        await _loadAllData();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('数据同步成功'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('数据同步失败'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('同步过程中出错: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isSyncing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('离线数据管理'),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: '概览'),
            Tab(text: '离线数据'),
            Tab(text: '焊接数据'),
          ],
        ),
        actions: [
          if (!_offlineState.isOfflineMode && _offlineState.pendingUploads > 0)
            IconButton(
              icon: _isSyncing
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Icon(Icons.sync),
              onPressed: _isSyncing ? null : _syncData,
              tooltip: '同步数据',
            ),
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadAllData,
            tooltip: '刷新',
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildOfflineDataTab(),
                _buildWeldingDataTab(),
              ],
            ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 状态卡片
          Card(
            color: _offlineState.isOfflineMode
                ? Colors.orange.shade50
                : Colors.green.shade50,
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _offlineState.isOfflineMode
                            ? Icons.wifi_off
                            : Icons.wifi,
                        color: _offlineState.isOfflineMode
                            ? Colors.orange
                            : Colors.green,
                        size: 32,
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _offlineState.isOfflineMode ? '离线模式' : '在线模式',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: _offlineState.isOfflineMode
                                    ? Colors.orange[700]
                                    : Colors.green[700],
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              _offlineState.isOfflineMode
                                  ? '数据将保存到本地，网络恢复后自动同步'
                                  : '网络连接正常，数据可实时同步',
                              style: TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  if (_offlineState.lastSyncTime != null) ...[
                    SizedBox(height: 12),
                    Text(
                      '上次同步时间: ${_formatDateTime(_offlineState.lastSyncTime!)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          SizedBox(height: 16),

          // 统计卡片
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  '离线数据',
                  '${_storageStats['totalOfflineData'] ?? 0}',
                  '待上传: ${_storageStats['pendingOfflineData'] ?? 0}',
                  Icons.storage,
                  Colors.blue,
                ),
              ),
              SizedBox(width: 8),
              Expanded(
                child: _buildStatCard(
                  '焊接数据',
                  '${_storageStats['totalWeldingData'] ?? 0}',
                  '待上传: ${_storageStats['pendingWeldingData'] ?? 0}',
                  Icons.build,
                  Colors.orange,
                ),
              ),
            ],
          ),

          SizedBox(height: 16),

          // 待上传数据总计
          if (_offlineState.pendingUploads > 0) ...[
            Card(
              color: Colors.red.shade50,
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.upload, color: Colors.red),
                        SizedBox(width: 8),
                        Text(
                          '待上传数据',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text(
                      '共有 ${_offlineState.pendingUploads} 条数据待上传到服务器',
                      style: TextStyle(fontSize: 14),
                    ),
                    if (!_offlineState.isOfflineMode) ...[
                      SizedBox(height: 12),
                      ElevatedButton.icon(
                        onPressed: _isSyncing ? null : _syncData,
                        icon:
                            Icon(_isSyncing ? Icons.hourglass_top : Icons.sync),
                        label: Text(_isSyncing ? '同步中...' : '立即同步'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOfflineDataTab() {
    final pendingData = _offlineData.where((data) => !data.isUploaded).toList();
    final uploadedData = _offlineData.where((data) => data.isUploaded).toList();

    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          TabBar(
            tabs: [
              Tab(text: '待上传 (${pendingData.length})'),
              Tab(text: '已上传 (${uploadedData.length})'),
            ],
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildDataList(pendingData, false),
                _buildDataList(uploadedData, true),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeldingDataTab() {
    final pendingData = _weldingData.where((data) => !data.isUploaded).toList();
    final uploadedData = _weldingData.where((data) => data.isUploaded).toList();

    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          TabBar(
            tabs: [
              Tab(text: '待上传 (${pendingData.length})'),
              Tab(text: '已上传 (${uploadedData.length})'),
            ],
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildWeldingDataList(pendingData, false),
                _buildWeldingDataList(uploadedData, true),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataList(List<OfflineDataModel> dataList, bool isUploaded) {
    if (dataList.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isUploaded ? Icons.check_circle : Icons.storage,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              isUploaded ? '暂无已上传数据' : '暂无待上传数据',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: dataList.length,
      itemBuilder: (context, index) {
        final data = dataList[index];
        return _buildOfflineDataCard(data);
      },
    );
  }

  Widget _buildWeldingDataList(
      List<OfflineWeldingData> dataList, bool isUploaded) {
    if (dataList.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isUploaded ? Icons.check_circle : Icons.build,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              isUploaded ? '暂无已上传焊接数据' : '暂无待上传焊接数据',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: dataList.length,
      itemBuilder: (context, index) {
        final data = dataList[index];
        return _buildWeldingDataCard(data);
      },
    );
  }

  Widget _buildOfflineDataCard(OfflineDataModel data) {
    final typeInfo = _getTypeInfo(data.type);

    return Card(
      margin: EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: typeInfo['color'],
          child: Icon(
            typeInfo['icon'],
            color: Colors.white,
          ),
        ),
        title: Text(typeInfo['title']),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('创建时间: ${_formatDateTime(data.createdAt)}'),
            if (data.imagePaths.isNotEmpty)
              Text('图片数量: ${data.imagePaths.length}'),
            if (data.isUploaded && data.uploadedAt != null)
              Text('上传时间: ${_formatDateTime(data.uploadedAt!)}'),
          ],
        ),
        trailing: data.isUploaded
            ? Icon(Icons.check_circle, color: Colors.green)
            : Icon(Icons.upload_outlined, color: Colors.orange),
        onTap: () => _showDataDetails(data),
      ),
    );
  }

  Widget _buildWeldingDataCard(OfflineWeldingData data) {
    return Card(
      margin: EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.orange,
          child: Icon(Icons.build, color: Colors.white),
        ),
        title: Text('焊接数据'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('时间: ${_formatDateTime(data.timestamp)}'),
            Text('项目ID: ${data.projectId}'),
            Text('用户ID: ${data.userId}'),
            if (data.imagePaths.isNotEmpty)
              Text('图片数量: ${data.imagePaths.length}'),
          ],
        ),
        trailing: data.isUploaded
            ? Icon(Icons.check_circle, color: Colors.green)
            : Icon(Icons.upload_outlined, color: Colors.orange),
        onTap: () => _showWeldingDataDetails(data),
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, String subtitle, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color),
                SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Map<String, dynamic> _getTypeInfo(String type) {
    switch (type) {
      case 'face':
        return {
          'title': '人脸校验',
          'icon': Icons.face,
          'color': Colors.blue,
        };
      case 'device':
        return {
          'title': '设备检验',
          'icon': Icons.devices,
          'color': Colors.green,
        };
      case 'pipe':
        return {
          'title': '管材检验',
          'icon': Icons.construction,
          'color': Colors.purple,
        };
      default:
        return {
          'title': '其他数据',
          'icon': Icons.storage,
          'color': Colors.grey,
        };
    }
  }

  void _showDataDetails(OfflineDataModel data) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_getTypeInfo(data.type)['title']),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('ID: ${data.id}'),
              SizedBox(height: 8),
              Text('创建时间: ${_formatDateTime(data.createdAt)}'),
              if (data.isUploaded && data.uploadedAt != null) ...[
                SizedBox(height: 8),
                Text('上传时间: ${_formatDateTime(data.uploadedAt!)}'),
              ],
              SizedBox(height: 8),
              Text('状态: ${data.isUploaded ? "已上传" : "待上传"}'),
              if (data.imagePaths.isNotEmpty) ...[
                SizedBox(height: 8),
                Text('图片数量: ${data.imagePaths.length}'),
              ],
              SizedBox(height: 8),
              Text('数据内容:'),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(8),
                margin: EdgeInsets.only(top: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  data.data.toString(),
                  style: TextStyle(fontSize: 12),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showWeldingDataDetails(OfflineWeldingData data) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('焊接数据详情'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('ID: ${data.id}'),
              SizedBox(height: 8),
              Text('项目ID: ${data.projectId}'),
              SizedBox(height: 8),
              Text('用户ID: ${data.userId}'),
              SizedBox(height: 8),
              Text('设备ID: ${data.deviceId}'),
              SizedBox(height: 8),
              Text('时间: ${_formatDateTime(data.timestamp)}'),
              SizedBox(height: 8),
              Text('状态: ${data.isUploaded ? "已上传" : "待上传"}'),
              if (data.imagePaths.isNotEmpty) ...[
                SizedBox(height: 8),
                Text('图片数量: ${data.imagePaths.length}'),
              ],
              SizedBox(height: 8),
              Text('焊接参数:'),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(8),
                margin: EdgeInsets.only(top: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  data.weldingParams.toString(),
                  style: TextStyle(fontSize: 12),
                ),
              ),
              SizedBox(height: 8),
              Text('160字节数据:'),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(8),
                margin: EdgeInsets.only(top: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  data.weldingDataHex.length > 100
                      ? data.weldingDataHex.substring(0, 100) + '...'
                      : data.weldingDataHex,
                  style: TextStyle(fontSize: 10, fontFamily: 'monospace'),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('关闭'),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
