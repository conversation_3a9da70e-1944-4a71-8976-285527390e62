import 'package:flutter/material.dart';
import '../services/welding_joint_number_service.dart';
import '../services/location_service.dart';

class WeldingJointManagementScreen extends StatefulWidget {
  @override
  _WeldingJointManagementScreenState createState() =>
      _WeldingJointManagementScreenState();
}

class _WeldingJointManagementScreenState
    extends State<WeldingJointManagementScreen> {
  final WeldingJointNumberService _jointNumberService =
      WeldingJointNumberService();

  String? _currentJointNumber;
  List<String> _todayJointNumbers = [];
  bool _isLoading = false;
  String _statusMessage = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _currentJointNumber = await _jointNumberService.getCurrentJointNumber();
      _todayJointNumbers = await _jointNumberService.getTodayJointNumbers();
    } catch (e) {
      _statusMessage = '加载数据失败: $e';
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _generateNewJointNumber() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在生成焊口编号...';
    });

    try {
      final newJointNumber =
          await _jointNumberService.generateWeldingJointNumber();
      _statusMessage = '成功生成焊口编号: $newJointNumber';
      await _loadData();
    } catch (e) {
      _statusMessage = '生成焊口编号失败: $e';
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testWeldingResult(int statusCode) async {
    if (_currentJointNumber == null) {
      setState(() {
        _statusMessage = '没有当前焊口编号，请先生成';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = '正在处理焊接结果...';
    });

    try {
      bool success = await _jointNumberService.handleWeldingResult(statusCode);
      if (success) {
        final statusDescription =
            _jointNumberService.getStatusDescription(statusCode);
        _statusMessage = '焊接结果处理成功: $statusDescription';
        await _loadData();
      } else {
        _statusMessage = '焊接结果处理失败';
      }
    } catch (e) {
      _statusMessage = '处理焊接结果失败: $e';
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testPLCWrite() async {
    if (_currentJointNumber == null) {
      setState(() {
        _statusMessage = '没有当前焊口编号，请先生成';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = '正在写入PLC...';
    });

    try {
      bool success =
          await _jointNumberService.writeJointNumberToPLC(_currentJointNumber!);
      if (success) {
        _statusMessage = '成功写入PLC寄存器';
      } else {
        _statusMessage = 'PLC写入失败（可能是蓝牙未连接）';
      }
    } catch (e) {
      _statusMessage = 'PLC写入异常: $e';
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testWeldingStartSignal() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在发送焊接开始信号...';
    });

    try {
      bool success = await _jointNumberService.writeWeldingStartSignal();
      if (success) {
        _statusMessage = '焊接开始信号发送成功（VW2250置1）';
      } else {
        _statusMessage = '焊接开始信号发送失败（可能是蓝牙未连接）';
      }
    } catch (e) {
      _statusMessage = '焊接开始信号发送异常: $e';
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testSendUserId() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在发送用户ID到焊机...';
    });

    try {
      // 使用测试用户ID
      String testUserId = 'test_user';
      bool success =
          await _jointNumberService.writeUserIdToWeldingMachine(testUserId);
      if (success) {
        _statusMessage = '用户ID发送成功: $testUserId';
      } else {
        _statusMessage = '用户ID发送失败（可能是蓝牙未连接）';
      }
    } catch (e) {
      _statusMessage = '用户ID发送异常: $e';
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testLocationWrite() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在检查位置权限...';
    });

    try {
      // 首先检查位置权限状态
      final locationService = LocationService();
      String permissionStatus =
          await locationService.getLocationPermissionStatus();

      setState(() {
        _statusMessage = '权限状态: $permissionStatus\n正在获取位置信息...';
      });

      bool success = await _jointNumberService.writeLocationToWeldingMachine();
      if (success) {
        _statusMessage = '位置信息写入成功！\n权限状态: $permissionStatus\n经度、纬度、海拔已写入PLC';
      } else {
        _statusMessage =
            '位置信息写入失败\n权限状态: $permissionStatus\n可能原因：蓝牙未连接、GPS获取失败或权限不足';
      }
    } catch (e) {
      _statusMessage = '位置信息写入异常: $e';
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testFullProcess() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在执行完整流程测试...';
    });

    try {
      // 1. 生成焊口编号
      String jointNumber =
          await _jointNumberService.generateWeldingJointNumber();
      if (jointNumber.isEmpty) {
        _statusMessage = '完整流程测试失败：生成焊口编号失败';
        return;
      }

      setState(() {
        _statusMessage = '步骤1/5：焊口编号生成成功 ($jointNumber)';
      });
      await Future.delayed(Duration(milliseconds: 500));

      // 2. 写入焊口编号到PLC
      bool writeSuccess =
          await _jointNumberService.writeJointNumberToPLC(jointNumber);
      if (!writeSuccess) {
        _statusMessage = '完整流程测试失败：写入焊口编号到PLC失败';
        return;
      }

      setState(() {
        _statusMessage = '步骤2/5：焊口编号写入PLC成功';
      });
      await Future.delayed(Duration(milliseconds: 500));

      // 3. 发送焊接开始信号
      bool startSignalSuccess =
          await _jointNumberService.writeWeldingStartSignal();
      if (!startSignalSuccess) {
        _statusMessage = '完整流程测试失败：发送焊接开始信号失败';
        return;
      }

      setState(() {
        _statusMessage = '步骤3/5：焊接开始信号发送成功';
      });
      await Future.delayed(Duration(milliseconds: 500));

      // 4. 写入位置信息
      setState(() {
        _statusMessage = '步骤4/5：正在获取并写入位置信息...';
      });
      bool locationSuccess =
          await _jointNumberService.writeLocationToWeldingMachine();

      setState(() {
        if (locationSuccess) {
          _statusMessage = '步骤4/5：位置信息写入成功';
        } else {
          _statusMessage = '步骤4/5：位置信息写入失败（继续流程）';
        }
      });
      await Future.delayed(Duration(milliseconds: 500));

      // 5. 完成
      setState(() {
        _statusMessage = '完整流程测试完成！\n' +
            '✓ 焊口编号：$jointNumber\n' +
            '✓ PLC写入：成功\n' +
            '✓ 开始信号：成功\n' +
            '${locationSuccess ? "✓" : "✗"} 位置信息：${locationSuccess ? "成功" : "失败"}';
      });

      // 刷新数据
      await _loadData();
    } catch (e) {
      _statusMessage = '完整流程测试异常: $e';
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testDataUpload() async {
    if (_currentJointNumber == null) {
      setState(() {
        _statusMessage = '没有当前焊口编号，请先生成';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = '正在测试数据上传...';
    });

    try {
      // 先将当前焊口设置为成功状态，才能上传
      await _jointNumberService
          .handleWeldingResult(WeldingJointNumberService.WELDING_SUCCESS);

      // 执行数据上传流程
      bool success = await _jointNumberService
          .handleWeldingDataUpload(_currentJointNumber!);

      if (success) {
        // 检查网络状态以显示正确的成功消息
        bool isConnected = await _jointNumberService.isNetworkConnected();
        if (isConnected) {
          _statusMessage = '✅ 数据上传测试成功！\n'
              '模式：联网上传\n'
              '焊口编号：$_currentJointNumber\n'
              '服务器同步：完成\n'
              'VW3202信号：已写入焊机';
        } else {
          _statusMessage = '✅ 数据存储测试成功！\n'
              '模式：断网存储\n'
              '焊口编号：$_currentJointNumber\n'
              '本地保存：完成\n'
              'VW3204信号：已写入焊机';
        }
      } else {
        _statusMessage = '❌ 数据上传测试失败\n可能原因：蓝牙未连接或PLC写入失败';
      }
    } catch (e) {
      _statusMessage = '数据上传测试异常: $e';
    }

    setState(() {
      _isLoading = false;
    });
  }

  void _openDataUploadScreen() {
    Navigator.pushNamed(
      context,
      '/dataUpload',
      arguments: _currentJointNumber,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('焊口编号管理'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 当前焊口编号卡片
                  Card(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '当前焊口编号',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          SizedBox(height: 8),
                          Text(
                            _currentJointNumber ?? '暂无',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: _currentJointNumber != null
                                  ? Colors.blue
                                  : Colors.grey,
                            ),
                          ),
                          if (_currentJointNumber != null) ...[
                            SizedBox(height: 8),
                            FutureBuilder<int>(
                              future: _jointNumberService
                                  .getWeldingStatus(_currentJointNumber!),
                              builder: (context, snapshot) {
                                if (snapshot.hasData) {
                                  final status = snapshot.data!;
                                  final statusDescription = _jointNumberService
                                      .getStatusDescription(status);
                                  return Text(
                                    '状态: $statusDescription',
                                    style: TextStyle(
                                      color: status ==
                                              WeldingJointNumberService
                                                  .WELDING_SUCCESS
                                          ? Colors.green
                                          : status == 0
                                              ? Colors.orange
                                              : Colors.red,
                                    ),
                                  );
                                }
                                return Text('状态: 加载中...');
                              },
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: 16),

                  // 操作按钮
                  Card(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '操作',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          SizedBox(height: 16),

                          // 生成新编号按钮
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _generateNewJointNumber,
                              icon: Icon(Icons.add),
                              label: Text('生成新焊口编号'),
                            ),
                          ),

                          SizedBox(height: 8),

                          // PLC写入测试按钮
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _currentJointNumber != null
                                  ? _testPLCWrite
                                  : null,
                              icon: Icon(Icons.send),
                              label: Text('测试PLC写入焊口编号'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                              ),
                            ),
                          ),

                          SizedBox(height: 8),

                          // 焊接开始信号测试按钮
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _testWeldingStartSignal,
                              icon: Icon(Icons.play_arrow),
                              label: Text('测试焊接开始信号（VW2250置1）'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                              ),
                            ),
                          ),

                          SizedBox(height: 8),

                          // 用户ID发送测试按钮
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _testSendUserId,
                              icon: Icon(Icons.person_add),
                              label: Text('测试发送用户ID到焊机'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.purple,
                              ),
                            ),
                          ),

                          SizedBox(height: 8),

                          // 位置信息写入测试按钮
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _testLocationWrite,
                              icon: Icon(Icons.location_on),
                              label: Text('测试位置信息写入'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.teal,
                              ),
                            ),
                          ),

                          SizedBox(height: 8),

                          // 完整流程测试按钮
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _testFullProcess,
                              icon: Icon(Icons.play_circle_filled),
                              label: Text('测试完整流程（生成编号+位置+开始信号）'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.indigo,
                              ),
                            ),
                          ),

                          SizedBox(height: 8),

                          // 数据上传测试按钮
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _currentJointNumber != null
                                  ? _testDataUpload
                                  : null,
                              icon: Icon(Icons.cloud_upload),
                              label: Text('测试数据上传（联网/断网模式）'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.deepPurple,
                              ),
                            ),
                          ),

                          SizedBox(height: 8),

                          // 跳转到数据上传界面按钮
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _openDataUploadScreen,
                              icon: Icon(Icons.open_in_new),
                              label: Text('打开数据上传界面'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.cyan,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: 16),

                  // 焊接结果测试
                  Card(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '焊接结果测试',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          SizedBox(height: 16),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              ElevatedButton(
                                onPressed: _currentJointNumber != null
                                    ? () => _testWeldingResult(
                                        WeldingJointNumberService
                                            .WELDING_SUCCESS)
                                    : null,
                                style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green),
                                child: Text('焊接成功'),
                              ),
                              ElevatedButton(
                                onPressed: _currentJointNumber != null
                                    ? () => _testWeldingResult(
                                        WeldingJointNumberService
                                            .HEAT_ABSORPTION_FAILED)
                                    : null,
                                style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red),
                                child: Text('吸热失败'),
                              ),
                              ElevatedButton(
                                onPressed: _currentJointNumber != null
                                    ? () => _testWeldingResult(
                                        WeldingJointNumberService
                                            .CURLING_FAILED)
                                    : null,
                                style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red),
                                child: Text('卷边失败'),
                              ),
                              ElevatedButton(
                                onPressed: _currentJointNumber != null
                                    ? () => _testWeldingResult(
                                        WeldingJointNumberService
                                            .COOLING_FAILED)
                                    : null,
                                style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red),
                                child: Text('冷却失败'),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: 16),

                  // 今日焊口编号列表
                  Card(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '今日焊口编号 (${_todayJointNumbers.length})',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          SizedBox(height: 16),
                          if (_todayJointNumbers.isEmpty)
                            Text('暂无今日焊口编号')
                          else
                            ...(_todayJointNumbers
                                .map(
                                  (jointNumber) => FutureBuilder<int>(
                                    future: _jointNumberService
                                        .getWeldingStatus(jointNumber),
                                    builder: (context, snapshot) {
                                      final status = snapshot.data ?? 0;
                                      final statusDescription =
                                          _jointNumberService
                                              .getStatusDescription(status);
                                      return ListTile(
                                        title: Text(jointNumber),
                                        subtitle:
                                            Text('状态: $statusDescription'),
                                        trailing: Icon(
                                          status ==
                                                  WeldingJointNumberService
                                                      .WELDING_SUCCESS
                                              ? Icons.check_circle
                                              : status == 0
                                                  ? Icons.radio_button_unchecked
                                                  : Icons.error,
                                          color: status ==
                                                  WeldingJointNumberService
                                                      .WELDING_SUCCESS
                                              ? Colors.green
                                              : status == 0
                                                  ? Colors.orange
                                                  : Colors.red,
                                        ),
                                      );
                                    },
                                  ),
                                )
                                .toList()),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: 16),

                  // 状态消息
                  if (_statusMessage.isNotEmpty)
                    Card(
                      color: Colors.blue.shade50,
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '状态信息',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            SizedBox(height: 8),
                            Text(_statusMessage),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
    );
  }
}
