import 'package:flutter/material.dart';
import '../services/user_service.dart';
import '../services/offline_mode_service.dart';
import '../services/network_service.dart';
import '../services/welding_joint_number_service.dart';
import 'package:fluttertoast/fluttertoast.dart';

class LoginScreen extends StatefulWidget {
  final Function(bool) onLoginResult;

  LoginScreen({Key? key, required this.onLoginResult}) : super(key: key);

  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController(text: 'huanghao');
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  bool _isCheckingNetwork = true;
  bool _isOnline = false;

  final UserService _userService = UserService();
  final OfflineModeService _offlineModeService = OfflineModeService();
  final NetworkService _networkService = NetworkService();

  // 🔧 懒加载GPS相关服务，避免启动时阻塞
  WeldingJointNumberService? _jointNumberService;
  WeldingJointNumberService get _jointNumberServiceInstance {
    _jointNumberService ??= WeldingJointNumberService();
    return _jointNumberService!;
  }

  @override
  void initState() {
    super.initState();
    _usernameController.text = 'huanghao';
    _passwordController.text = '123456'; // 默认输入密码方便测试
    _checkNetworkStatus();
  }

  void _showToast(String message) {
    Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 2,
        backgroundColor: Colors.black87,
        textColor: Colors.white,
        fontSize: 16.0);
  }

  // 检查网络状态
  Future<void> _checkNetworkStatus() async {
    try {
      await _networkService.initialize();
      final isOnline = await _networkService.checkNetworkStatus();
      setState(() {
        _isOnline = isOnline;
        _isCheckingNetwork = false;
      });
    } catch (e) {
      print('检查网络状态失败: $e');
      setState(() {
        _isOnline = false;
        _isCheckingNetwork = false;
      });
    }
  }

  // 发送用户ID到焊机
  Future<void> _sendUserIdToWeldingMachine(String userId) async {
    try {
      _showToast("正在发送用户ID到焊机...");

      bool success =
          await _jointNumberServiceInstance.writeUserIdToWeldingMachine(userId);

      if (success) {
        _showToast("用户ID已发送到焊机");
        print('用户ID已成功发送到焊机: $userId');
      } else {
        _showToast("用户ID发送失败（可能是蓝牙未连接）");
        print('用户ID发送失败，可能是蓝牙未连接或其他错误');
      }
    } catch (e) {
      print('发送用户ID到焊机异常: $e');
      _showToast("发送用户ID异常: $e");
    }
  }

  // 进入离线模式
  Future<void> _enterOfflineMode() async {
    try {
      _showToast("正在进入离线模式...");

      // 初始化离线模式服务
      await _offlineModeService.initialize();

      // 设置为离线模式
      await _offlineModeService.setOfflineMode(true);

      // 设置离线模式状态
      await _offlineModeService.setCurrentUserAndProject(
        'offline_user', // 离线模式默认用户
        null, // 项目ID稍后选择
      );

      _showToast("已进入离线模式");

      // 导航到主页
      Navigator.of(context).pushNamedAndRemoveUntil(
        '/home',
        (route) => false,
      );

      // 通知应用已登录（离线模式）
      widget.onLoginResult(true);
    } catch (e) {
      print('进入离线模式失败: $e');
      _showToast("进入离线模式失败: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('进入离线模式失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final username = _usernameController.text;
      final password = _passwordController.text;

      print('开始登录流程...');
      _showToast("开始登录...");

      var result = await _userService.login(username, password);
      print('登录API响应结果: $result');

      if (result['success']) {
        print('登录成功，设置为在线模式...');

        // 初始化离线模式服务并设置为在线模式
        await _offlineModeService.initialize();
        await _offlineModeService.setOfflineMode(false);

        _showToast("登录成功，准备跳转...");

        // 🎯 登录成功后发送用户ID到焊机
        await _sendUserIdToWeldingMachine(username);

        // 先导航再调用回调，避免状态更新导致页面重建
        print('准备跳转到主页...');

        // 确保登录状态被更新，然后再导航
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // 在下一帧渲染后执行导航
          if (mounted) {
            print('导航到主页');
            Navigator.of(context)
                .pushNamedAndRemoveUntil('/home', (route) => false // 移除所有之前的路由
                    );
          }
        });

        // 调用回调通知App状态更新
        widget.onLoginResult(true);
      } else {
        print('登录失败: ${result['message']}');
        _showToast("登录失败: ${result['message']}");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('登录失败: ${result['message']}')),
        );
      }
    } catch (e) {
      print('登录错误: $e');
      _showToast("登录错误: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('登录错误: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Logo或标题
                  Icon(
                    Icons.lock_outline,
                    size: 80,
                    color: Colors.blue,
                  ),
                  SizedBox(height: 16),

                  // 网络状态提示
                  if (_isCheckingNetwork)
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('检查网络状态...', style: TextStyle(fontSize: 12)),
                        ],
                      ),
                    )
                  else
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        color: _isOnline
                            ? Colors.green.shade50
                            : Colors.orange.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: _isOnline ? Colors.green : Colors.orange,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _isOnline ? Icons.wifi : Icons.wifi_off,
                            color: _isOnline ? Colors.green : Colors.orange,
                            size: 16,
                          ),
                          SizedBox(width: 8),
                          Text(
                            _isOnline ? '网络连接正常' : '网络连接不可用',
                            style: TextStyle(
                              color: _isOnline
                                  ? Colors.green[700]
                                  : Colors.orange[700],
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                  SizedBox(height: 24),

                  // 用户名输入框
                  TextFormField(
                    controller: _usernameController,
                    decoration: InputDecoration(
                      labelText: '用户名',
                      prefixIcon: Icon(Icons.person),
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '请输入用户名';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 16),
                  // 密码输入框
                  TextFormField(
                    controller: _passwordController,
                    decoration: InputDecoration(
                      labelText: '密码',
                      prefixIcon: Icon(Icons.lock),
                      border: OutlineInputBorder(),
                    ),
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '请输入密码';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 24),

                  // 登录按钮
                  ElevatedButton(
                    onPressed: _isLoading ? null : _login,
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: _isLoading
                        ? SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            '登录',
                            style: TextStyle(fontSize: 16),
                          ),
                  ),

                  SizedBox(height: 16),

                  // 分割线
                  Row(
                    children: [
                      Expanded(child: Divider()),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          '或',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ),
                      Expanded(child: Divider()),
                    ],
                  ),

                  SizedBox(height: 16),

                  // 离线模式入口按钮
                  OutlinedButton.icon(
                    onPressed: _isLoading ? null : _enterOfflineMode,
                    icon: Icon(Icons.wifi_off),
                    label: Text('进入离线模式'),
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      foregroundColor: Colors.orange,
                      side: BorderSide(color: Colors.orange),
                    ),
                  ),

                  SizedBox(height: 12),

                  // 离线模式说明
                  Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info_outline,
                                size: 16, color: Colors.grey[600]),
                            SizedBox(width: 8),
                            Text(
                              '离线模式说明',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8),
                        Text(
                          '• 可在无网络环境下使用基础功能\n'
                          '• 数据将保存到本地，网络恢复后自动同步\n'
                          '• 支持蓝牙连接、数据记录等核心操作',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
