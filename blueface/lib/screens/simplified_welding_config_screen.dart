import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/bluetooth_service.dart';
import '../services/command_service.dart';
import '../services/location_service.dart';
import '../services/welding_joint_number_service.dart';
import '../services/simplified_welding_record_service.dart';
import '../models/simplified_welding_record.dart';
import 'dart:async';

/// 简化的焊接配置页面
/// 核心功能：专注于焊接操作，移除复杂的项目管理
class SimplifiedWeldingConfigScreen extends StatefulWidget {
  @override
  _SimplifiedWeldingConfigScreenState createState() => _SimplifiedWeldingConfigScreenState();
}

class _SimplifiedWeldingConfigScreenState extends State<SimplifiedWeldingConfigScreen> {
  final BleService _bleService = BleService();
  final CommandService _commandService = CommandService();
  final WeldingJointNumberService _jointNumberService = WeldingJointNumberService();
  final SimplifiedWeldingRecordService _recordService = SimplifiedWeldingRecordService();
  final LocationService _locationService = LocationService();

  bool _isLoading = true;
  String _statusMessage = '';
  
  // 核心数据
  String _currentJointNumber = '';
  Map<String, String> _deviceInfo = {};
  String _weldingData = '';
  Map<String, dynamic>? _parsedWeldingData;
  String _locationData = '';
  
  // 焊接状态
  bool _isWeldingInProgress = false;
  bool _isWeldingCompleted = false;
  SimplifiedWeldingRecord? _currentRecord;

  StreamSubscription? _receivedDataSubscription;

  @override
  void initState() {
    super.initState();
    _initializeScreen();
    _setupDataListener();
  }

  @override
  void dispose() {
    _receivedDataSubscription?.cancel();
    super.dispose();
  }

  /// 初始化页面
  Future<void> _initializeScreen() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在初始化...';
    });

    try {
      // 加载当前焊口编号
      await _loadCurrentJointNumber();
      
      // 检查连接状态
      if (_bleService.isConnected) {
        await _loadAllData();
      } else {
        setState(() {
          _statusMessage = '请先连接焊机设备';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '初始化失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 设置数据监听
  void _setupDataListener() {
    _receivedDataSubscription = _bleService.receivedDataStream.listen((data) {
      if (data.startsWith('【重要】焊机编号:')) {
        String machineNumber = data.split(':').last.trim();
        setState(() {
          _deviceInfo['machineNumber'] = machineNumber;
          _statusMessage = '已获取焊机编号: $machineNumber';
        });
      }
      // 可以添加更多数据解析逻辑
    });
  }

  /// 加载当前焊口编号
  Future<void> _loadCurrentJointNumber() async {
    try {
      String? jointNumber = await _jointNumberService.getCurrentJointNumber();
      if (jointNumber == null || jointNumber.isEmpty) {
        jointNumber = await _jointNumberService.generateWeldingJointNumber();
      }
      
      setState(() {
        _currentJointNumber = jointNumber!;
        _statusMessage = '当前焊口编号: $jointNumber';
      });

      // 检查是否有未完成的记录
      final existingRecord = await _recordService.getRecord(jointNumber!);
      if (existingRecord != null && !existingRecord.isCompleted) {
        setState(() {
          _currentRecord = existingRecord;
          _isWeldingInProgress = true;
          _statusMessage = '检测到未完成的焊接: $jointNumber';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '加载焊口编号失败: $e';
      });
    }
  }

  /// 加载所有必要数据
  Future<void> _loadAllData() async {
    setState(() {
      _statusMessage = '正在加载数据...';
    });

    // 并行加载所有数据
    await Future.wait([
      _loadDeviceInfo(),
      _loadWeldingData(),
      _loadLocationData(),
    ]);

    setState(() {
      _statusMessage = '数据加载完成';
    });
  }

  /// 加载设备信息
  Future<void> _loadDeviceInfo() async {
    try {
      // 发送设备信息查询命令
      await _commandService.sendDeviceInfoQuery();
      
      setState(() {
        _deviceInfo['connectionStatus'] = '已连接';
        _deviceInfo['loadTime'] = DateTime.now().toIso8601String();
      });
    } catch (e) {
      setState(() {
        _statusMessage = '加载设备信息失败: $e';
      });
    }
  }

  /// 加载焊机数据
  Future<void> _loadWeldingData() async {
    try {
      final data = await _commandService.readWeldingData();
      if (data.isNotEmpty) {
        final parsed = _commandService.parseWeldingData(data);
        setState(() {
          _weldingData = data;
          _parsedWeldingData = parsed;
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '加载焊机数据失败: $e';
      });
    }
  }

  /// 加载位置数据
  Future<void> _loadLocationData() async {
    try {
      final location = await _locationService.getLocationWithFallback();
      if (location != null) {
        setState(() {
          _locationData = _locationService.formatLocationData(location);
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '加载位置数据失败: $e';
      });
    }
  }

  /// 开始焊接
  Future<void> _startWelding() async {
    if (!_checkDataReady()) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('数据未准备就绪，请先加载所有必要数据')),
      );
      return;
    }

    try {
      setState(() {
        _isWeldingInProgress = true;
        _statusMessage = '正在开始焊接...';
      });

      // 创建焊接记录
      final record = SimplifiedWeldingRecord(
        jointNumber: _currentJointNumber,
        startTime: DateTime.now(),
        status: WeldingStatus.notStarted,
        deviceInfo: Map<String, dynamic>.from(_deviceInfo),
        weldingData: {'raw': _weldingData},
        parsedData: _parsedWeldingData ?? {},
        locationData: _locationData,
        imagePaths: [],
      );

      // 保存记录
      await _recordService.saveRecord(record);
      
      // 发送焊接开始信号
      await _jointNumberService.writeWeldingStartSignal();

      setState(() {
        _currentRecord = record;
        _statusMessage = '焊接已开始，焊口编号: $_currentJointNumber';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('焊接开始成功'), backgroundColor: Colors.green),
      );
    } catch (e) {
      setState(() {
        _isWeldingInProgress = false;
        _statusMessage = '开始焊接失败: $e';
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('开始焊接失败: $e'), backgroundColor: Colors.red),
      );
    }
  }

  /// 完成焊接
  Future<void> _completeWelding() async {
    if (_currentRecord == null) return;

    try {
      setState(() {
        _statusMessage = '正在完成焊接...';
      });

      // 完成记录
      final completedRecord = _currentRecord!.complete(WeldingStatus.success);
      await _recordService.updateRecord(completedRecord);

      // 处理焊接结果
      await _jointNumberService.handleWeldingResult(WeldingStatus.success);

      setState(() {
        _isWeldingCompleted = true;
        _isWeldingInProgress = false;
        _currentRecord = completedRecord;
        _statusMessage = '焊接完成成功';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('焊接完成'), backgroundColor: Colors.green),
      );
    } catch (e) {
      setState(() {
        _statusMessage = '完成焊接失败: $e';
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('完成焊接失败: $e'), backgroundColor: Colors.red),
      );
    }
  }

  /// 上传数据
  Future<void> _uploadData() async {
    if (_currentRecord == null) return;

    try {
      setState(() {
        _statusMessage = '正在上传数据...';
      });

      // 构建上传数据
      final uploadData = {
        'deviceInfo': _deviceInfo,
        'weldingData': _weldingData,
        'parsedData': _parsedWeldingData,
        'locationData': _locationData,
        'completionTime': DateTime.now().toIso8601String(),
      };

      // 上传数据
      final success = await _jointNumberService.handleWeldingDataUpload(
        _currentJointNumber, 
        uploadData
      );

      if (success) {
        // 标记为已上传
        await _recordService.markAsUploaded(_currentJointNumber);
        
        setState(() {
          _statusMessage = '数据上传成功';
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('数据上传成功'), backgroundColor: Colors.green),
        );

        // 准备下一个焊口
        await _prepareNextWelding();
      } else {
        setState(() {
          _statusMessage = '数据上传失败';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '上传数据失败: $e';
      });
    }
  }

  /// 准备下一个焊接
  Future<void> _prepareNextWelding() async {
    try {
      // 生成新的焊口编号
      final newJointNumber = await _jointNumberService.generateWeldingJointNumber();
      
      setState(() {
        _currentJointNumber = newJointNumber;
        _isWeldingInProgress = false;
        _isWeldingCompleted = false;
        _currentRecord = null;
        _statusMessage = '已准备新的焊口: $newJointNumber';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '准备下一个焊接失败: $e';
      });
    }
  }

  /// 检查数据是否准备就绪
  bool _checkDataReady() {
    return _deviceInfo.isNotEmpty && 
           _weldingData.isNotEmpty && 
           _locationData.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('简化焊接配置'),
        backgroundColor: Colors.blue,
      ),
      body: _isLoading 
        ? Center(child: CircularProgressIndicator())
        : _buildMainContent(),
      bottomNavigationBar: _buildBottomActionBar(),
    );
  }

  Widget _buildMainContent() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          _buildJointNumberCard(),
          SizedBox(height: 16),
          _buildStatusCard(),
          SizedBox(height: 16),
          _buildDataSummaryCard(),
          SizedBox(height: 100), // 为底部按钮留空间
        ],
      ),
    );
  }

  Widget _buildJointNumberCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('当前焊口编号', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            SizedBox(height: 8),
            Text(_currentJointNumber, style: TextStyle(fontSize: 24, color: Colors.blue)),
            if (_currentRecord != null) ...[
              SizedBox(height: 8),
              Text('状态: ${_currentRecord!.statusDescription}'),
              Text('开始时间: ${_currentRecord!.startTime.toString().substring(0, 19)}'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      color: _getStatusColor(),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(_getStatusIcon(), color: Colors.white),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                _statusMessage,
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataSummaryCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('数据状态', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            SizedBox(height: 12),
            _buildDataStatusRow('设备信息', _deviceInfo.isNotEmpty),
            _buildDataStatusRow('焊机数据', _weldingData.isNotEmpty),
            _buildDataStatusRow('位置数据', _locationData.isNotEmpty),
            SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: _bleService.isConnected ? _loadAllData : null,
              icon: Icon(Icons.refresh),
              label: Text('刷新数据'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataStatusRow(String label, bool isReady) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isReady ? Icons.check_circle : Icons.cancel,
            color: isReady ? Colors.green : Colors.red,
            size: 20,
          ),
          SizedBox(width: 8),
          Text(label),
        ],
      ),
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [BoxShadow(color: Colors.grey.shade300, blurRadius: 4)],
      ),
      child: _buildActionButton(),
    );
  }

  Widget _buildActionButton() {
    if (!_bleService.isConnected) {
      return ElevatedButton(
        onPressed: null,
        child: Text('请先连接设备'),
        style: ElevatedButton.styleFrom(
          minimumSize: Size(double.infinity, 50),
        ),
      );
    }

    if (!_isWeldingInProgress && !_isWeldingCompleted) {
      return ElevatedButton(
        onPressed: _checkDataReady() ? _startWelding : null,
        child: Text('开始焊接'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          minimumSize: Size(double.infinity, 50),
        ),
      );
    }

    if (_isWeldingInProgress && !_isWeldingCompleted) {
      return ElevatedButton(
        onPressed: _completeWelding,
        child: Text('完成焊接'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.orange,
          minimumSize: Size(double.infinity, 50),
        ),
      );
    }

    if (_isWeldingCompleted) {
      return ElevatedButton(
        onPressed: _uploadData,
        child: Text('上传数据'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          minimumSize: Size(double.infinity, 50),
        ),
      );
    }

    return Container();
  }

  Color _getStatusColor() {
    if (_isWeldingCompleted) return Colors.green;
    if (_isWeldingInProgress) return Colors.orange;
    return Colors.blue;
  }

  IconData _getStatusIcon() {
    if (_isWeldingCompleted) return Icons.check_circle;
    if (_isWeldingInProgress) return Icons.build;
    return Icons.info;
  }
}
