import 'package:flutter/material.dart';
import '../services/offline_storage_service.dart';
import '../services/welding_joint_number_service.dart';
import '../services/user_service.dart';
import '../models/offline_data_model.dart';

class WeldingDataHistoryScreen extends StatefulWidget {
  @override
  _WeldingDataHistoryScreenState createState() =>
      _WeldingDataHistoryScreenState();
}

class _WeldingDataHistoryScreenState extends State<WeldingDataHistoryScreen> {
  final OfflineStorageService _storageService = OfflineStorageService();
  final WeldingJointNumberService _jointNumberService =
      WeldingJointNumberService();
  final UserService _userService = UserService();

  List<OfflineWeldingData> _allWeldingData = [];
  List<OfflineWeldingData> _filteredData = [];
  bool _isLoading = true;
  String _errorMessage = '';
  String _searchQuery = '';
  String _filterStatus = 'all';
  String _currentUser = '';

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    await _loadCurrentUser();
    await _loadWeldingData();
  }

  Future<void> _loadCurrentUser() async {
    try {
      _currentUser = await _userService.getUserName() ?? '未知用户';
    } catch (e) {
      _currentUser = '未知用户';
    }
  }

  Future<void> _loadWeldingData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      _allWeldingData = await _storageService.getAllWeldingData();
      _allWeldingData.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      _applyFilters();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = '加载焊接数据失败: $e';
        _isLoading = false;
      });
    }
  }

  void _applyFilters() {
    _filteredData = _allWeldingData.where((data) {
      bool statusMatch = true;
      if (_filterStatus == 'uploaded') {
        statusMatch = data.isUploaded;
      } else if (_filterStatus == 'pending') {
        statusMatch = !data.isUploaded;
      }

      bool searchMatch = true;
      if (_searchQuery.isNotEmpty) {
        searchMatch = data.id
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()) ||
            data.projectId.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            data.userId.toLowerCase().contains(_searchQuery.toLowerCase());
      }

      return statusMatch && searchMatch;
    }).toList();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _applyFilters();
    });
  }

  void _onFilterChanged(String filter) {
    setState(() {
      _filterStatus = filter;
      _applyFilters();
    });
  }

  Future<void> _refreshData() async {
    await _loadWeldingData();
  }

  String _formatDateTime(DateTime dateTime) {
    DateTime beijingTime = dateTime.toLocal();
    return '${beijingTime.year}-${beijingTime.month.toString().padLeft(2, '0')}-${beijingTime.day.toString().padLeft(2, '0')} '
        '${beijingTime.hour.toString().padLeft(2, '0')}:${beijingTime.minute.toString().padLeft(2, '0')}:${beijingTime.second.toString().padLeft(2, '0')} '
        '(北京时间)';
  }

  String _getStatusText(OfflineWeldingData data) {
    return data.isUploaded ? '已上传' : '待上传';
  }

  Color _getStatusColor(OfflineWeldingData data) {
    return data.isUploaded ? Colors.green : Colors.orange;
  }

  IconData _getStatusIcon(OfflineWeldingData data) {
    return data.isUploaded ? Icons.check_circle : Icons.upload_outlined;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('焊接数据历史'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _refreshData,
            tooltip: '刷新数据',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildStatsHeader(),
          _buildSearchAndFilter(),
          Expanded(child: _buildDataList()),
        ],
      ),
    );
  }

  Widget _buildStatsHeader() {
    int totalCount = _allWeldingData.length;
    int uploadedCount = _allWeldingData.where((data) => data.isUploaded).length;
    int pendingCount = totalCount - uploadedCount;

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade50, Colors.blue.shade100],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.person, color: Colors.blue),
              SizedBox(width: 8),
              Text(
                '当前操作员: $_currentUser',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
            ],
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                  child: _buildStatCard(
                      '总计', totalCount.toString(), Icons.storage, Colors.blue)),
              SizedBox(width: 8),
              Expanded(
                  child: _buildStatCard('已上传', uploadedCount.toString(),
                      Icons.check_circle, Colors.green)),
              SizedBox(width: 8),
              Expanded(
                  child: _buildStatCard('待上传', pendingCount.toString(),
                      Icons.upload_outlined, Colors.orange)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(color: Colors.black12, blurRadius: 4, offset: Offset(0, 2))
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          SizedBox(height: 4),
          Text(value,
              style: TextStyle(
                  fontSize: 20, fontWeight: FontWeight.bold, color: color)),
          Text(title, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          TextField(
            decoration: InputDecoration(
              hintText: '搜索焊口编号、项目ID或用户ID...',
              prefixIcon: Icon(Icons.search),
              border:
                  OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onChanged: _onSearchChanged,
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Text('状态过滤: ', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(width: 8),
              Expanded(
                child: Row(
                  children: [
                    _buildFilterChip('全部', 'all'),
                    SizedBox(width: 8),
                    _buildFilterChip('已上传', 'uploaded'),
                    SizedBox(width: 8),
                    _buildFilterChip('待上传', 'pending'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    bool isSelected = _filterStatus == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) _onFilterChanged(value);
      },
      selectedColor: Colors.blue.shade100,
      checkmarkColor: Colors.blue,
    );
  }

  Widget _buildDataList() {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在加载焊接数据...'),
          ],
        ),
      );
    }

    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text(_errorMessage,
                style: TextStyle(color: Colors.red),
                textAlign: TextAlign.center),
            SizedBox(height: 16),
            ElevatedButton(onPressed: _refreshData, child: Text('重试')),
          ],
        ),
      );
    }

    if (_filteredData.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty || _filterStatus != 'all'
                  ? '没有找到符合条件的焊接数据'
                  : '暂无焊接数据',
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
            ),
            if (_searchQuery.isNotEmpty || _filterStatus != 'all') ...[
              SizedBox(height: 8),
              TextButton(
                onPressed: () {
                  setState(() {
                    _searchQuery = '';
                    _filterStatus = 'all';
                    _applyFilters();
                  });
                },
                child: Text('清除筛选条件'),
              ),
            ],
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshData,
      child: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: _filteredData.length,
        itemBuilder: (context, index) {
          final data = _filteredData[index];
          return _buildWeldingDataCard(data);
        },
      ),
    );
  }

  Widget _buildWeldingDataCard(OfflineWeldingData data) {
    return Card(
      margin: EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: () => _showWeldingDataDetails(data),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Icons.build, color: Colors.blue, size: 20),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('焊口编号: ${data.id}',
                            style: TextStyle(
                                fontWeight: FontWeight.bold, fontSize: 16)),
                        SizedBox(height: 4),
                        Text('项目: ${data.projectId}',
                            style: TextStyle(
                                color: Colors.grey[600], fontSize: 14)),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor(data).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border:
                          Border.all(color: _getStatusColor(data), width: 1),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(_getStatusIcon(data),
                            size: 14, color: _getStatusColor(data)),
                        SizedBox(width: 4),
                        Text(_getStatusText(data),
                            style: TextStyle(
                                color: _getStatusColor(data),
                                fontSize: 12,
                                fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                      child: _buildInfoItem(Icons.person, '操作员', data.userId)),
                  Expanded(
                      child:
                          _buildInfoItem(Icons.devices, '设备', data.deviceId)),
                ],
              ),
              SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                      child: _buildInfoItem(
                          Icons.access_time,
                          '时间',
                          _formatDateTime(data.timestamp).split(' ')[0] +
                              ' ' +
                              _formatDateTime(data.timestamp).split(' ')[1])),
                  if (data.imagePaths.isNotEmpty)
                    Expanded(
                        child: _buildInfoItem(
                            Icons.image, '图片', '${data.imagePaths.length} 张')),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(label,
                  style: TextStyle(fontSize: 10, color: Colors.grey[500])),
              Text(value,
                  style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                  overflow: TextOverflow.ellipsis),
            ],
          ),
        ),
      ],
    );
  }

  void _showWeldingDataDetails(OfflineWeldingData data) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.build, color: Colors.blue),
            SizedBox(width: 8),
            Expanded(child: Text('焊接数据详情')),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('焊口编号', data.id),
              _buildDetailRow('项目ID', data.projectId),
              _buildDetailRow('用户ID', data.userId),
              _buildDetailRow('设备ID', data.deviceId),
              _buildDetailRow('创建时间', _formatDateTime(data.timestamp)),
              _buildDetailRow('上传状态', _getStatusText(data)),
              if (data.imagePaths.isNotEmpty) ...[
                SizedBox(height: 8),
                _buildDetailRow('图片数量', '${data.imagePaths.length} 张'),
              ],
              SizedBox(height: 12),
              Text('焊接参数:', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 4),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(4)),
                child: Text(_formatWeldingParams(data.weldingParams),
                    style: TextStyle(fontSize: 12, fontFamily: 'monospace')),
              ),
              if (data.weldingDataHex.isNotEmpty) ...[
                SizedBox(height: 12),
                Text('160字节数据:', style: TextStyle(fontWeight: FontWeight.bold)),
                SizedBox(height: 4),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(4)),
                  child: Text(
                    data.weldingDataHex.length > 100
                        ? '${data.weldingDataHex.substring(0, 100)}...'
                        : data.weldingDataHex,
                    style: TextStyle(fontSize: 10, fontFamily: 'monospace'),
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(context), child: Text('关闭')),
          if (!data.isUploaded)
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _retryUpload(data);
              },
              child: Text('重新上传'),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text('$label:',
                style: TextStyle(
                    fontWeight: FontWeight.w500, color: Colors.grey[700])),
          ),
          Expanded(
              child:
                  Text(value, style: TextStyle(fontWeight: FontWeight.w400))),
        ],
      ),
    );
  }

  String _formatWeldingParams(Map<String, dynamic> params) {
    if (params.isEmpty) return '无参数数据';
    StringBuffer buffer = StringBuffer();
    params.forEach((key, value) {
      buffer.writeln('$key: $value');
    });
    return buffer.toString().trim();
  }

  Future<void> _retryUpload(OfflineWeldingData data) async {
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在重新上传...'),
            ],
          ),
        ),
      );

      bool success = await _jointNumberService.handleWeldingDataUpload(data.id);
      Navigator.pop(context);

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('数据上传成功'), backgroundColor: Colors.green),
        );
        await _refreshData();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('数据上传失败，请检查网络连接'), backgroundColor: Colors.red),
        );
      }
    } catch (e) {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('上传异常: $e'), backgroundColor: Colors.red),
      );
    }
  }
}
