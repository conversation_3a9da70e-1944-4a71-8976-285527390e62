import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../services/pipe_photo_service.dart';

class PipePhotoScreen extends StatefulWidget {
  @override
  _PipePhotoScreenState createState() => _PipePhotoScreenState();
}

class _PipePhotoScreenState extends State<PipePhotoScreen> {
  String? _imagePath;
  bool _isLoading = false;
  String _statusMessage = '请拍摄管道照片';

  @override
  void initState() {
    super.initState();
    _takePhoto();
  }

  Future<void> _takePhoto() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在准备相机...';
    });

    try {
      final XFile? image = await PipePhotoService.takePipePhoto();

      if (image != null) {
        setState(() {
          _imagePath = image.path;
          _statusMessage = '照片已拍摄，请确认';
          _isLoading = false;
        });
      } else {
        // 用户取消了拍照
        setState(() {
          _isLoading = false;
          _statusMessage = '拍照已取消';
        });
      }
    } catch (e) {
      print('拍照出错: $e');
      PipePhotoService.showToast('拍照出错: $e');
      setState(() {
        _isLoading = false;
        _statusMessage = '拍照出错: $e';
      });
    }
  }

  // 上传并验证照片
  Future<void> _uploadAndVerifyPhoto() async {
    if (_imagePath == null) {
      PipePhotoService.showToast('请先拍摄管道照片');
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = '正在处理照片...';
    });

    try {
      // 使用新的处理方法（自动判断在线/离线模式）
      final XFile photoFile = XFile(_imagePath!);
      final Map<String, dynamic>? result =
          await PipePhotoService.processPipePhoto(photoFile);

      if (result != null) {
        setState(() {
          _isLoading = false;
          _statusMessage = '管道照片处理完成: ${result['result']}';
        });

        // 成功处理，返回结果
        Future.delayed(Duration(milliseconds: 500), () {
          Navigator.of(context).pop(result);
        });
      } else {
        // 处理失败
        setState(() {
          _isLoading = false;
          _statusMessage = '照片处理失败，请重试';
        });
      }
    } catch (e) {
      print('处理照片出错: $e');
      PipePhotoService.showToast('处理照片出错: $e');
      setState(() {
        _isLoading = false;
        _statusMessage = '处理照片出错: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('管道照片'),
        actions: [
          if (_imagePath != null)
            IconButton(
              icon: Icon(Icons.check_circle),
              tooltip: '确认并上传',
              onPressed: _isLoading ? null : _uploadAndVerifyPhoto,
            )
        ],
      ),
      body: Stack(
        children: [
          // 主内容
          _isLoading
              ? Center(child: CircularProgressIndicator())
              : _imagePath != null
                  ? Column(
                      children: [
                        Expanded(
                          child: Image.file(
                            File(_imagePath!),
                            fit: BoxFit.contain,
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              ElevatedButton.icon(
                                onPressed: _isLoading ? null : _takePhoto,
                                icon: Icon(Icons.refresh),
                                label: Text('重新拍摄'),
                              ),
                              ElevatedButton.icon(
                                onPressed:
                                    _isLoading ? null : _uploadAndVerifyPhoto,
                                icon: Icon(Icons.check),
                                label: Text('确认使用'),
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
                  : Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.camera_alt, size: 80, color: Colors.grey),
                          SizedBox(height: 16),
                          Text('点击下方按钮拍摄管道照片'),
                          SizedBox(height: 24),
                          ElevatedButton.icon(
                            onPressed: _isLoading ? null : _takePhoto,
                            icon: Icon(Icons.camera_alt),
                            label: Text('拍摄照片'),
                          ),
                        ],
                      ),
                    ),

          // 状态信息
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              color: Colors.black54,
              padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: Text(
                _statusMessage,
                style: TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
