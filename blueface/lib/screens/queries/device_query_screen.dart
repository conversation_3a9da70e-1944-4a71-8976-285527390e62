import 'package:flutter/material.dart';
import '../../models/device_model.dart';
import '../../models/project_model.dart';
import '../../services/query_service.dart';
import '../../services/project_service.dart';

class DeviceQueryScreen extends StatefulWidget {
  @override
  _DeviceQueryScreenState createState() => _DeviceQueryScreenState();
}

class _DeviceQueryScreenState extends State<DeviceQueryScreen> {
  final QueryService _queryService = QueryService();
  final ProjectService _projectService = ProjectService();

  List<Device> _devices = [];
  List<Project> _projects = [];
  Project? _selectedProject;
  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  Future<void> _loadProjects() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final projects = await _projectService.getUserProjects();
      setState(() {
        _projects = projects;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = '加载项目失败: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _searchDevices() async {
    if (_selectedProject == null) {
      setState(() {
        _errorMessage = '请先选择项目';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _devices = [];
    });

    try {
      final devices =
          await _queryService.getDevicesByProjectId(_selectedProject!.id);
      setState(() {
        _devices = devices;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('设备查询'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 项目选择区域
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '选择项目',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 16),
                    _buildProjectDropdown(),
                    SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _searchDevices,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.search),
                          SizedBox(width: 8),
                          Text('查询设备'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // 错误信息显示
            if (_errorMessage.isNotEmpty) ...[
              SizedBox(height: 16),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage,
                        style: TextStyle(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // 加载中
            if (_isLoading) ...[
              SizedBox(height: 20),
              Center(
                child: CircularProgressIndicator(),
              ),
            ],

            // 设备列表
            if (_devices.isNotEmpty && !_isLoading) ...[
              SizedBox(height: 20),
              Text(
                '设备列表 (${_devices.length})',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  itemCount: _devices.length,
                  itemBuilder: (context, index) {
                    final device = _devices[index];
                    return Card(
                      margin: EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: Colors.blue.shade50,
                          child: Icon(Icons.devices, color: Colors.blue),
                        ),
                        title: Text(device.name),
                        subtitle: Text('序列号: ${device.serialNumber}'),
                        trailing: Icon(Icons.arrow_forward_ios, size: 16),
                        onTap: () => _showDeviceDetails(device),
                      ),
                    );
                  },
                ),
              ),
            ],

            // 无设备时的提示
            if (_devices.isEmpty &&
                !_isLoading &&
                _selectedProject != null) ...[
              SizedBox(height: 20),
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.devices_other,
                      size: 64,
                      color: Colors.grey.withOpacity(0.5),
                    ),
                    SizedBox(height: 16),
                    Text(
                      '未发现设备',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProjectDropdown() {
    return DropdownButtonFormField<Project>(
      decoration: InputDecoration(
        labelText: '项目',
        border: OutlineInputBorder(),
      ),
      hint: Text('请选择项目'),
      value: _selectedProject,
      onChanged: (Project? newValue) {
        setState(() {
          _selectedProject = newValue;
        });
      },
      items: _projects.map<DropdownMenuItem<Project>>((Project project) {
        return DropdownMenuItem<Project>(
          value: project,
          child: Text('${project.name} (${project.code})'),
        );
      }).toList(),
      isExpanded: true,
    );
  }

  void _showDeviceDetails(Device device) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.devices, color: Colors.blue),
            SizedBox(width: 8),
            Expanded(
              child: Text(
                device.name,
                style: TextStyle(fontSize: 18),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailItem('设备ID', device.id),
              _buildDetailItem('序列号', device.serialNumber),
              _buildDetailItem('型号', device.model),
              if (device.manufacturer != null)
                _buildDetailItem('制造商', device.manufacturer!),
              if (device.status != null) _buildDetailItem('状态', device.status!),
              if (device.lastMaintenance != null)
                _buildDetailItem('最近维护', device.lastMaintenance!),
              if (device.nextMaintenance != null)
                _buildDetailItem('下次维护', device.nextMaintenance!),
              if (device.location != null)
                _buildDetailItem('位置', device.location!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(fontSize: 14),
          ),
          Divider(height: 16),
        ],
      ),
    );
  }
}
