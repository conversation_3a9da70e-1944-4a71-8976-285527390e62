import 'package:flutter/material.dart';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/rendering.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../../services/query_service.dart';
import '../../services/project_service.dart';
import '../../models/project_model.dart';
import '../../models/welding_joint_model.dart';

class QRCodeScreen extends StatefulWidget {
  @override
  _QRCodeScreenState createState() => _QRCodeScreenState();
}

class _QRCodeScreenState extends State<QRCodeScreen> {
  final TextEditingController _jointCodeController = TextEditingController();
  final QueryService _queryService = QueryService();
  final ProjectService _projectService = ProjectService();

  List<Project> _projects = [];
  Project? _selectedProject;
  List<Map<String, dynamic>> _joints = [];
  String? _selectedJointCode;
  String? _qrCodeData;
  bool _isLoading = false;
  String _errorMessage = '';

  // 二维码引用
  final GlobalKey _qrKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  @override
  void dispose() {
    _jointCodeController.dispose();
    super.dispose();
  }

  Future<void> _loadProjects() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final projects = await _projectService.getUserProjects();
      setState(() {
        _projects = projects;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = '加载项目失败: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadJoints() async {
    if (_selectedProject == null) {
      setState(() {
        _errorMessage = '请先选择项目';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _joints = [];
      _selectedJointCode = null;
      _qrCodeData = null;
    });

    try {
      final joints = await _projectService.getWeldJoints(_selectedProject!.id);
      setState(() {
        _joints = joints;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _generateQRCode() async {
    final jointCode = _selectedJointCode ?? _jointCodeController.text.trim();
    if (jointCode.isEmpty) {
      setState(() {
        _errorMessage = '请输入或选择焊口编号';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _qrCodeData = null;
    });

    try {
      final qrCodeData =
          await _queryService.generateWeldingJointQRCode(jointCode);
      setState(() {
        _qrCodeData = qrCodeData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _saveQRCode() async {
    try {
      // 找到渲染对象
      final RenderRepaintBoundary boundary =
          _qrKey.currentContext!.findRenderObject() as RenderRepaintBoundary;

      // 捕获图像
      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData == null) {
        throw Exception('无法保存二维码');
      }

      final Uint8List pngBytes = byteData.buffer.asUint8List();

      // 保存到临时目录
      final tempDir = await getTemporaryDirectory();
      final jointCode = _selectedJointCode ?? _jointCodeController.text.trim();
      final file = File(
          '${tempDir.path}/qrcode_${jointCode}_${DateTime.now().millisecondsSinceEpoch}.png');
      await file.writeAsBytes(pngBytes);

      // 分享文件
      await Share.shareXFiles(
        [XFile(file.path)],
        text: '焊口编号 $jointCode 的二维码',
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('二维码分享成功')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('保存二维码失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('二维码生成打印'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 查询区域
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '二维码生成',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 16),
                    _buildProjectDropdown(),
                    SizedBox(height: 16),
                    if (_joints.isNotEmpty) ...[
                      _buildJointsDropdown(),
                      SizedBox(height: 16),
                      Text('或'),
                    ],
                    SizedBox(height: 8),
                    TextField(
                      controller: _jointCodeController,
                      decoration: InputDecoration(
                        labelText: '焊口编号',
                        hintText: '请输入焊口编号',
                        prefixIcon: Icon(Icons.code),
                        border: OutlineInputBorder(),
                      ),
                    ),
                    SizedBox(height: 16),
                    Row(
                      children: [
                        if (_selectedProject != null)
                          ElevatedButton(
                            onPressed: _isLoading ? null : _loadJoints,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue.shade700,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.refresh),
                                SizedBox(width: 8),
                                Text('加载焊口'),
                              ],
                            ),
                          ),
                        Spacer(),
                        ElevatedButton(
                          onPressed: _isLoading ? null : _generateQRCode,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.qr_code),
                              SizedBox(width: 8),
                              Text('生成二维码'),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // 错误信息显示
            if (_errorMessage.isNotEmpty) ...[
              SizedBox(height: 16),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage,
                        style: TextStyle(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // 加载中
            if (_isLoading) ...[
              SizedBox(height: 20),
              Center(
                child: CircularProgressIndicator(),
              ),
            ],

            // 二维码展示
            if (_qrCodeData != null && !_isLoading) ...[
              SizedBox(height: 20),
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '焊口二维码',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 16),
                      // 使用RepaintBoundary包装二维码，用于截图
                      RepaintBoundary(
                        key: _qrKey,
                        child: Container(
                          color: Colors.white,
                          padding: EdgeInsets.all(16),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              QrImageView(
                                data: _qrCodeData!,
                                version: QrVersions.auto,
                                size: 200.0,
                              ),
                              SizedBox(height: 8),
                              Text(
                                _selectedJointCode ?? _jointCodeController.text,
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 24),
                      ElevatedButton.icon(
                        icon: Icon(Icons.share),
                        label: Text('保存/分享二维码'),
                        onPressed: _saveQRCode,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProjectDropdown() {
    return DropdownButtonFormField<Project>(
      decoration: InputDecoration(
        labelText: '项目',
        border: OutlineInputBorder(),
      ),
      hint: Text('请选择项目'),
      value: _selectedProject,
      onChanged: (Project? newValue) {
        setState(() {
          _selectedProject = newValue;
          _joints = [];
          _selectedJointCode = null;
        });
      },
      items: _projects.map<DropdownMenuItem<Project>>((Project project) {
        return DropdownMenuItem<Project>(
          value: project,
          child: Text('${project.name} (${project.code})'),
        );
      }).toList(),
      isExpanded: true,
    );
  }

  Widget _buildJointsDropdown() {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: '焊口编号',
        border: OutlineInputBorder(),
      ),
      hint: Text('请选择焊口'),
      value: _selectedJointCode,
      onChanged: (String? newValue) {
        setState(() {
          _selectedJointCode = newValue;
          if (newValue != null) {
            _jointCodeController.text = '';
          }
        });
      },
      items:
          _joints.map<DropdownMenuItem<String>>((Map<String, dynamic> joint) {
        final code = joint['code']?.toString() ?? '未知编号';
        return DropdownMenuItem<String>(
          value: code,
          child: Text(code),
        );
      }).toList(),
      isExpanded: true,
    );
  }
}
