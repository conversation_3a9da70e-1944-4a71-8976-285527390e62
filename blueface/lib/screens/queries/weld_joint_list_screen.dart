import 'package:flutter/material.dart';
import '../../models/project_model.dart';
import '../../services/project_service.dart';

class WeldJointListScreen extends StatefulWidget {
  @override
  _WeldJointListScreenState createState() => _WeldJointListScreenState();
}

class _WeldJointListScreenState extends State<WeldJointListScreen> {
  final ProjectService _projectService = ProjectService();

  List<Project> _projects = [];
  Project? _selectedProject;
  List<Map<String, dynamic>> _joints = [];
  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  Future<void> _loadProjects() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final projects = await _projectService.getUserProjects();
      setState(() {
        _projects = projects;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = '加载项目失败: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadJoints() async {
    if (_selectedProject == null) {
      setState(() {
        _errorMessage = '请先选择项目';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _joints = [];
    });

    try {
      final joints = await _projectService.getWeldJoints(_selectedProject!.id);
      setState(() {
        _joints = joints;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('焊口列表'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 项目选择区域
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '选择项目',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 16),
                    _buildProjectDropdown(),
                    SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _loadJoints,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.search),
                          SizedBox(width: 8),
                          Text('查询焊口'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // 错误信息显示
            if (_errorMessage.isNotEmpty) ...[
              SizedBox(height: 16),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage,
                        style: TextStyle(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // 加载中
            if (_isLoading) ...[
              SizedBox(height: 20),
              Center(
                child: CircularProgressIndicator(),
              ),
            ],

            // 焊口列表
            if (_joints.isNotEmpty && !_isLoading) ...[
              SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '焊口列表 (${_joints.length})',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '项目: ${_selectedProject?.name}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  itemCount: _joints.length,
                  itemBuilder: (context, index) {
                    final joint = _joints[index];
                    final code = joint['code']?.toString() ?? '未知编号';
                    final status = joint['status']?.toString() ?? '未知状态';

                    return Card(
                      margin: EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: Colors.orange.shade100,
                          child: Icon(Icons.line_axis, color: Colors.orange),
                        ),
                        title: Text(code),
                        subtitle: Text('状态: $status'),
                        trailing: Icon(Icons.arrow_forward_ios, size: 16),
                        onTap: () => _showJointDetails(joint),
                      ),
                    );
                  },
                ),
              ),
            ],

            // 无焊口时的提示
            if (_joints.isEmpty && !_isLoading && _selectedProject != null) ...[
              SizedBox(height: 20),
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.line_axis,
                      size: 64,
                      color: Colors.grey.withOpacity(0.5),
                    ),
                    SizedBox(height: 16),
                    Text(
                      '未发现焊口',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProjectDropdown() {
    return DropdownButtonFormField<Project>(
      decoration: InputDecoration(
        labelText: '项目',
        border: OutlineInputBorder(),
      ),
      hint: Text('请选择项目'),
      value: _selectedProject,
      onChanged: (Project? newValue) {
        setState(() {
          _selectedProject = newValue;
          _joints = [];
        });
      },
      items: _projects.map<DropdownMenuItem<Project>>((Project project) {
        return DropdownMenuItem<Project>(
          value: project,
          child: Text('${project.name} (${project.code})'),
        );
      }).toList(),
      isExpanded: true,
    );
  }

  void _showJointDetails(Map<String, dynamic> joint) {
    final id = joint['id']?.toString() ?? '未知ID';
    final code = joint['code']?.toString() ?? '未知编号';
    final status = joint['status']?.toString() ?? '未知状态';
    final createdAt = joint['createdAt']?.toString() ?? '未知时间';
    final pipeSegmentId = joint['pipeSegmentId']?.toString() ?? '未知管段';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.line_axis, color: Colors.orange),
            SizedBox(width: 8),
            Expanded(
              child: Text(
                '焊口详情',
                style: TextStyle(fontSize: 18),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailItem('焊口ID', id),
              _buildDetailItem('焊口编号', code),
              _buildDetailItem('状态', status),
              _buildDetailItem('创建时间', createdAt),
              _buildDetailItem('管段ID', pipeSegmentId),

              // 显示额外信息
              ..._getAdditionalInfo(joint).entries.map((entry) {
                return _buildDetailItem(entry.key, entry.value.toString());
              }).toList(),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getAdditionalInfo(Map<String, dynamic> joint) {
    final Map<String, dynamic> additionalInfo = Map.from(joint);

    // 移除已经显示的标准字段
    [
      'id',
      'code',
      'projectId',
      'pipeSegmentId',
      'status',
      'createdAt',
      'updatedAt'
    ].forEach((field) => additionalInfo.remove(field));

    return additionalInfo;
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(fontSize: 14),
          ),
          Divider(height: 16),
        ],
      ),
    );
  }
}
