import 'package:flutter/material.dart';
import '../../models/user_model.dart';
import '../../services/query_service.dart';

class UserQueryScreen extends StatefulWidget {
  @override
  _UserQueryScreenState createState() => _UserQueryScreenState();
}

class _UserQueryScreenState extends State<UserQueryScreen> {
  final TextEditingController _searchController = TextEditingController();
  final QueryService _queryService = QueryService();

  User? _user;
  bool _isLoading = false;
  String _errorMessage = '';
  String _searchType = 'username'; // 默认按用户名搜索

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _searchUser() async {
    final searchValue = _searchController.text.trim();
    if (searchValue.isEmpty) {
      setState(() {
        _errorMessage = '请输入${_searchType == 'id' ? '用户ID' : '用户名'}';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _user = null;
    });

    try {
      final User user;
      if (_searchType == 'id') {
        user = await _queryService.getUserById(searchValue);
      } else {
        user = await _queryService.getUserByUsername(searchValue);
      }

      setState(() {
        _user = user;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('用户信息查询'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 搜索区域
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '用户查询',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 16),
                    Row(
                      children: [
                        Radio<String>(
                          value: 'username',
                          groupValue: _searchType,
                          onChanged: (value) {
                            setState(() {
                              _searchType = value!;
                              _searchController.clear();
                              _user = null;
                            });
                          },
                        ),
                        Text('按用户名'),
                        SizedBox(width: 16),
                        Radio<String>(
                          value: 'id',
                          groupValue: _searchType,
                          onChanged: (value) {
                            setState(() {
                              _searchType = value!;
                              _searchController.clear();
                              _user = null;
                            });
                          },
                        ),
                        Text('按用户ID'),
                      ],
                    ),
                    SizedBox(height: 16),
                    TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        labelText: _searchType == 'id' ? '用户ID' : '用户名',
                        hintText: '请输入${_searchType == 'id' ? '用户ID' : '用户名'}',
                        prefixIcon: Icon(Icons.search),
                        border: OutlineInputBorder(),
                      ),
                      onSubmitted: (_) => _searchUser(),
                    ),
                    SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _searchUser,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.search),
                          SizedBox(width: 8),
                          Text('搜索'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // 错误信息显示
            if (_errorMessage.isNotEmpty) ...[
              SizedBox(height: 16),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage,
                        style: TextStyle(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // 加载中
            if (_isLoading) ...[
              SizedBox(height: 20),
              Center(
                child: CircularProgressIndicator(),
              ),
            ],

            // 用户信息展示
            if (_user != null && !_isLoading) ...[
              SizedBox(height: 20),
              Expanded(
                child: SingleChildScrollView(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              CircleAvatar(
                                backgroundColor: Colors.blue.shade50,
                                radius: 32,
                                child: _user!.avatar != null
                                    ? Image.network(_user!.avatar!)
                                    : Icon(
                                        Icons.person,
                                        size: 32,
                                        color: Colors.blue,
                                      ),
                              ),
                              SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _user!.name ?? _user!.username,
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      _user!.role ?? '无角色信息',
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          Divider(height: 32),
                          _buildUserDetailItem('用户ID', _user!.id),
                          _buildUserDetailItem('用户名', _user!.username),
                          if (_user!.department != null)
                            _buildUserDetailItem('部门', _user!.department!),
                          if (_user!.email != null)
                            _buildUserDetailItem('邮箱', _user!.email!),
                          if (_user!.phone != null)
                            _buildUserDetailItem('电话', _user!.phone!),
                          if (_user!.lastLogin != null)
                            _buildUserDetailItem('上次登录', _user!.lastLogin!),

                          // 显示额外信息
                          if (_user!.additionalInfo.isNotEmpty) ...[
                            SizedBox(height: 16),
                            Text(
                              '额外信息',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(height: 8),
                            ..._user!.additionalInfo.entries.map((entry) {
                              return _buildUserDetailItem(
                                  entry.key, entry.value.toString());
                            }).toList(),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildUserDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}
