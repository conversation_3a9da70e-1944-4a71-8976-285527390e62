import 'package:flutter/material.dart';
import '../../models/welding_joint_model.dart';
import '../../services/query_service.dart';
import 'package:qr_flutter/qr_flutter.dart';

class WeldingJointQueryScreen extends StatefulWidget {
  @override
  _WeldingJointQueryScreenState createState() =>
      _WeldingJointQueryScreenState();
}

class _WeldingJointQueryScreenState extends State<WeldingJointQueryScreen> {
  final TextEditingController _searchController = TextEditingController();
  final QueryService _queryService = QueryService();

  WeldingJoint? _weldingJoint;
  bool _isLoading = false;
  String _errorMessage = '';
  String? _qrCodeData;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _searchWeldingJoint() async {
    final code = _searchController.text.trim();
    if (code.isEmpty) {
      setState(() {
        _errorMessage = '请输入焊口编号';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _weldingJoint = null;
      _qrCodeData = null;
    });

    try {
      final weldingJoint = await _queryService.getWeldingJointByCode(code);
      setState(() {
        _weldingJoint = weldingJoint;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _generateQRCode() async {
    if (_weldingJoint == null) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final qrCodeData =
          await _queryService.generateWeldingJointQRCode(_weldingJoint!.code);
      setState(() {
        _qrCodeData = qrCodeData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('焊口信息查询'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 搜索区域
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '焊口查询',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 16),
                    TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        labelText: '焊口编号',
                        hintText: '请输入焊口编号',
                        prefixIcon: Icon(Icons.search),
                        border: OutlineInputBorder(),
                      ),
                      onSubmitted: (_) => _searchWeldingJoint(),
                    ),
                    SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _searchWeldingJoint,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.search),
                          SizedBox(width: 8),
                          Text('搜索'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // 错误信息显示
            if (_errorMessage.isNotEmpty) ...[
              SizedBox(height: 16),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage,
                        style: TextStyle(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // 加载中
            if (_isLoading) ...[
              SizedBox(height: 20),
              Center(
                child: CircularProgressIndicator(),
              ),
            ],

            // 焊口信息展示
            if (_weldingJoint != null && !_isLoading) ...[
              SizedBox(height: 20),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    '焊口信息',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  OutlinedButton.icon(
                                    icon: Icon(Icons.qr_code),
                                    label: Text('生成二维码'),
                                    onPressed: _generateQRCode,
                                  ),
                                ],
                              ),
                              Divider(),
                              _buildDetailItem('焊口ID', _weldingJoint!.id),
                              _buildDetailItem('焊口编号', _weldingJoint!.code),
                              _buildDetailItem(
                                  '项目ID', _weldingJoint!.projectId),
                              _buildDetailItem(
                                  '管段ID', _weldingJoint!.pipeSegmentId),
                              if (_weldingJoint!.status != null)
                                _buildDetailItem('状态', _weldingJoint!.status!),
                              if (_weldingJoint!.createdAt != null)
                                _buildDetailItem(
                                    '创建时间', _weldingJoint!.createdAt!),
                              if (_weldingJoint!.updatedAt != null)
                                _buildDetailItem(
                                    '更新时间', _weldingJoint!.updatedAt!),
                              if (_weldingJoint!.description != null)
                                _buildDetailItem(
                                    '描述', _weldingJoint!.description!),

                              // 额外信息
                              if (_weldingJoint!.additionalInfo.isNotEmpty) ...[
                                SizedBox(height: 16),
                                Text(
                                  '额外信息',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: 8),
                                ...(_weldingJoint!.additionalInfo.entries
                                    .map((entry) {
                                  return _buildDetailItem(
                                    entry.key,
                                    entry.value.toString(),
                                  );
                                }).toList()),
                              ],
                            ],
                          ),
                        ),
                      ),

                      // 二维码显示
                      if (_qrCodeData != null) ...[
                        SizedBox(height: 20),
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  '焊口二维码',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: 16),
                                Center(
                                  child: QrImageView(
                                    data: _qrCodeData!,
                                    version: QrVersions.auto,
                                    size: 200.0,
                                    backgroundColor: Colors.white,
                                  ),
                                ),
                                SizedBox(height: 16),
                                OutlinedButton.icon(
                                  icon: Icon(Icons.print),
                                  label: Text('打印二维码'),
                                  onPressed: () {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text('打印功能将在未来版本提供'),
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
