import 'package:flutter/material.dart';
import '../../services/bluetooth_service.dart';
import '../../services/project_service.dart';
import '../../services/command_service.dart';
import '../../models/project_model.dart';

class ProjectTab extends StatefulWidget {
  final BleService bleService;

  const ProjectTab({Key? key, required this.bleService}) : super(key: key);

  @override
  _ProjectTabState createState() => _ProjectTabState();
}

class _ProjectTabState extends State<ProjectTab> {
  final ProjectService _projectService = ProjectService();
  final CommandService _commandService = CommandService();
  List<Project> _projects = [];
  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  Future<void> _loadProjects() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final projects = await _projectService.getUserProjects();
      setState(() {
        _projects = projects;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = '加载项目失败: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部标题和刷新按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '项目列表',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              _isLoading
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : IconButton(
                      icon: const Icon(Icons.refresh),
                      onPressed: _loadProjects,
                      tooltip: '刷新项目列表',
                    ),
            ],
          ),
          const SizedBox(height: 16),

          // 项目统计信息
          if (_projects.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.info_outline, color: Colors.blue),
                  const SizedBox(width: 8),
                  Text(
                    '共 ${_projects.length} 个项目',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ],
              ),
            ),
          const SizedBox(height: 16),

          // 错误信息
          if (_errorMessage.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.red),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ),

          // 项目列表
          Expanded(
            child: _projects.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.folder_off,
                          size: 64,
                          color: Colors.grey.withOpacity(0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _isLoading ? '加载中...' : '暂无项目',
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 16,
                          ),
                        ),
                        if (!_isLoading)
                          TextButton.icon(
                            icon: const Icon(Icons.refresh),
                            label: const Text('刷新'),
                            onPressed: _loadProjects,
                          ),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: _projects.length,
                    itemBuilder: (context, index) {
                      final project = _projects[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 12),
                        clipBehavior: Clip.antiAlias,
                        child: InkWell(
                          onTap: () => _showProjectDetails(context, project),
                          child: _buildProjectItem(project),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildProjectItem(Project project) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.blue.shade50,
              child: const Icon(
                Icons.business,
                color: Colors.blue,
                size: 24,
              ),
            ),
            title: Text(
              project.name,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Text(
              project.address,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 14),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
            child: Row(
              children: [
                Chip(
                  label: Text(
                    '编号: ${project.code}',
                    style: const TextStyle(fontSize: 12),
                  ),
                  backgroundColor: Colors.grey.shade100,
                  labelPadding: const EdgeInsets.symmetric(horizontal: 8),
                  padding: EdgeInsets.zero,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                const SizedBox(width: 8),
                Chip(
                  label: Text(
                    project.status,
                    style: const TextStyle(fontSize: 12),
                  ),
                  backgroundColor: _getStatusColor(project.status),
                  labelPadding: const EdgeInsets.symmetric(horizontal: 8),
                  padding: EdgeInsets.zero,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case '进行中':
        return Colors.green.shade100;
      case '已完成':
        return Colors.blue.shade100;
      case '已暂停':
        return Colors.orange.shade100;
      case '已取消':
        return Colors.red.shade100;
      default:
        return Colors.grey.shade100;
    }
  }

  void _showProjectDetails(BuildContext context, Project project) {
    final bool isDeviceConnected = widget.bleService.isConnected;

    // 获取ScaffoldMessenger引用
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.business, color: Colors.blue),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                project.name,
                style: const TextStyle(fontSize: 18),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailItem('项目ID', project.id),
              _buildDetailItem('项目编号', project.code),
              _buildDetailItem('项目地址', project.address),
              _buildDetailItem('施工单位', project.constructionUnit),
              _buildDetailItem('状态', project.status),
              if (project.startDate != null)
                _buildDetailItem('开始日期', project.startDate!),
              if (project.endDate != null)
                _buildDetailItem('结束日期', project.endDate!),
              if (project.description != null)
                _buildDetailItem('描述', project.description!),
            ],
          ),
        ),
        actions: [
          TextButton.icon(
            icon: const Icon(Icons.check_circle_outline, size: 18),
            label: const Text('设为当前项目'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.green,
            ),
            onPressed: () async {
              try {
                // 保存为当前项目
                await _projectService.saveCurrentProject(project);
                // 弹出成功提示
                Navigator.of(context).pop(); // 关闭对话框
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text('已将"${project.name}"设置为当前项目'),
                    backgroundColor: Colors.green,
                    duration: const Duration(seconds: 2),
                  ),
                );
              } catch (e) {
                // 出错处理
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text('设置当前项目失败: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(fontSize: 14),
          ),
          Divider(height: 16),
        ],
      ),
    );
  }

  // 发送项目信息到设备
  Future<void> _sendProjectInfoToDevice(Project project) async {
    if (!widget.bleService.isConnected) {
      throw Exception('设备未连接');
    }

    try {
      // 记录项目信息日志
      widget.bleService.addLog('【项目模块】准备发送项目信息到焊机');
      widget.bleService.addLog('【项目模块】=========================');
      widget.bleService.addLog('【项目模块】项目ID: ${project.id}');
      widget.bleService.addLog('【项目模块】项目名称: ${project.name}');
      widget.bleService.addLog('【项目模块】项目编号: ${project.code}');
      widget.bleService.addLog('【项目模块】项目地址: ${project.address}');
      widget.bleService.addLog('【项目模块】施工单位: ${project.constructionUnit}');
      widget.bleService.addLog('【项目模块】项目状态: ${project.status}');
      if (project.startDate != null) {
        widget.bleService.addLog('【项目模块】开始日期: ${project.startDate}');
      }
      if (project.endDate != null) {
        widget.bleService.addLog('【项目模块】结束日期: ${project.endDate}');
      }
      if (project.description != null) {
        widget.bleService.addLog('【项目模块】项目描述: ${project.description}');
      }
      widget.bleService.addLog('【项目模块】=========================');

      // 预先记录命令详情
      _commandService.logProjectCommands(
          project.code, project.name, project.address);
      widget.bleService.addLog('【项目模块】开始发送项目命令...');

      // 1. 发送项目地址
      widget.bleService.addLog('【项目模块】开始发送项目地址命令');
      final addressCommand =
          _commandService.buildProjectAddressCommand(project.address);
      if (addressCommand.isEmpty) {
        widget.bleService.addLog('【项目模块】错误：项目地址命令构建失败');
        throw Exception('项目地址命令构建失败');
      }

      // 转换为十六进制显示
      final addressHex = _commandService.bytesToHexString(addressCommand);
      widget.bleService.addLog('【项目模块】项目地址命令HEX: $addressHex');

      // 命令结构详情
      String addressDetails = _commandService.formatCommand(addressCommand);
      widget.bleService.addLog('【项目模块】地址命令解析:\n$addressDetails');

      // 发送命令
      await widget.bleService.sendData(addressCommand);
      widget.bleService.addLog('【项目模块】项目地址命令已发送');
      await Future.delayed(const Duration(milliseconds: 500));

      // 2. 发送项目编号
      widget.bleService.addLog('【项目模块】开始发送项目编号命令');
      final codeCommand = _commandService.buildProjectCodeCommand(project.code);
      if (codeCommand.isEmpty) {
        widget.bleService.addLog('【项目模块】错误：项目编号命令构建失败');
        throw Exception('项目编号命令构建失败');
      }

      // 转换为十六进制显示
      final codeHex = _commandService.bytesToHexString(codeCommand);
      widget.bleService.addLog('【项目模块】项目编号命令HEX: $codeHex');

      // 命令结构详情
      String codeDetails = _commandService.formatCommand(codeCommand);
      widget.bleService.addLog('【项目模块】编号命令解析:\n$codeDetails');

      // 发送命令
      await widget.bleService.sendData(codeCommand);
      widget.bleService.addLog('【项目模块】项目编号命令已发送');
      await Future.delayed(const Duration(milliseconds: 500));

      // 3. 发送项目名称
      widget.bleService.addLog('【项目模块】开始发送项目名称命令');
      final nameCommand = _commandService.buildProjectNameCommand(project.name);
      if (nameCommand.isEmpty) {
        widget.bleService.addLog('【项目模块】错误：项目名称命令构建失败');
        throw Exception('项目名称命令构建失败');
      }

      // 转换为十六进制显示
      final nameHex = _commandService.bytesToHexString(nameCommand);
      widget.bleService.addLog('【项目模块】项目名称命令HEX: $nameHex');

      // 命令结构详情
      String nameDetails = _commandService.formatCommand(nameCommand);
      widget.bleService.addLog('【项目模块】名称命令解析:\n$nameDetails');

      // 发送命令
      await widget.bleService.sendData(nameCommand);
      widget.bleService.addLog('【项目模块】项目名称命令已发送');

      // 确认完成
      widget.bleService.addLog('【项目模块】=========================');
      widget.bleService.addLog('【项目模块】项目信息发送完成！共发送3个命令：');
      widget.bleService.addLog('【项目模块】1. 项目地址：${project.address}');
      widget.bleService.addLog('【项目模块】2. 项目编号：${project.code}');
      widget.bleService.addLog('【项目模块】3. 项目名称：${project.name}');
      widget.bleService.addLog('【项目模块】=========================');
    } catch (e) {
      widget.bleService.addLog('【项目模块】发送项目信息失败: $e');
      throw Exception('发送项目信息失败: $e');
    }
  }
}
