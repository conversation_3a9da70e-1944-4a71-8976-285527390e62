import 'package:flutter/material.dart';
import '../../screens/queries/welding_joint_query_screen.dart';
import '../../screens/queries/device_query_screen.dart';
import '../../screens/queries/user_query_screen.dart';
import '../../screens/queries/qr_code_screen.dart';
import '../../screens/queries/weld_joint_list_screen.dart';

class QueryTab extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildQueryCard('焊口信息查询', '通过焊口编号查询', context, () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => WeldingJointQueryScreen(),
            ),
          );
        }),
        _buildQueryCard('设备查询', '通过项目ID查询可用设备', context, () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => DeviceQueryScreen(),
            ),
          );
        }),
        _buildQueryCard('用户信息查询', '通过用户ID查询', context, () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => UserQueryScreen(),
            ),
          );
        }),
        _buildQueryCard('管段信息查询', '通过项目ID查询', context, () {
          _showComingSoonDialog(context, '管段信息查询');
        }),
        _buildQueryCard('焊口号列表', '通过项目ID查询', context, () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => WeldJointListScreen(),
            ),
          );
        }),
        _buildQueryCard('二维码生成打印', '焊口二维码生成与打印', context, () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => QRCodeScreen(),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildQueryCard(
      String title, String subtitle, BuildContext context, VoidCallback onTap) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListTile(
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }

  void _showComingSoonDialog(BuildContext context, String featureName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.info_outline, color: Colors.blue),
            const SizedBox(width: 8),
            Text('$featureName 即将推出'),
          ],
        ),
        content: const Text('此功能正在开发中，将在未来版本中提供。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('了解'),
          ),
        ],
      ),
    );
  }
}
