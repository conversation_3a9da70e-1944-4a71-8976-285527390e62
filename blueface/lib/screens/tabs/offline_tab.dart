import 'package:flutter/material.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../../services/project_service.dart';
import '../../services/offline_mode_service.dart';
import '../../services/network_service.dart';
import '../../models/project_model.dart';
import '../../models/offline_data_model.dart';
import '../../services/user_service.dart';
import '../offline/offline_projects_screen.dart';
import '../offline/pending_welds_screen.dart';
import '../offline/offline_joints_screen.dart';
import '../offline/offline_users_screen.dart';
import '../offline/sync_settings_screen.dart';

class OfflineTab extends StatefulWidget {
  @override
  _OfflineTabState createState() => _OfflineTabState();
}

class _OfflineTabState extends State<OfflineTab> {
  final ProjectService _projectService = ProjectService();
  final UserService _userService = UserService();
  final OfflineModeService _offlineModeService = OfflineModeService();
  final NetworkService _networkService = NetworkService();

  bool _isSyncing = false;
  bool _isLoadingData = false;
  String _lastSyncTime = '从未同步';
  int _cachedProjectsCount = 0;
  int _cachedWeldJointsCount = 0;
  int _pendingUploadsCount = 0;
  double _usedStorage = 0;
  String _error = '';
  Project? _currentProject;

  // 新增离线模式状态
  OfflineModeState _offlineState = OfflineModeState(
    isOfflineMode: false,
    pendingUploads: 0,
  );
  Map<String, int> _storageStats = {};

  @override
  void initState() {
    super.initState();
    _initializeOfflineMode();
    _loadOfflineData();
  }

  // 初始化离线模式
  Future<void> _initializeOfflineMode() async {
    try {
      await _offlineModeService.initialize();

      // 只监听离线状态变化，不监听网络状态
      _offlineModeService.stateStream.listen((state) {
        if (mounted) {
          setState(() {
            _offlineState = state;
          });
        }
      });

      // 获取当前状态
      setState(() {
        _offlineState = _offlineModeService.currentState;
      });
    } catch (e) {
      print('初始化离线模式失败: $e');
    }
  }

  Future<void> _loadOfflineData() async {
    setState(() {
      _isLoadingData = true;
      _error = '';
    });

    try {
      // 获取当前项目
      _currentProject = await _projectService.getCurrentProject();

      // 获取离线模式数据统计
      final stats = await _offlineModeService.getStorageStats();
      setState(() {
        _storageStats = stats;
        _pendingUploadsCount =
            stats['pendingOfflineData']! + stats['pendingWeldingData']!;
      });

      // 获取最后同步时间
      if (_offlineState.lastSyncTime != null) {
        setState(() {
          _lastSyncTime = _formatDateTime(_offlineState.lastSyncTime!);
        });
      }

      // 获取缓存项目数量
      final cachedProjects = await _projectService.getCachedProjects();
      setState(() {
        _cachedProjectsCount = cachedProjects.length;
      });

      // 计算已用存储空间
      final storageSize = await _calculateStorageSize();
      setState(() {
        _usedStorage = storageSize;
      });

      // 加载完成
      setState(() {
        _isLoadingData = false;
      });
    } catch (e) {
      setState(() {
        _error = '加载离线数据失败: $e';
        _isLoadingData = false;
      });
    }
  }

  // 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // 同步数据
  Future<void> _syncData() async {
    if (_isSyncing) return;

    // 检查实际网络状态
    final isOnline = await _networkService.checkNetworkStatus();
    if (!isOnline) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('网络连接不可用，无法同步数据'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isSyncing = true;
      _error = '';
    });

    try {
      // 展示同步进度对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => WillPopScope(
          onWillPop: () async => false,
          child: AlertDialog(
            title: Text('正在同步数据'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                LinearProgressIndicator(),
                SizedBox(height: 16),
                Text('正在同步离线数据到服务器...'),
              ],
            ),
          ),
        ),
      );

      // 使用新的离线模式服务同步数据
      final result = await _offlineModeService.manualSyncData();

      // 关闭对话框
      Navigator.of(context).pop();

      if (result) {
        // 重新加载离线数据
        await _loadOfflineData();

        // 显示成功消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('数据同步成功！'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        throw Exception('同步失败');
      }
    } catch (e) {
      // 关闭对话框
      if (Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      setState(() {
        _error = '同步数据失败: $e';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('同步失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isSyncing = false;
      });
    }
  }

  // 检查待上传数据
  Future<int> _checkPendingUploads() async {
    // 这里应该实现检查本地待上传数据的逻辑
    // 例如，检查特定目录中的暂存文件数量
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final pendingDir = Directory('${appDir.path}/pending_uploads');

      if (!await pendingDir.exists()) {
        return 0;
      }

      final files = await pendingDir.list().toList();
      return files.length;
    } catch (e) {
      print('检查待上传数据错误: $e');
      return 0;
    }
  }

  // 计算已用存储空间（MB）
  Future<double> _calculateStorageSize() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final appDirSize = await _getDirSize(appDir);
      return appDirSize / (1024 * 1024); // 转换为MB
    } catch (e) {
      print('计算存储空间错误: $e');
      return 0;
    }
  }

  // 获取目录大小（字节）
  Future<int> _getDirSize(Directory dir) async {
    int size = 0;
    try {
      final items = dir.listSync(recursive: true, followLinks: false);
      for (var item in items) {
        if (item is File) {
          size += await item.length();
        }
      }
      return size;
    } catch (e) {
      print('获取目录大小错误: $e');
      return 0;
    }
  }

  // 上传待上传数据
  Future<void> _uploadPendingData() async {
    // 这里应该实现上传待上传数据的逻辑
    await Future.delayed(Duration(seconds: 1));
    // 示例中只是模拟上传过程
  }

  // 下载最新数据
  Future<void> _downloadLatestData() async {
    // 这里应该实现下载最新数据的逻辑
    await Future.delayed(Duration(seconds: 1));
    // 示例中只是模拟下载过程
  }

  // 清除所有缓存数据
  Future<void> _clearAllCache() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('确认清除'),
        content: Text('确定要清除所有缓存数据吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              setState(() {
                _isLoadingData = true;
              });

              try {
                // 清除应用文档目录中的数据
                final appDir = await getApplicationDocumentsDirectory();
                final cacheDir = Directory('${appDir.path}/cache');
                if (await cacheDir.exists()) {
                  await cacheDir.delete(recursive: true);
                }

                // 重新创建空目录
                await cacheDir.create();

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('缓存数据已清除'),
                    backgroundColor: Colors.green,
                  ),
                );

                // 重新加载数据
                await _loadOfflineData();
              } catch (e) {
                setState(() {
                  _error = '清除缓存失败: $e';
                  _isLoadingData = false;
                });
              }
            },
            child: Text('确定', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // 离线状态提示
          Container(
            padding: const EdgeInsets.all(16),
            color: _offlineState.isOfflineMode
                ? Colors.orange[100]
                : Colors.green[100],
            child: Row(
              children: [
                Icon(
                  _offlineState.isOfflineMode ? Icons.wifi_off : Icons.wifi,
                  color: _offlineState.isOfflineMode
                      ? Colors.orange
                      : Colors.green,
                ),
                SizedBox(width: 8),
                Text(
                  _offlineState.isOfflineMode ? '离线模式运行中' : '在线模式 - 可同步数据到服务器',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _offlineState.isOfflineMode
                        ? Colors.orange[700]
                        : Colors.green[700],
                  ),
                ),
                Spacer(),
                if (_offlineState.pendingUploads > 0)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${_offlineState.pendingUploads}条待上传',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // 错误信息显示
          if (_error.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _error,
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ),

          // 统计信息卡片
          if (!_isLoadingData)
            Padding(
              padding: const EdgeInsets.all(16),
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '离线数据统计',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Divider(),
                      SizedBox(height: 8),
                      _buildStatItem('运行模式',
                          _offlineState.isOfflineMode ? '离线模式' : '在线模式'),
                      _buildStatItem('最后同步时间', _lastSyncTime),
                      _buildStatItem('当前离线项目', _currentProject?.name ?? '无'),
                      _buildStatItem('缓存项目数量', '$_cachedProjectsCount 个'),
                      _buildStatItem('离线数据总数',
                          '${_storageStats['totalOfflineData'] ?? 0} 条'),
                      _buildStatItem('焊接数据总数',
                          '${_storageStats['totalWeldingData'] ?? 0} 条'),
                      _buildStatItem(
                          '待上传数据', '${_offlineState.pendingUploads} 条'),
                      _buildStatItem(
                          '已用存储空间', '${_usedStorage.toStringAsFixed(2)} MB'),
                      SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          ElevatedButton.icon(
                            icon: Icon(
                                _isSyncing ? Icons.hourglass_top : Icons.sync),
                            label: Text(_isSyncing ? '同步中...' : '立即同步'),
                            onPressed: _isSyncing ? null : _syncData,
                          ),
                          OutlinedButton.icon(
                            icon: Icon(Icons.delete_outline, color: Colors.red),
                            label: Text('清除缓存',
                                style: TextStyle(color: Colors.red)),
                            onPressed: _clearAllCache,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),

          // 加载中指示器
          if (_isLoadingData)
            Padding(
              padding: const EdgeInsets.all(32.0),
              child: Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('正在加载离线数据...'),
                  ],
                ),
              ),
            ),

          // 功能列表
          if (!_isLoadingData)
            Expanded(
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  // 新增：离线数据管理入口
                  _buildOfflineSection('离线数据管理', Icons.storage, context,
                      () => Navigator.pushNamed(context, '/offlineData')),

                  // 原有功能保持不变
                  _buildOfflineSection('离线项目管理', Icons.folder, context,
                      () => _navigateToOfflineSection('offline_projects')),
                  _buildOfflineSection('待上传焊接记录', Icons.upload_file, context,
                      () => _navigateToOfflineSection('pending_uploads')),
                  _buildOfflineSection('离线焊口数据', Icons.line_axis, context,
                      () => _navigateToOfflineSection('offline_joints')),
                  _buildOfflineSection('本地人员信息', Icons.people, context,
                      () => _navigateToOfflineSection('offline_users')),
                  _buildOfflineSection(
                      '数据同步设置',
                      Icons.settings_suggest,
                      context,
                      () => _navigateToOfflineSection('sync_settings')),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // 导航到离线功能页面
  void _navigateToOfflineSection(String section) {
    switch (section) {
      case 'offline_projects':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => OfflineProjectsScreen(),
          ),
        );
        break;
      case 'pending_uploads':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PendingWeldsScreen(),
          ),
        );
        break;
      case 'offline_joints':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const OfflineJointsScreen(),
          ),
        );
        break;
      case 'offline_users':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const OfflineUsersScreen(),
          ),
        );
        break;
      case 'sync_settings':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const SyncSettingsScreen(),
          ),
        );
        break;
      default:
        // 对于尚未实现的功能，仍然显示"即将推出"对话框
        _showComingSoonDialog(context, _getSectionTitle(section));
    }
  }

  // 获取离线功能的标题
  String _getSectionTitle(String section) {
    switch (section) {
      case 'offline_projects':
        return '离线项目管理';
      case 'pending_uploads':
        return '待上传焊接记录';
      case 'offline_joints':
        return '离线焊口数据';
      case 'offline_users':
        return '本地人员信息';
      case 'sync_settings':
        return '数据同步设置';
      default:
        return '离线功能';
    }
  }

  Widget _buildStatItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOfflineSection(
      String title, IconData icon, BuildContext context, VoidCallback onTap) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListTile(
        leading: Icon(icon, color: Colors.blue),
        title: Text(title),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  void _showComingSoonDialog(BuildContext context, String featureName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.info_outline, color: Colors.blue),
            const SizedBox(width: 8),
            Expanded(
              child: Text('$featureName 即将推出'),
            ),
          ],
        ),
        content: const Text('此功能正在开发中，将在未来版本中提供。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('了解'),
          ),
        ],
      ),
    );
  }
}
