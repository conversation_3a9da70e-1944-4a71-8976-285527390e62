import 'package:flutter/material.dart';
import 'dart:async';
import '../../services/bluetooth_service.dart';
import 'package:blueface/services/user_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class WeldingTab extends StatefulWidget {
  final BleService bleService;
  final VoidCallback? onSwitchToProjectTab;

  const WeldingTab({
    Key? key,
    required this.bleService,
    this.onSwitchToProjectTab,
  }) : super(key: key);

  @override
  _WeldingTabState createState() => _WeldingTabState();
}

class _WeldingTabState extends State<WeldingTab> {
  final TextEditingController _sendDataController = TextEditingController();
  final List<String> _receivedDataList = [];

  // 添加订阅对象变量，以便在dispose时取消订阅
  StreamSubscription? _receivedDataSubscription;
  StreamSubscription? _logSubscription;
  StreamSubscription? _connectionSubscription;

  // 添加 UserService 实例和状态变量
  final UserService _userService = UserService();
  String? _operatorName;

  @override
  void initState() {
    super.initState();
    // 订阅蓝牙数据接收
    _receivedDataSubscription =
        widget.bleService.receivedDataStream.listen((data) {
      if (mounted) {
        setState(() {
          // 检查是否是特殊格式的焊机编号消息
          if (data.startsWith('焊机编号:')) {
            _receivedDataList.add('↓ 接收: $data');
          } else {
            _receivedDataList.add('↓ 接收: $data');
          }
          // 保持最多50条记录
          if (_receivedDataList.length > 50) {
            _receivedDataList.removeAt(0);
          }
        });
      }
    });

    // 订阅蓝牙日志
    _logSubscription = widget.bleService.logStream.listen((log) {
      // 如果日志中包含特定关键词，添加到数据接收记录中
      if (log.contains('发送数据') ||
          log.contains('接收数据') ||
          log.contains('发送特定命令') ||
          log.contains('【命令服务】') ||
          log.contains('焊机编号')) {
        if (mounted) {
          setState(() {
            // 区分发送和接收
            String prefix = '';
            if (log.contains('发送数据') ||
                log.contains('发送特定命令') ||
                log.contains('【命令服务】发送') ||
                log.contains('【命令服务】构建')) {
              prefix = '↑ '; // 添加向上箭头表示发送
            } else if (log.contains('接收数据') || log.contains('焊机编号')) {
              prefix = '↓ '; // 添加向下箭头表示接收
            }

            _receivedDataList.add(prefix + log);
            // 保持最多50条记录
            if (_receivedDataList.length > 50) {
              _receivedDataList.removeAt(0);
            }
          });
        }
      }
    });

    // 添加连接设备状态变化监听，确保UI状态同步
    _connectionSubscription =
        widget.bleService.connectedDeviceStream.listen((_) {
      if (mounted) {
        setState(() {
          // 更新UI状态
        });
      }
    });

    _setupListeners();
    _loadOperatorName(); // 加载操作员名称
  }

  void _setupListeners() {
    // 添加加载操作员名称的方法
    _loadOperatorName();
  }

  // 添加加载操作员名称的方法
  Future<void> _loadOperatorName() async {
    final name = await _userService.getUserName();
    if (mounted) {
      // 检查 Widget 是否还在树中
      setState(() {
        _operatorName = name ?? '未登录'; // 如果获取不到，显示'未登录'
      });
    }
  }

  @override
  void dispose() {
    // 取消所有订阅
    _receivedDataSubscription?.cancel();
    _logSubscription?.cancel();
    _connectionSubscription?.cancel();
    _sendDataController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 状态信息区
          _buildStatusCard(),

          // 设备区域，只有当没有设备连接时才显示
          _buildDevicesList(),

          // 添加测试发送区域（仅在有连接的设备时显示）
          if (widget.bleService.isConnected) _buildSendDataCard(),

          // 主要操作按钮区域
          _buildActionButtons(),

          // 日志区域
          _buildLogsCard(),
        ],
      ),
    );
  }

  Widget _buildStatusCard() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(
                    widget.bleService.isConnected
                        ? Icons.bluetooth_connected
                        : Icons.bluetooth_disabled,
                    color: widget.bleService.isConnected
                        ? Colors.green
                        : Colors.red,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      widget.bleService.isConnected
                          ? '已连接: ${widget.bleService.connectedDevice?.name}'
                          : '设备未连接',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                  if (widget.bleService.isConnected)
                    OutlinedButton(
                      onPressed: () async {
                        await widget.bleService.disconnectDevice();
                        setState(() {}); // 刷新UI
                      },
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 0),
                        minimumSize: const Size(0, 30),
                      ),
                      child: const Text('断开连接'),
                    ),
                ],
              ),
              const Divider(),
              Row(
                children: [
                  const Icon(Icons.person, size: 18, color: Colors.blue),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text('当前操作员: ${_operatorName ?? "加载中..."}', // 使用状态变量
                        style: const TextStyle(fontWeight: FontWeight.w500)),
                  ),
                  if (!widget.bleService.isConnected)
                    OutlinedButton(
                      onPressed: widget.bleService.isScanning
                          ? null
                          : () async {
                              setState(() {}); // 更新UI
                              await widget.bleService.startScan();
                              setState(() {}); // 刷新扫描结果
                            },
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 0),
                        minimumSize: const Size(0, 30),
                      ),
                      child: Text(
                        widget.bleService.isScanning ? '扫描中...' : '扫描设备',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDevicesList() {
    // 使用StreamBuilder来监听设备列表变化
    return StreamBuilder<List<dynamic>>(
      stream: widget.bleService.devicesStream,
      initialData: widget.bleService.devices,
      builder: (context, snapshot) {
        final devices = snapshot.data ?? [];

        // 如果已连接设备或者没有设备，不显示此部分
        if (widget.bleService.isConnected) {
          return const SizedBox.shrink();
        }

        // 即使没有设备，也显示列表区域，提供视觉反馈
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8.0),
                    child: Row(
                      children: [
                        const Icon(Icons.bluetooth_searching,
                            size: 16, color: Colors.blue),
                        const SizedBox(width: 8),
                        const Text(
                          '蓝牙设备',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '(${devices.length})',
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                        const Spacer(),
                        // 添加刷新按钮
                        if (!widget.bleService.isScanning)
                          IconButton(
                            icon: const Icon(Icons.refresh, size: 18),
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                            onPressed: () async {
                              setState(() {}); // 更新UI
                              await widget.bleService.startScan();
                              setState(() {}); // 刷新扫描结果
                            },
                          ),
                        if (widget.bleService.isScanning)
                          const SizedBox(
                            width: 18,
                            height: 18,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.blue),
                            ),
                          ),
                      ],
                    ),
                  ),
                  if (devices.isEmpty)
                    Container(
                      height: 70,
                      alignment: Alignment.center,
                      child: Text(
                        widget.bleService.isScanning
                            ? '正在扫描设备...'
                            : '未发现设备，点击刷新按钮重新扫描',
                        style: TextStyle(color: Colors.grey[600], fontSize: 13),
                      ),
                    )
                  else
                    SizedBox(
                      height: 95,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: devices.length,
                        itemBuilder: (context, index) => SizedBox(
                          width: 180,
                          child: _buildDeviceItem(devices[index]),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDeviceItem(device) {
    return Card(
      margin: const EdgeInsets.only(right: 8, bottom: 4),
      child: InkWell(
        onTap: () async {
          // 连接到设备
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('正在连接到设备: ${device.name}...'),
              duration: const Duration(seconds: 1),
            ),
          );

          bool success = await widget.bleService.connectToDevice(device);
          setState(() {}); // 刷新UI

          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('已成功连接到设备: ${device.name}'),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 2),
              ),
            );

            // 延迟1秒后发送特定命令
            Future.delayed(const Duration(seconds: 1), () {
              // 显示正在发送命令提示
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('正在发送命令: 01 03 00 05 00 05 95 C8'),
                  duration: Duration(seconds: 1),
                ),
              );

              // 发送特定命令
              widget.bleService.sendSpecificCommand().then((success) {
                // 根据发送结果显示提示
                if (success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('命令发送成功'),
                      backgroundColor: Colors.green,
                      duration: Duration(seconds: 2),
                    ),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('命令发送失败'),
                      backgroundColor: Colors.red,
                      duration: Duration(seconds: 2),
                    ),
                  );
                }
              });
            });
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('连接到设备 ${device.name} 失败'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.bluetooth, size: 16, color: Colors.blue),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      device.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                device.id.toString(),
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[600],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              const Align(
                alignment: Alignment.centerRight,
                child: Text(
                  '点击连接',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.blue,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSendDataCard() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: const [
                  Icon(Icons.send, color: Colors.blue, size: 16),
                  SizedBox(width: 8),
                  Text(
                    '测试数据发送',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _sendDataController,
                      decoration: const InputDecoration(
                        hintText: 'HEX (如: AA 55 01) 或 文本',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                        isDense: true,
                      ),
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () => _sendTestData(),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      minimumSize: const Size(0, 35),
                    ),
                    child: const Text('发送'),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // 添加快捷预设命令按钮
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  OutlinedButton(
                    onPressed: () {
                      _sendDataController.text = 'AA 55';
                      _sendTestData();
                    },
                    child: const Text('AA55', style: TextStyle(fontSize: 12)),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      minimumSize: const Size(0, 30),
                    ),
                  ),
                  OutlinedButton(
                    onPressed: () {
                      _sendDataController.text = '01 03 00 05 00 05 95 C8';
                      _sendTestData();
                    },
                    child: const Text('查询', style: TextStyle(fontSize: 12)),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      minimumSize: const Size(0, 30),
                    ),
                  ),
                  OutlinedButton(
                    onPressed: () {
                      _sendDataController.text = 'AA 55 02 01';
                      _sendTestData();
                    },
                    child: const Text('命令1', style: TextStyle(fontSize: 12)),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      minimumSize: const Size(0, 30),
                    ),
                  ),
                  OutlinedButton(
                    onPressed: () {
                      _sendDataController.text = 'AA 55 03 02';
                      _sendTestData();
                    },
                    child: const Text('命令2', style: TextStyle(fontSize: 12)),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      minimumSize: const Size(0, 30),
                    ),
                  ),
                  OutlinedButton(
                    onPressed: () {
                      _sendDataController.text =
                          '01 10 01 4F 00 04 08 31 30 34 2E 34 38 30 36 A4 92';
                    },
                    child: const Text('经度测试', style: TextStyle(fontSize: 12)),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      minimumSize: const Size(0, 30),
                      backgroundColor: Colors.orange.shade50,
                      foregroundColor: Colors.orange.shade700,
                    ),
                  ),
                  OutlinedButton(
                    onPressed: () {
                      _sendDataController.text =
                          '01 10 01 63 00 04 08 33 36 2E 33 30 35 35 36 AF 7F';
                      _sendTestData();
                    },
                    child: const Text('纬度测试', style: TextStyle(fontSize: 12)),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      minimumSize: const Size(0, 30),
                      backgroundColor: Colors.blue.shade50,
                      foregroundColor: Colors.blue.shade700,
                    ),
                  ),
                  OutlinedButton(
                    onPressed: () {
                      _sendDataController.text = '01 06 01 68 00 0F 49 EE';
                      _sendTestData();
                    },
                    child: const Text('海拔测试', style: TextStyle(fontSize: 12)),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      minimumSize: const Size(0, 30),
                      backgroundColor: Colors.green.shade50,
                      foregroundColor: Colors.green.shade700,
                    ),
                  ),
                  OutlinedButton(
                    onPressed: () {
                      _sendDataController.text = '';
                    },
                    child: const Text('清空', style: TextStyle(fontSize: 12)),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      minimumSize: const Size(0, 30),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '焊接作业',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 16),
              GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                childAspectRatio: 2.5,
                mainAxisSpacing: 12,
                crossAxisSpacing: 12,
                children: [
                  _buildActionButton(
                    icon: Icons.face,
                    label: '人脸识别',
                    onPressed: () {
                      Navigator.pushNamed(context, '/faceDetection');
                    },
                  ),
                  _buildActionButton(
                    icon: Icons.camera_alt,
                    label: '拍摄设备照片',
                    onPressed: widget.bleService.isConnected
                        ? () {
                            Navigator.pushNamed(context, '/devicePhoto');
                          }
                        : null,
                  ),
                  _buildActionButton(
                    icon: Icons.photo_camera,
                    label: '拍摄管道照片',
                    onPressed: widget.bleService.isConnected
                        ? () {
                            Navigator.pushNamed(context, '/pipePhoto');
                          }
                        : null,
                  ),
                  _buildActionButton(
                    icon: Icons.settings,
                    label: '焊接参数配置',
                    onPressed: widget.bleService.isConnected
                        ? () {
                            // 检查是否已选择项目
                            _checkProjectSelected(
                              context,
                              () => Navigator.pushNamed(
                                  context, '/weldingConfig'),
                            );
                          }
                        : null,
                  ),
                  _buildActionButton(
                    icon: Icons.cloud_upload,
                    label: '数据上传',
                    onPressed: widget.bleService.isConnected
                        ? () {
                            Navigator.pushNamed(context, '/dataUpload');
                          }
                        : null,
                  ),
                  _buildActionButton(
                    icon: Icons.history,
                    label: '焊接数据历史',
                    onPressed: () {
                      Navigator.pushNamed(context, '/weldingDataHistory');
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
  }) {
    final bool isEnabled = onPressed != null;
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 18),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        foregroundColor: isEnabled ? Colors.white : Colors.grey[400],
        backgroundColor: isEnabled ? Colors.blue : Colors.grey[200],
        disabledForegroundColor: Colors.grey[400],
        disabledBackgroundColor: Colors.grey[200],
      ),
    );
  }

  Widget _buildLogsCard() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('通信日志', style: TextStyle(fontWeight: FontWeight.bold)),
                  Row(
                    children: [
                      // 添加测试 - 添加测试蓝牙接收按钮
                      if (widget.bleService.isConnected)
                        IconButton(
                          icon: Icon(Icons.sensors, size: 18),
                          tooltip: '测试接收',
                          onPressed: () {
                            _testBleReceiving();
                          },
                        ),
                      IconButton(
                        icon: Icon(Icons.delete, size: 18),
                        tooltip: '清空日志',
                        onPressed: () {
                          setState(() {
                            _receivedDataList.clear();
                          });
                        },
                      ),
                    ],
                  ),
                ],
              ),
              const Divider(),
              Container(
                height: 200,
                child: _receivedDataList.isEmpty
                    ? const Center(
                        child: Text('暂无通信日志'),
                      )
                    : ListView.builder(
                        itemCount: _receivedDataList.length,
                        reverse: true, // 最新的在上面
                        itemBuilder: (context, index) {
                          // 反向索引，因为要最新的在上面
                          int reverseIndex =
                              _receivedDataList.length - 1 - index;
                          String item = _receivedDataList[reverseIndex];

                          // 根据日志内容设置不同的颜色
                          Color textColor = Colors.black;
                          if (item.startsWith('↑')) {
                            textColor = Colors.blue; // 发送数据蓝色
                          } else if (item.startsWith('↓')) {
                            textColor = Colors.green; // 接收数据绿色
                          }

                          // 高亮错误日志
                          if (item.toLowerCase().contains('错误') ||
                              item.toLowerCase().contains('失败') ||
                              item.toLowerCase().contains('校验失败')) {
                            textColor = Colors.red;
                          }

                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Text(
                              item,
                              style: TextStyle(
                                fontSize: 11,
                                color: textColor,
                                fontFamily: 'Courier',
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 测试蓝牙接收功能
  void _testBleReceiving() {
    if (!widget.bleService.isConnected) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请先连接设备')),
      );
      return;
    }

    setState(() {
      _receivedDataList.add('→ 测试蓝牙接收');
    });

    // 创建几种不同的测试数据用于调试接收处理
    List<List<int>> testDataList = [
      // 测试1: 英文焊机编号 "TEST1"
      [
        0x01, 0x03, 0x0A, // 响应头部
        0x54, 0x45, 0x53, 0x54, 0x31, // 焊机编号"TEST1"
        0xCD, 0xAB // CRC校验码（示例）
      ],

      // 测试2: 中文焊机编号 "西部" (GBK编码: C9C2 CEF7)
      [
        0x01, 0x03, 0x0A, // 响应头部
        0xC9, 0xC2, 0xCE, 0xF7, 0x00, // 焊机编号"西部"+填充
        0xCD, 0xAB // CRC校验码（示例）
      ],

      // 测试3: 特殊十六进制编号，不可读字符
      [
        0x01, 0x03, 0x0A, // 响应头部
        0xA1, 0xB2, 0xC3, 0xD4, 0xE5, // 特殊编号
        0xCD, 0xAB // CRC校验码（示例）
      ]
    ];

    // 依次测试不同的数据
    for (var i = 0; i < testDataList.length; i++) {
      Future.delayed(Duration(milliseconds: 500 * i), () {
        widget.bleService.addLog('手动触发测试数据 #${i + 1}');
        widget.bleService.testReceiveData(testDataList[i]);
      });
    }
  }

  // 发送测试数据
  void _sendTestData() async {
    if (_sendDataController.text.isEmpty) return;

    try {
      List<int> data;
      String text = _sendDataController.text.trim();

      // 检查是否是十六进制格式
      final RegExp hexPattern = RegExp(r'^([0-9A-Fa-f]{2}\s?)+$');
      if (hexPattern.hasMatch(text)) {
        // 是十六进制
        data = [];
        text.split(' ').forEach((hex) {
          if (hex.isNotEmpty) {
            data.add(int.parse(hex, radix: 16));
          }
        });

        // 添加更明确的日志
        widget.bleService.addLog('【测试发送】正在发送HEX: $text');
      } else {
        // 普通文本
        data = text.codeUnits;

        // 将文本转换为十六进制显示
        String hexText = data
            .map((e) => e.toRadixString(16).padLeft(2, '0').toUpperCase())
            .join(' ');

        widget.bleService.addLog('【测试发送】正在发送文本: $text (HEX: $hexText)');
      }

      // 发送数据
      await widget.bleService.sendData(data);

      // 清空输入框
      _sendDataController.text = '';
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('发送数据错误: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // 读取设备信息并显示
  Future<void> _readDeviceInfo(BuildContext context) async {
    if (!widget.bleService.isConnected) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请先连接设备')),
      );
      return;
    }

    // 显示加载对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text('读取中...'),
        content: Container(
          height: 100,
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在读取设备信息，请稍候...'),
              ],
            ),
          ),
        ),
      ),
    );

    try {
      // 调用蓝牙服务读取设备信息
      final Map<String, String> deviceInfo =
          await widget.bleService.readDeviceInfoForUI();

      // 关闭加载对话框
      Navigator.of(context).pop();

      // 显示设备信息对话框
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.info, color: Colors.blue),
              SizedBox(width: 8),
              Text('设备信息'),
            ],
          ),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDeviceInfoItem(
                    '连接状态', deviceInfo['connectionStatus'] ?? '未知'),
                Divider(),
                _buildDeviceInfoItem(
                    '焊机编号', deviceInfo['machineNumber'] ?? '未知'),
                Divider(),
                _buildDeviceInfoItem(
                    '焊接标准', deviceInfo['weldingStandard'] ?? '未知'),
                Divider(),
                _buildDeviceInfoItem('焊机机型', deviceInfo['machineType'] ?? '未知'),
                Divider(),
                _buildDeviceInfoItem(
                    '油缸面积', deviceInfo['cylinderArea'] ?? '未知'),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('关闭'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _readDeviceInfo(context); // 重新读取
              },
              child: Text('刷新'),
            ),
          ],
        ),
      );
    } catch (e) {
      // 关闭加载对话框
      Navigator.of(context).pop();

      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('读取设备信息失败: $e')),
      );
    }
  }

  // 设备信息项
  Widget _buildDeviceInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 检查是否已选择项目
  void _checkProjectSelected(BuildContext context, VoidCallback onContinue) {
    // 显示提示对话框，告知用户需要先选择项目
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: const [
            Icon(Icons.info_outline, color: Colors.blue),
            SizedBox(width: 8),
            Text('项目信息提示'),
          ],
        ),
        content: const Text(
          '发送项目信息前，请确保您已经在"项目管理"标签页中选择了当前项目。\n\n您需要点击项目列表中的项目，然后点击"设为当前项目"按钮。',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 执行继续的操作
              onContinue();
            },
            child: const Text('继续'),
          ),
          TextButton(
            onPressed: () {
              // 关闭对话框，然后切换到项目管理标签
              Navigator.of(context).pop();
              // 通知父组件(HomeScreen)切换到项目管理页面
              if (widget.onSwitchToProjectTab != null) {
                widget.onSwitchToProjectTab!();
              }
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('请先在项目管理页面选择当前项目'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
            child: const Text('去选择项目'),
          ),
        ],
      ),
    );
  }
}
