import 'dart:async';
import 'package:flutter/material.dart';
import '../services/command_service.dart';
import '../services/bluetooth_service.dart';

class WeldingDataScreen extends StatefulWidget {
  final BleService bleService;
  final CommandService commandService;

  const WeldingDataScreen({
    Key? key,
    required this.bleService,
    required this.commandService,
  }) : super(key: key);

  @override
  State<WeldingDataScreen> createState() => _WeldingDataScreenState();
}

class _WeldingDataScreenState extends State<WeldingDataScreen> {
  // 存储焊接参数和状态
  Map<String, dynamic>? _weldingParameters;
  Map<String, dynamic>? _weldingStatus;
  Map<String, dynamic>? _machineInfo;
  String _statusMessage = '准备就绪';
  bool _isLoading = false;
  bool _isAutoRefresh = false;
  Timer? _refreshTimer;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 检查蓝牙连接状态并初始加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkBluetoothConnection();
    });
  }

  @override
  void dispose() {
    _cancelAutoRefresh();
    _scrollController.dispose();
    super.dispose();
  }

  // 检查蓝牙连接状态
  void _checkBluetoothConnection() {
    if (!widget.bleService.isConnected) {
      setState(() {
        _statusMessage = '请先连接蓝牙设备';
      });
      // 显示蓝牙未连接提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请先连接蓝牙设备'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 3),
        ),
      );
    } else {
      setState(() {
        _statusMessage = '蓝牙已连接，可以读取数据';
      });
      // 可选：自动加载机器信息
      _readMachineInfo();
    }
  }

  // 读取焊接参数
  Future<void> _readWeldingParameters() async {
    if (!widget.bleService.isConnected) {
      setState(() {
        _statusMessage = '请先连接蓝牙设备';
      });
      return;
    }

    setState(() {
      _statusMessage = '正在读取焊接参数...';
      _isLoading = true;
    });

    try {
      // 发送读取焊接参数命令
      List<int> command =
          widget.commandService.buildReadWeldingParametersCommand();
      bool sent = await widget.bleService.sendData(command);

      if (!sent) {
        setState(() {
          _statusMessage = '发送命令失败';
          _isLoading = false;
        });
        return;
      }

      // 等待响应 (实际应用中可能需要更复杂的处理)
      await Future.delayed(const Duration(milliseconds: 500));

      // 模拟收到响应 (真实情况下应该通过BLE回调获取)
      // 这里需要根据你的蓝牙服务实现方式调整
      List<int>? response = await _waitForResponse();

      if (response != null) {
        Map<String, dynamic> result =
            widget.commandService.parseWeldingParametersResponse(response);

        setState(() {
          _weldingParameters = result;
          _statusMessage =
              result['success'] ? '焊接参数读取成功' : '焊接参数解析失败: ${result['message']}';
          _isLoading = false;
        });
      } else {
        setState(() {
          _statusMessage = '未收到响应或响应超时';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '读取焊接参数错误: $e';
        _isLoading = false;
      });
    }
  }

  // 读取焊接状态
  Future<void> _readWeldingStatus() async {
    if (!widget.bleService.isConnected) {
      setState(() {
        _statusMessage = '请先连接蓝牙设备';
      });
      return;
    }

    setState(() {
      _statusMessage = '正在读取焊接状态...';
      _isLoading = true;
    });

    try {
      // 发送读取焊接状态命令
      List<int> command = widget.commandService.buildReadWeldingStatusCommand();
      bool sent = await widget.bleService.sendData(command);

      if (!sent) {
        setState(() {
          _statusMessage = '发送命令失败';
          _isLoading = false;
        });
        return;
      }

      // 等待响应
      await Future.delayed(const Duration(milliseconds: 500));

      // 获取响应
      List<int>? response = await _waitForResponse();

      if (response != null) {
        Map<String, dynamic> result =
            widget.commandService.parseWeldingStatusResponse(response);

        setState(() {
          _weldingStatus = result;
          _statusMessage =
              result['success'] ? '焊接状态读取成功' : '焊接状态解析失败: ${result['message']}';
          _isLoading = false;
        });
      } else {
        setState(() {
          _statusMessage = '未收到响应或响应超时';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '读取焊接状态错误: $e';
        _isLoading = false;
      });
    }
  }

  // 读取机器信息
  Future<void> _readMachineInfo() async {
    if (!widget.bleService.isConnected) {
      setState(() {
        _statusMessage = '请先连接蓝牙设备';
      });
      return;
    }

    setState(() {
      _statusMessage = '正在读取机器信息...';
      _isLoading = true;
    });

    try {
      // 发送读取机器编号命令
      List<int> command = widget.commandService.buildReadMachineNumberCommand();
      bool sent = await widget.bleService.sendData(command);

      if (!sent) {
        setState(() {
          _statusMessage = '发送命令失败';
          _isLoading = false;
        });
        return;
      }

      // 等待响应
      await Future.delayed(const Duration(milliseconds: 500));

      // 获取响应
      List<int>? response = await _waitForResponse();

      if (response != null) {
        Map<String, dynamic> result =
            widget.commandService.enhancedParseMachineNumberResponse(response);

        setState(() {
          _machineInfo = result;
          _statusMessage =
              result['success'] ? '机器信息读取成功' : '机器信息解析失败: ${result['message']}';
          _isLoading = false;
        });
      } else {
        setState(() {
          _statusMessage = '未收到响应或响应超时';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '读取机器信息错误: $e';
        _isLoading = false;
      });
    }
  }

  // 等待响应
  Future<List<int>?> _waitForResponse() async {
    // 这个方法需要根据实际蓝牙实现进行调整
    // 如果BLE服务允许直接查询最后一个响应，可以实现如下：

    // 创建一个Completer来等待响应或超时
    Completer<List<int>?> responseCompleter = Completer();

    // 设置超时
    Timer timeoutTimer = Timer(const Duration(seconds: 3), () {
      if (!responseCompleter.isCompleted) {
        responseCompleter.complete(null);
      }
    });

    // 这里应该监听蓝牙响应，如果你的BleService有lastResponse属性或类似功能
    // 例如：
    StreamSubscription? subscription;
    subscription = widget.bleService.receivedDataStream.listen((data) {
      // 假设能够将响应数据解析为List<int>
      if (!responseCompleter.isCompleted) {
        // 取消超时计时器
        timeoutTimer.cancel();
        responseCompleter.complete(_parseReceivedData(data));
        subscription?.cancel();
      }
    });

    // 返回Future
    return responseCompleter.future;
  }

  // 解析接收到的数据字符串为字节数组
  List<int>? _parseReceivedData(String data) {
    try {
      // 假设数据格式是十六进制字符串，例如 "01 03 0A ..."
      List<String> hexValues = data.split(' ');
      List<int> bytes = [];

      for (String hex in hexValues) {
        if (hex.trim().isNotEmpty) {
          int? value = int.tryParse(hex.trim(), radix: 16);
          if (value != null) {
            bytes.add(value);
          }
        }
      }

      return bytes.isNotEmpty ? bytes : null;
    } catch (e) {
      print('解析接收数据失败: $e');
      return null;
    }
  }

  // 读取所有数据
  Future<void> _readAllData() async {
    await _readMachineInfo();
    await _readWeldingParameters();
    await _readWeldingStatus();
  }

  // 开始自动刷新
  void _startAutoRefresh() {
    _cancelAutoRefresh(); // 确保取消之前的定时器

    setState(() {
      _isAutoRefresh = true;
    });

    // 初始加载
    _readAllData();

    // 设置定时器，每3秒刷新一次
    _refreshTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (_isAutoRefresh) {
        _readAllData();
      } else {
        _cancelAutoRefresh();
      }
    });
  }

  // 取消自动刷新
  void _cancelAutoRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;

    setState(() {
      _isAutoRefresh = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('焊接数据监控'),
        actions: [
          // 自动刷新开关
          IconButton(
            icon: Icon(_isAutoRefresh ? Icons.sync_disabled : Icons.sync),
            tooltip: _isAutoRefresh ? '停止自动刷新' : '开始自动刷新',
            onPressed: _isLoading
                ? null
                : () {
                    if (_isAutoRefresh) {
                      _cancelAutoRefresh();
                    } else {
                      _startAutoRefresh();
                    }
                  },
          ),
          // 刷新按钮
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: '刷新所有数据',
            onPressed: _isLoading ? null : _readAllData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _readAllData,
              child: SingleChildScrollView(
                controller: _scrollController,
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 状态消息
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  widget.bleService.isConnected
                                      ? Icons.bluetooth_connected
                                      : Icons.bluetooth_disabled,
                                  color: widget.bleService.isConnected
                                      ? Colors.blue
                                      : Colors.red,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  widget.bleService.isConnected
                                      ? '蓝牙已连接'
                                      : '蓝牙未连接',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: widget.bleService.isConnected
                                        ? Colors.blue
                                        : Colors.red,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(_statusMessage),
                            if (_isAutoRefresh) ...[
                              const SizedBox(height: 8),
                              const Row(
                                children: [
                                  Icon(Icons.sync,
                                      size: 16, color: Colors.green),
                                  SizedBox(width: 4),
                                  Text(
                                    '自动刷新已开启',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.green,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // 机器信息
                    _buildSection(
                      title: '机器信息',
                      icon: Icons.info_outline,
                      color: Colors.blue,
                      content: _machineInfo == null
                          ? _buildEmptyCard('暂无机器信息，点击按钮获取')
                          : _buildMachineInfoCard(),
                      actionButton: ElevatedButton.icon(
                        icon: const Icon(Icons.refresh, size: 18),
                        label: const Text('读取机器信息'),
                        onPressed: _isLoading ? null : _readMachineInfo,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // 焊接参数
                    _buildSection(
                      title: '焊接参数',
                      icon: Icons.settings,
                      color: Colors.orange,
                      content: _weldingParameters == null
                          ? _buildEmptyCard('暂无焊接参数，点击按钮获取')
                          : _buildParametersCard(),
                      actionButton: ElevatedButton.icon(
                        icon: const Icon(Icons.refresh, size: 18),
                        label: const Text('读取焊接参数'),
                        onPressed: _isLoading ? null : _readWeldingParameters,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // 焊接状态
                    _buildSection(
                      title: '焊接状态',
                      icon: Icons.analytics_outlined,
                      color: Colors.green,
                      content: _weldingStatus == null
                          ? _buildEmptyCard('暂无焊接状态，点击按钮获取')
                          : _buildStatusCard(),
                      actionButton: ElevatedButton.icon(
                        icon: const Icon(Icons.refresh, size: 18),
                        label: const Text('读取焊接状态'),
                        onPressed: _isLoading ? null : _readWeldingStatus,
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  // 构建区块
  Widget _buildSection({
    required String title,
    required IconData icon,
    required Color color,
    required Widget content,
    required Widget actionButton,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(icon, color: color),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            actionButton,
          ],
        ),
        const SizedBox(height: 8),
        content,
      ],
    );
  }

  // 构建空卡片
  Widget _buildEmptyCard(String message) {
    return Card(
      elevation: 1,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        child: Text(
          message,
          textAlign: TextAlign.center,
          style: TextStyle(color: Colors.grey[600]),
        ),
      ),
    );
  }

  // 构建机器信息卡片
  Widget _buildMachineInfoCard() {
    if (_machineInfo == null || _machineInfo!['success'] != true) {
      return _buildEmptyCard('机器信息读取失败或数据无效');
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoItem(
              '机器编号',
              _machineInfo!['machineNumber'] ?? '未知',
              Icons.numbers,
            ),
            const Divider(),
            _buildInfoItem(
              '编号（十六进制）',
              _machineInfo!['machineNumberHex'] ?? '未知',
              Icons.code,
            ),
            const Divider(),
            _buildInfoItem(
              '原始字节数',
              (_machineInfo!['rawBytes'] as List<dynamic>?)
                      ?.length
                      .toString() ??
                  '0',
              Icons.memory,
            ),
          ],
        ),
      ),
    );
  }

  // 构建参数卡片
  Widget _buildParametersCard() {
    if (_weldingParameters == null || _weldingParameters!['success'] != true) {
      return _buildEmptyCard('焊接参数读取失败或数据无效');
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoItem(
              '电流设定值',
              '${_weldingParameters!['currentSetpoint']} A',
              Icons.electrical_services,
            ),
            const Divider(),
            _buildInfoItem(
              '电压设定值',
              '${_weldingParameters!['voltageSetpoint']} V',
              Icons.bolt,
            ),
            const Divider(),
            _buildInfoItem(
              '送丝速度',
              '${_weldingParameters!['wireSpeed']} m/min',
              Icons.speed,
            ),
            const Divider(),
            _buildInfoItem(
              '弧长调节',
              '${_weldingParameters!['arcLength']}',
              Icons.adjust,
            ),
            const Divider(),
            _buildInfoItem(
              '电感设定',
              '${_weldingParameters!['inductance']}',
              Icons.settings_input_component,
            ),
            const Divider(),
            _buildInfoItem(
              '气体预流时间',
              '${_weldingParameters!['preflowTime']} 秒',
              Icons.timer,
            ),
            const Divider(),
            _buildInfoItem(
              '气体后流时间',
              '${_weldingParameters!['postflowTime']} 秒',
              Icons.timer_off,
            ),
            const Divider(),
            _buildInfoItem(
              '缓慢送丝速度',
              '${_weldingParameters!['slowFeedSpeed']} m/min',
              Icons.slow_motion_video,
            ),
            const Divider(),
            _buildInfoItem(
              '回烧时间',
              '${_weldingParameters!['burnbackTime']} 秒',
              Icons.local_fire_department,
            ),
            const Divider(),
            _buildInfoItem(
              '点焊时间',
              '${_weldingParameters!['spotWeldTime']} 秒',
              Icons.flash_on,
            ),
          ],
        ),
      ),
    );
  }

  // 构建状态卡片
  Widget _buildStatusCard() {
    if (_weldingStatus == null || _weldingStatus!['success'] != true) {
      return _buildEmptyCard('焊接状态读取失败或数据无效');
    }

    // 获取状态信息
    bool isIdle = _weldingStatus!['isIdle'] ?? true;
    bool isError = _weldingStatus!['isError'] ?? false;
    String statusDescription = _weldingStatus!['statusDescription'] ?? '未知状态';
    String modeDescription = _weldingStatus!['modeDescription'] ?? '未知模式';

    // 确定状态颜色
    Color statusColor = Colors.grey;
    if (isError) {
      statusColor = Colors.red;
    } else if (!isIdle) {
      statusColor = Colors.green;
    } else {
      statusColor = Colors.blue;
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 状态指示器
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: statusColor, width: 1),
              ),
              child: Row(
                children: [
                  Icon(
                    isError
                        ? Icons.error
                        : (isIdle ? Icons.pause : Icons.play_arrow),
                    color: statusColor,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    statusDescription,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: statusColor,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoItem(
              '实际电流值',
              '${_weldingStatus!['currentValue']} A',
              Icons.electrical_services,
            ),
            const Divider(),
            _buildInfoItem(
              '实际电压值',
              '${_weldingStatus!['voltageValue']} V',
              Icons.bolt,
            ),
            const Divider(),
            _buildInfoItem(
              '焊接模式',
              modeDescription,
              Icons.category,
            ),
            const Divider(),
            _buildInfoItem(
              '错误代码',
              '${_weldingStatus!['errorCode']}',
              Icons.error_outline,
              valueColor: isError ? Colors.red : null,
            ),
          ],
        ),
      ),
    );
  }

  // 构建信息项
  Widget _buildInfoItem(String label, String value, IconData icon,
      {Color? valueColor}) {
    return Row(
      children: [
        Icon(icon, size: 18, color: Colors.grey),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: valueColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
