import 'package:flutter/material.dart';
import 'dart:convert';
import '../services/offline_mode_service.dart';
import '../services/bluetooth_service.dart';
import '../services/welding_joint_number_service.dart';
import '../services/offline_storage_service.dart';
import '../services/user_service.dart';
import '../services/project_service.dart';
import '../services/location_service.dart';
import '../models/offline_data_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

class WeldingScreen extends StatefulWidget {
  @override
  _WeldingScreenState createState() => _WeldingScreenState();
}

class _WeldingScreenState extends State<WeldingScreen> {
  bool _isLoading = false;
  int _currentStep = 0;
  String _statusMessage = '准备开始焊接...';
  bool _weldingCompleted = false;
  bool _weldingInProgress = false;

  // 离线模式相关
  final OfflineModeService _offlineModeService = OfflineModeService();
  final BleService _bleService = BleService();
  final WeldingJointNumberService _jointNumberService =
      WeldingJointNumberService();
  final OfflineStorageService _storageService = OfflineStorageService();

  // 其他服务
  final UserService _userService = UserService();
  final ProjectService _projectService = ProjectService();
  final LocationService _locationService = LocationService();

  // 焊接数据相关
  Map<String, dynamic> _weldingParams = {};
  String _weldingDataHex = '';
  List<String> _weldingImages = [];

  // 完整信息数据
  Map<String, dynamic> _userInfo = {};
  Map<String, dynamic> _projectInfo = {};
  Map<String, dynamic> _deviceInfo = {};
  Map<String, dynamic> _weldingMachineData = {};
  Map<String, dynamic> _locationInfo = {};

  // 焊接配置信息（从配置页面收集）
  Map<String, dynamic> _weldingConfigInfo = {};
  Map<String, dynamic> _deviceConfigInfo = {};
  Map<String, dynamic> _gpsConfigInfo = {};
  Map<String, dynamic> _offlineModeInfo = {};

  // 历史焊接数据
  List<OfflineWeldingData> _historicalWeldingData = [];
  bool _isLoadingHistory = false;

  @override
  void initState() {
    super.initState();
    // 初始化并自动开始焊接
    _initializeAndStartWelding();
  }

  Future<void> _initializeAndStartWelding() async {
    await _initializeOfflineMode();
    await _startWelding();
  }

  Future<void> _initializeOfflineMode() async {
    try {
      await _offlineModeService.initialize();
    } catch (e) {
      print('初始化离线模式服务失败: $e');
    }
  }

  Future<void> _startWelding() async {
    setState(() {
      _isLoading = true;
      _weldingInProgress = false;
      _statusMessage = '正在准备焊接设备...';
    });

    // 模拟准备阶段
    await Future.delayed(Duration(seconds: 1));

    setState(() {
      _currentStep = 1;
      _statusMessage = '焊接参数配置中...';
    });

    // 模拟参数配置 - 收集焊接参数
    await _collectWeldingParameters();
    await Future.delayed(Duration(seconds: 1));

    setState(() {
      _currentStep = 2;
      _isLoading = false;
      _weldingInProgress = true;
      _statusMessage = '焊接进行中... 请手动完成焊接后点击"完成焊接"按钮';
    });
  }

  Future<void> _completeWelding() async {
    setState(() {
      _isLoading = true;
      _weldingInProgress = false;
      _statusMessage = '正在收集焊接数据...';
    });

    // 焊接完成后收集160字节数据
    await _collectWeldingData();

    setState(() {
      _currentStep = 3;
      _statusMessage = '焊接已完成，正在保存数据...';
    });

    // 保存焊接数据
    await _saveWeldingData();

    // 🎯 模拟焊接结果并处理焊接状态
    await _handleWeldingResult();

    setState(() {
      _isLoading = false;
      _weldingCompleted = true;
      _statusMessage = '焊接数据已保存完成';
    });

    // 焊接完成后收集所有信息
    await _collectAllInformation();

    // 焊接完成后加载历史数据
    await _loadHistoricalData();
  }

  // 收集所有相关信息
  Future<void> _collectAllInformation() async {
    try {
      setState(() {
        _statusMessage = '正在收集完整信息...';
      });

      // 并行收集所有信息
      await Future.wait([
        _collectUserInfo(),
        _collectProjectInfo(),
        _collectDeviceInfo(),
        _collectWeldingMachineData(),
        _collectLocationInfo(),
        _collectWeldingConfigInfo(),
        _collectDeviceConfigInfo(),
        _collectGpsConfigInfo(),
        _collectOfflineModeInfo(),
        _collectConfigWeldingMachineData(),
        _collectConfigProjectInfo(),
      ]);

      print('所有信息收集完成');
    } catch (e) {
      print('收集信息失败: $e');
    }
  }

  // 收集用户信息
  Future<void> _collectUserInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _userInfo = {
        'userId': await _userService.getUserId() ?? '未知ID',
        'userName': await _userService.getUserName() ?? '未知用户',
        'loginTime': prefs.getString('loginTime') ?? '未知时间',
        'userRole': prefs.getString('userRole') ?? '操作员',
        'department': prefs.getString('department') ?? '未设置',
        'employeeId': prefs.getString('employeeId') ?? '未设置',
      };
      print('用户信息已收集: $_userInfo');
    } catch (e) {
      print('收集用户信息失败: $e');
      _userInfo = {'error': '获取用户信息失败: $e'};
    }
  }

  // 收集项目信息
  Future<void> _collectProjectInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final projectId = prefs.getString('selectedProjectId');

      if (projectId != null && projectId.isNotEmpty) {
        // 尝试获取项目详细信息
        final project = await _projectService.getProjectDetails(projectId);
        if (project != null) {
          _projectInfo = {
            'projectId': project.id,
            'projectCode': project.code,
            'projectName': project.name,
            'projectAddress': project.address,
            'selectedTime': prefs.getString('selectedProjectTime') ?? '未知时间',
            'status': '进行中',
          };
        } else {
          _projectInfo = {
            'projectId': projectId,
            'error': '项目详情获取失败',
            'selectedTime': prefs.getString('selectedProjectTime') ?? '未知时间',
          };
        }
      } else {
        _projectInfo = {
          'error': '未选择项目',
          'message': '请先在项目标签页选择项目',
        };
      }
      print('项目信息已收集: $_projectInfo');
    } catch (e) {
      print('收集项目信息失败: $e');
      _projectInfo = {'error': '获取项目信息失败: $e'};
    }
  }

  // 收集设备信息
  Future<void> _collectDeviceInfo() async {
    try {
      if (_bleService.isConnected && _bleService.connectedDevice != null) {
        final device = _bleService.connectedDevice!;
        _deviceInfo = {
          'deviceId': device.id.toString(),
          'deviceName': device.name ?? '未知设备',
          'connectionTime': DateTime.now().toIso8601String(),
          'isConnected': true,
          'services': device.services.length.toString(),
        };
      } else {
        _deviceInfo = {
          'error': '设备未连接',
          'message': '蓝牙设备未连接或连接已断开',
          'isConnected': false,
        };
      }
      print('设备信息已收集: $_deviceInfo');
    } catch (e) {
      print('收集设备信息失败: $e');
      _deviceInfo = {'error': '获取设备信息失败: $e'};
    }
  }

  // 收集焊机数据
  Future<void> _collectWeldingMachineData() async {
    try {
      if (_bleService.isConnected) {
        // 尝试获取焊机状态数据
        bool queryResult = await _bleService.sendQueryWeldingDataCommand();

        _weldingMachineData = {
          'hexData': _weldingDataHex,
          'dataLength': '${_weldingDataHex.length ~/ 2} 字节',
          'querySuccess': queryResult,
          'collectionTime': DateTime.now().toIso8601String(),
          'weldingStatus': _getWeldingStatusFromData(),
          'parameters': _weldingParams,
        };

        // 如果有实际的160字节数据，解析一些关键参数
        if (_weldingDataHex.length >= 320) {
          // 160字节 = 320个字符
          _weldingMachineData['parsedData'] =
              _parseWeldingDataForDisplay(_weldingDataHex);
        }
      } else {
        _weldingMachineData = {
          'error': '焊机未连接',
          'message': '无法获取焊机数据，设备未连接',
          'hexData': _weldingDataHex,
          'parameters': _weldingParams,
        };
      }
      print('焊机数据已收集: $_weldingMachineData');
    } catch (e) {
      print('收集焊机数据失败: $e');
      _weldingMachineData = {'error': '获取焊机数据失败: $e'};
    }
  }

  // 收集位置信息
  Future<void> _collectLocationInfo() async {
    try {
      final location = await _locationService.getLocationWithFallback();
      if (location != null) {
        _locationInfo = {
          'longitude': location.longitude,
          'latitude': location.latitude,
          'altitude': location.altitude,
          'accuracy': location.accuracy,
          'timestamp': location.timestamp.toIso8601String(),
          'formatted': _locationService.formatLocationData(location),
        };
      } else {
        _locationInfo = {
          'error': '位置获取失败',
          'message': '无法获取当前位置信息',
        };
      }
      print('位置信息已收集: $_locationInfo');
    } catch (e) {
      print('收集位置信息失败: $e');
      _locationInfo = {'error': '获取位置信息失败: $e'};
    }
  }

  // 收集焊接配置信息 - 从配置页面获取实际数据
  Future<void> _collectWeldingConfigInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 从配置页面获取设备信息
      Map<String, String> configDeviceInfo = {
        'connectionStatus': prefs.getString('config_connectionStatus') ?? '未知',
        'machineNumber': prefs.getString('config_machineNumber') ?? '未获取',
        'weldingStandard': prefs.getString('config_weldingStandard') ?? '未设置',
        'machineType': prefs.getString('config_machineType') ?? '未知',
        'cylinderArea': prefs.getString('config_cylinderArea') ?? '未设置',
      };

      _weldingConfigInfo = {
        'uploadStatus': '等待上传',
        'dataType': '焊接配置信息',
        'configPageVisited': prefs.getBool('configPageVisited') ?? false,
        'lastConfigTime': prefs.getString('lastConfigTime') ?? '未配置',
        'connectionStatus': configDeviceInfo['connectionStatus']!,
        'machineNumber': configDeviceInfo['machineNumber']!,
        'weldingStandard': configDeviceInfo['weldingStandard']!,
        'machineType': configDeviceInfo['machineType']!,
        'cylinderArea': configDeviceInfo['cylinderArea']!,
        'currentConnectionStatus': _bleService.isConnected ? '已连接' : '未连接',
        'dataCollectionTime': DateTime.now().toIso8601String(),
        'pendingUpload': true,
      };

      print('焊接配置信息已收集: $_weldingConfigInfo');
    } catch (e) {
      print('收集焊接配置信息失败: $e');
      _weldingConfigInfo = {
        'error': '获取焊接配置信息失败: $e',
        'uploadStatus': '收集失败',
        'pendingUpload': false,
      };
    }
  }

  // 收集设备配置信息 - 从配置页面获取实际设备信息
  Future<void> _collectDeviceConfigInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 从配置页面获取实际设备信息
      Map<String, dynamic> configDeviceData = {
        'connectionStatus': prefs.getString('config_connectionStatus') ?? '未知',
        'machineNumber': prefs.getString('config_machineNumber') ?? '未获取',
        'weldingStandard': prefs.getString('config_weldingStandard') ?? '未设置',
        'machineType': prefs.getString('config_machineType') ?? '未知',
        'cylinderArea': prefs.getString('config_cylinderArea') ?? '未设置',
        'lastDeviceInfoTime': prefs.getString('lastDeviceInfoTime') ?? '未读取',
        'deviceInfoLoaded': prefs.getBool('deviceInfoLoaded') ?? false,
      };

      _deviceConfigInfo = {
        'uploadStatus': '等待上传',
        'dataType': '设备配置信息',
        'connectionStatus': configDeviceData['connectionStatus'],
        'machineNumber': configDeviceData['machineNumber'],
        'weldingStandard': configDeviceData['weldingStandard'],
        'machineType': configDeviceData['machineType'],
        'cylinderArea': configDeviceData['cylinderArea'],
        'deviceInfoLoaded': configDeviceData['deviceInfoLoaded'],
        'lastDeviceInfoTime': configDeviceData['lastDeviceInfoTime'],
        'bluetoothEnabled': _bleService.isConnected,
        'scanTime': prefs.getString('lastScanTime') ?? '未扫描',
        'connectionAttempts': prefs.getInt('connectionAttempts') ?? 0,
        'lastConnectionTime': prefs.getString('lastConnectionTime') ?? '未连接',
        'dataCollectionTime': DateTime.now().toIso8601String(),
        'pendingUpload': true,
      };

      print('设备配置信息已收集: $_deviceConfigInfo');
    } catch (e) {
      print('收集设备配置信息失败: $e');
      _deviceConfigInfo = {
        'error': '获取设备配置信息失败: $e',
        'uploadStatus': '收集失败',
        'pendingUpload': false,
      };
    }
  }

  // 收集GPS配置信息 - 从配置页面获取实际GPS数据
  Future<void> _collectGpsConfigInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 从配置页面获取实际GPS数据
      String configLocationData = prefs.getString('config_locationData') ?? '';
      Map<String, dynamic> configGpsData = {
        'gpsTestPerformed': prefs.getBool('gpsTestPerformed') ?? false,
        'lastGpsTestTime': prefs.getString('lastGpsTestTime') ?? '未测试',
        'gpsPermissionGranted': prefs.getBool('gpsPermissionGranted') ?? false,
        'lastKnownLatitude':
            prefs.getDouble('lastKnownLatitude')?.toString() ?? '未知',
        'lastKnownLongitude':
            prefs.getDouble('lastKnownLongitude')?.toString() ?? '未知',
        'gpsAccuracy': prefs.getDouble('gpsAccuracy')?.toString() ?? '未知',
        'locationData': configLocationData,
      };

      _gpsConfigInfo = {
        'uploadStatus': '等待上传',
        'dataType': 'GPS配置信息',
        'gpsTestPerformed': configGpsData['gpsTestPerformed'],
        'lastGpsTestTime': configGpsData['lastGpsTestTime'],
        'gpsPermissionGranted': configGpsData['gpsPermissionGranted'],
        'lastKnownLatitude': configGpsData['lastKnownLatitude'],
        'lastKnownLongitude': configGpsData['lastKnownLongitude'],
        'gpsAccuracy': configGpsData['gpsAccuracy'],
        'locationData': configGpsData['locationData'],
        'gpsConfigStatus': 'GPS配置信息已收集',
        'dataCollectionTime': DateTime.now().toIso8601String(),
        'pendingUpload': true,
      };

      print('GPS配置信息已收集: $_gpsConfigInfo');
    } catch (e) {
      print('收集GPS配置信息失败: $e');
      _gpsConfigInfo = {
        'error': '获取GPS配置信息失败: $e',
        'uploadStatus': '收集失败',
        'pendingUpload': false,
      };
    }
  }

  // 收集离线模式信息
  Future<void> _collectOfflineModeInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      _offlineModeInfo = {
        'isOfflineMode': prefs.getBool('isOfflineMode') ?? false,
        'pendingUploads': prefs.getInt('pendingUploads') ?? 0,
        'lastSyncTime': prefs.getString('lastSyncTime') ?? '未同步',
        'offlineDataCount': prefs.getInt('offlineDataCount') ?? 0,
        'networkStatus':
            prefs.getBool('networkAvailable') ?? false ? '网络可用' : '网络不可用',
        'offlineModeStatus': '离线模式信息已收集',
        'dataCollectionTime': DateTime.now().toIso8601String(),
      };

      print('离线模式信息已收集: $_offlineModeInfo');
    } catch (e) {
      print('收集离线模式信息失败: $e');
      _offlineModeInfo = {'error': '获取离线模式信息失败: $e'};
    }
  }

  // 收集配置页面的焊机数据 - 从配置页面获取160字节焊机数据
  Future<void> _collectConfigWeldingMachineData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 从配置页面获取160字节焊机数据
      String configWeldingData = prefs.getString('config_weldingData') ?? '';
      String configParsedData =
          prefs.getString('config_parsedWeldingData') ?? '';

      Map<String, dynamic> configWeldingInfo = {
        'weldingDataLoaded': prefs.getBool('weldingDataLoaded') ?? false,
        'lastWeldingDataTime': prefs.getString('lastWeldingDataTime') ?? '未获取',
        'weldingDataLength': configWeldingData.length,
        'rawWeldingData': configWeldingData,
        'parsedWeldingData': configParsedData,
        'dataParseSuccess': configParsedData.isNotEmpty,
      };

      // 更新焊机数据信息，标记为等待上传
      _weldingMachineData = {
        'uploadStatus': '等待上传',
        'dataType': '焊机数据 (160字节)',
        'dataLength': configWeldingInfo['weldingDataLength'],
        'querySuccess': configWeldingInfo['weldingDataLoaded'],
        'collectionTime': configWeldingInfo['lastWeldingDataTime'],
        'weldingStatus':
            configWeldingInfo['dataParseSuccess'] ? '数据解析成功' : '数据解析失败',
        'hexData': configWeldingInfo['rawWeldingData'],
        'parsedData': configWeldingInfo['parsedWeldingData'],
        'dataCollectionTime': DateTime.now().toIso8601String(),
        'pendingUpload': true,
      };

      print('配置页面焊机数据已收集: ${configWeldingInfo['weldingDataLength']} 字符');
    } catch (e) {
      print('收集配置页面焊机数据失败: $e');
      _weldingMachineData = {
        'error': '获取配置页面焊机数据失败: $e',
        'uploadStatus': '收集失败',
        'pendingUpload': false,
      };
    }
  }

  // 收集配置页面的项目信息
  Future<void> _collectConfigProjectInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 从配置页面获取项目信息
      String configProjectId = prefs.getString('config_projectId') ?? '';
      String configProjectName = prefs.getString('config_projectName') ?? '';
      String configProjectCode = prefs.getString('config_projectCode') ?? '';
      String configProjectAddress =
          prefs.getString('config_projectAddress') ?? '';
      String configConstructionUnit =
          prefs.getString('config_constructionUnit') ?? '';

      Map<String, dynamic> configProjectData = {
        'projectId': configProjectId,
        'projectName': configProjectName,
        'projectCode': configProjectCode,
        'projectAddress': configProjectAddress,
        'constructionUnit': configConstructionUnit,
        'projectLoaded': configProjectId.isNotEmpty,
        'lastProjectTime': prefs.getString('lastProjectTime') ?? '未加载',
      };

      // 更新项目信息，标记为等待上传
      _projectInfo = {
        'uploadStatus': '等待上传',
        'dataType': '项目信息',
        'projectId': configProjectData['projectId'],
        'projectCode': configProjectData['projectCode'],
        'projectName': configProjectData['projectName'],
        'projectAddress': configProjectData['projectAddress'],
        'constructionUnit': configProjectData['constructionUnit'],
        'selectedTime': configProjectData['lastProjectTime'],
        'status': configProjectData['projectLoaded'] ? '项目已加载' : '未加载项目',
        'dataCollectionTime': DateTime.now().toIso8601String(),
        'pendingUpload': true,
      };

      print('配置页面项目信息已收集: ${configProjectData['projectName']}');
    } catch (e) {
      print('收集配置页面项目信息失败: $e');
      _projectInfo = {
        'error': '获取配置页面项目信息失败: $e',
        'uploadStatus': '收集失败',
        'pendingUpload': false,
      };
    }
  }

  // 从焊机数据中获取焊接状态
  String _getWeldingStatusFromData() {
    if (_weldingDataHex.isEmpty) return '无数据';
    if (_weldingDataHex.length < 10) return '数据不完整';

    try {
      // 根据实际的160字节数据格式解析状态
      // TODO: 这里需要根据实际的焊机数据格式进行解析

      List<int> bytes = [];
      for (int i = 0; i < _weldingDataHex.length - 1; i += 2) {
        bytes.add(int.parse(_weldingDataHex.substring(i, i + 2), radix: 16));
      }

      if (bytes.length >= 10) {
        // 根据实际数据位置解析状态
        // 这里需要根据焊机协议文档来确定状态字节的位置
        return '数据接收完成 (${bytes.length}字节)';
      } else {
        return '数据不完整';
      }
    } catch (e) {
      return '数据解析错误: $e';
    }
  }

  // 解析焊机数据 - 根据实际的160字节数据格式
  Map<String, dynamic> _parseWeldingData(String hexData) {
    try {
      List<int> bytes = [];
      for (int i = 0; i < hexData.length - 1; i += 2) {
        bytes.add(int.parse(hexData.substring(i, i + 2), radix: 16));
      }

      if (bytes.length < 160) {
        return {
          'error': '数据长度不足160字节，当前${bytes.length}字节',
          'rawDataPreview': bytes
              .take(10)
              .map((b) => b.toRadixString(16).padLeft(2, '0'))
              .join(' '),
        };
      }

      // 根据表格解析各个参数
      Map<String, dynamic> parsed = {
        'totalBytes': bytes.length,
        'rawDataPreview': bytes
            .take(20)
            .map((b) => b.toRadixString(16).padLeft(2, '0'))
            .join(' '),
      };

      // 解析基础参数 (VW2530-VW2540)
      // 160字节 = 80字，VW2530对应字索引0
      parsed['管材直径'] = _getWordValueByVW(bytes, 2530); // VW2530 = 字索引0
      parsed['管材SDR'] = _getWordValueByVW(bytes, 2532); // VW2532 = 字索引1
      parsed['管材厚度'] = _getWordValueByVW(bytes, 2534); // VW2534 = 字索引2
      parsed['环境温度'] = _getWordValueByVW(bytes, 2536); // VW2536 = 字索引3
      parsed['热板温度'] = _getWordValueByVW(bytes, 2538); // VW2538 = 字索引4
      parsed['拖动压力'] = _getWordValueByVW(bytes, 2540); // VW2540 = 字索引5

      // 解析工艺参数
      parsed['卷边设定压力'] = _getWordValueByVW(bytes, 2544); // VW2544 = 字索引7
      parsed['卷边设定时间'] = _getWordValueByVW(bytes, 2546); // VW2546 = 字索引8
      parsed['卷边实际时间'] = _getWordValueByVW(bytes, 2548); // VW2548 = 字索引9
      parsed['吸热设定压力'] = _getWordValueByVW(bytes, 2550); // VW2550 = 字索引10
      parsed['吸热设定时间'] = _getWordValueByVW(bytes, 2552); // VW2552 = 字索引11
      parsed['吸热实际压力'] = _getWordValueByVW(bytes, 2554); // VW2554 = 字索引12
      parsed['吸热实际时间'] = _getWordValueByVW(bytes, 2556); // VW2556 = 字索引13
      parsed['转换时间'] = _getWordValueByVW(bytes, 2558); // VW2558 = 字索引14
      parsed['增压时间'] = _getWordValueByVW(bytes, 2560); // VW2560 = 字索引15
      parsed['冷却设定压力'] = _getWordValueByVW(bytes, 2562); // VW2562 = 字索引16
      parsed['冷却设定时间'] = _getWordValueByVW(bytes, 2564); // VW2564 = 字索引17
      parsed['冷却实际压力'] = _getWordValueByVW(bytes, 2566); // VW2566 = 字索引18
      parsed['冷却时间时间'] = _getWordValueByVW(bytes, 2568); // VW2568 = 字索引19

      // 解析焊接状态
      int statusValue = _getWordValueByVW(bytes, 2570); // VW2570 = 字索引20
      parsed['焊接状态'] = _getWeldingStatusTextForDisplay(statusValue);

      // 解析日期时间信息 - 多字参数
      parsed['熔接开始日期'] =
          _parseDateByVW(bytes, 2580, 2590); // VW2580-VW2590 (5字)
      parsed['熔接开始时间'] =
          _parseTimeByVW(bytes, 2600, 2608); // VW2600-VW2608 (4字)
      parsed['熔接结束日期'] =
          _parseDateByVW(bytes, 2620, 2630); // VW2620-VW2630 (5字)
      parsed['熔接结束时间'] =
          _parseTimeByVW(bytes, 2640, 2648); // VW2640-VW2648 (4字)

      return parsed;
    } catch (e) {
      return {'error': '数据解析失败: $e'};
    }
  }

  // 从字节数组中获取字值（16位）
  int _getWordValue(List<int> bytes, int index) {
    if (index + 1 < bytes.length) {
      // 高字节在前，低字节在后（大端序）
      return (bytes[index] << 8) | bytes[index + 1];
    }
    return 0;
  }

  // 根据VW地址获取字值
  int _getWordValueByVW(List<int> bytes, int vwAddress) {
    // VW2530是起始地址，对应字索引0
    // VW地址到字索引的转换：(vwAddress - 2530) / 2
    int wordIndex = (vwAddress - 2530) ~/ 2;
    int byteIndex = wordIndex * 2;

    if (byteIndex + 1 < bytes.length) {
      // 高字节在前，低字节在后（大端序）
      return (bytes[byteIndex] << 8) | bytes[byteIndex + 1];
    }
    return 0;
  }

  // 解析日期
  String _parseDate(
      List<int> bytes, int yearIndex, int monthIndex, int dayIndex) {
    try {
      int year = _getWordValue(bytes, yearIndex);
      int month = _getWordValue(bytes, monthIndex);
      int day = _getWordValue(bytes, dayIndex);

      // 如果年份看起来是两位数，加上2000
      if (year < 100) {
        year += 2000;
      }

      return '${year}-${month.toString().padLeft(2, '0')}-${day.toString().padLeft(2, '0')}';
    } catch (e) {
      return '日期解析错误';
    }
  }

  // 解析时间
  String _parseTime(List<int> bytes, int hourMinIndex, int secIndex) {
    try {
      int hourMin = _getWordValue(bytes, hourMinIndex);
      int sec = _getWordValue(bytes, secIndex);

      int hour = hourMin >> 8; // 高8位为小时
      int minute = hourMin & 0xFF; // 低8位为分钟
      int second = sec >> 8; // 高8位为秒

      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}:${second.toString().padLeft(2, '0')}';
    } catch (e) {
      return '时间解析错误';
    }
  }

  // 根据VW地址解析日期（多字参数）
  String _parseDateByVW(List<int> bytes, int startVW, int endVW) {
    try {
      // 5字参数：年、月、日可能分布在多个字中
      // 根据实际格式调整，这里假设：
      // VW2580: 年份, VW2582: 月份, VW2584: 日期
      // VW2586: 扩展信息, VW2588: 保留

      if (endVW - startVW >= 4) {
        // 至少5个字
        int year = _getWordValueByVW(bytes, startVW); // VW2580
        int month = _getWordValueByVW(bytes, startVW + 2); // VW2582
        int day = _getWordValueByVW(bytes, startVW + 4); // VW2584

        // 如果年份看起来是两位数，加上2000
        if (year < 100) {
          year += 2000;
        }

        return '${year}-${month.toString().padLeft(2, '0')}-${day.toString().padLeft(2, '0')}';
      } else {
        return '日期格式错误';
      }
    } catch (e) {
      return '日期解析错误: $e';
    }
  }

  // 根据VW地址解析时间（多字参数）
  String _parseTimeByVW(List<int> bytes, int startVW, int endVW) {
    try {
      // 4字参数：时、分、秒可能分布在多个字中
      // 根据实际格式调整，这里假设：
      // VW2600: 小时, VW2602: 分钟, VW2604: 秒, VW2606: 毫秒

      if (endVW - startVW >= 2) {
        // 至少4个字
        int hour = _getWordValueByVW(bytes, startVW); // VW2600
        int minute = _getWordValueByVW(bytes, startVW + 2); // VW2602
        int second = _getWordValueByVW(bytes, startVW + 4); // VW2604

        return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}:${second.toString().padLeft(2, '0')}';
      } else {
        return '时间格式错误';
      }
    } catch (e) {
      return '时间解析错误: $e';
    }
  }

  // 获取焊接状态文本
  String _getWeldingStatusText(int statusValue) {
    switch (statusValue) {
      case 0:
        return '待机';
      case 1:
        return '准备中';
      case 2:
        return '卷边阶段';
      case 3:
        return '吸热阶段';
      case 4:
        return '转换阶段';
      case 5:
        return '增压阶段';
      case 6:
        return '冷却阶段';
      case 7:
        return '焊接完成';
      case 8:
        return '焊接失败';
      case 9:
        return '设备故障';
      default:
        return '未知状态($statusValue)';
    }
  }

  // 收集焊接参数
  Future<void> _collectWeldingParameters() async {
    try {
      // 从蓝牙设备获取当前焊接参数
      if (_bleService.isConnected) {
        // 尝试从蓝牙设备读取实际焊接参数
        bool success = await _bleService.sendQueryWeldingDataCommand();

        if (success) {
          // 等待参数响应并解析
          await Future.delayed(Duration(seconds: 1));
          // 这里应该从蓝牙服务获取实际参数，现在先设置基本信息
          _weldingParams = {
            'timestamp': DateTime.now().toIso8601String(),
            'deviceId': _bleService.connectedDevice?.id.toString() ?? 'unknown',
            'deviceName': _bleService.connectedDevice?.name ?? 'unknown',
            'querySuccess': true,
            'note': '参数需要从实际蓝牙响应解析',
          };
        } else {
          _weldingParams = {
            'timestamp': DateTime.now().toIso8601String(),
            'deviceId': _bleService.connectedDevice?.id.toString() ?? 'unknown',
            'deviceName': _bleService.connectedDevice?.name ?? 'unknown',
            'querySuccess': false,
            'error': '参数查询失败',
          };
        }
      } else {
        _weldingParams = {
          'timestamp': DateTime.now().toIso8601String(),
          'deviceId': 'no_device',
          'error': '设备未连接',
          'note': '无法获取焊接参数',
        };
      }
      print('焊接参数已收集: $_weldingParams');
    } catch (e) {
      print('收集焊接参数失败: $e');
      _weldingParams = {
        'timestamp': DateTime.now().toIso8601String(),
        'error': '收集参数异常: $e',
      };
    }
  }

  // 收集160字节焊机数据
  Future<void> _collectWeldingData() async {
    try {
      if (_bleService.isConnected) {
        print('🔍 开始查询焊机数据...');

        // 尝试获取实际的160字节数据
        bool result = await _bleService.sendQueryWeldingDataCommand();
        if (result) {
          print('✅ 焊机数据查询命令发送成功，等待响应...');
          // 等待数据响应并检查是否收到数据
          await Future.delayed(Duration(seconds: 3));

          // 从蓝牙服务的接收数据流中获取最新的焊机数据
          // 这里需要实际从receivedDataStream或存储的数据中获取
          final receivedData = await _getLatestWeldingDataFromBluetooth();

          if (receivedData != null && receivedData.isNotEmpty) {
            _weldingDataHex = receivedData;
            print('📊 成功获取到焊机数据: ${_weldingDataHex.length ~/ 2} 字节');
          } else {
            _weldingDataHex = '';
            print('⚠️ 未收到焊机数据响应');
          }
        } else {
          _weldingDataHex = '';
          print('❌ 焊机数据查询命令发送失败');
        }
      } else {
        _weldingDataHex = '';
        print('📱 设备未连接，无法获取焊机数据');
      }

      print(
          '🔧 160字节焊机数据收集完成: ${_weldingDataHex.isEmpty ? '无数据' : '${_weldingDataHex.length ~/ 2} 字节'}');
    } catch (e) {
      print('💥 收集焊机数据异常: $e');
      _weldingDataHex = '';
    }
  }

  // 从蓝牙服务获取最新的焊机数据
  Future<String?> _getLatestWeldingDataFromBluetooth() async {
    try {
      // 这里应该从蓝牙服务中获取实际接收到的数据
      // 目前先返回空，需要根据实际的蓝牙数据接收机制来实现

      // 检查蓝牙服务是否有存储最新接收到的数据
      // 如果有专门的方法获取最新焊机数据，在这里调用

      return null; // 暂时返回null，等待实际数据接收机制
    } catch (e) {
      print('从蓝牙获取焊机数据失败: $e');
      return null;
    }
  }

  // 保存焊接数据
  Future<void> _saveWeldingData() async {
    try {
      final isOfflineMode = _offlineModeService.currentState.isOfflineMode;

      if (isOfflineMode) {
        // 离线模式：保存到本地存储
        print('离线模式：保存焊接数据到本地存储...');
        await _saveWeldingDataOffline();
        print('焊接数据已保存到本地存储');
      } else {
        // 在线模式：上传到服务器
        print('在线模式：上传焊接数据到服务器...');
        await _uploadWeldingDataOnline();
        print('焊接数据已上传到服务器');
      }
    } catch (e) {
      print('保存焊接数据失败: $e');
      setState(() {
        _statusMessage = '保存焊接数据失败: $e';
      });
    }
  }

  // 离线模式保存焊接数据
  Future<void> _saveWeldingDataOffline() async {
    try {
      // 使用实际拍摄的焊接图片（如果有的话）
      List<String> base64Images = _weldingImages.isNotEmpty
          ? _weldingImages
          : []; // 不使用模拟图片，如果没有实际图片就为空

      // 保存到离线存储
      final weldingId = await _offlineModeService.saveWeldingData(
        _weldingParams,
        _weldingDataHex,
        base64Images,
      );

      print('焊接数据已保存到离线存储，ID: $weldingId');
    } catch (e) {
      print('离线保存焊接数据失败: $e');
      throw e;
    }
  }

  // 在线模式上传焊接数据
  Future<void> _uploadWeldingDataOnline() async {
    try {
      // TODO: 实现实际的网络上传逻辑
      // 这里需要调用实际的API接口上传焊接数据

      print('⚠️ 在线上传功能待实现：需要实际的API接口');

      // 暂时记录需要上传的数据信息
      print('待上传数据：');
      print('- 焊接参数: ${_weldingParams.keys.join(', ')}');
      print(
          '- 焊机数据: ${_weldingDataHex.isEmpty ? '无' : '${_weldingDataHex.length ~/ 2}字节'}');
      print('- 焊接图片: ${_weldingImages.length}张');

      // 等待实际API实现
      await Future.delayed(Duration(seconds: 1));
    } catch (e) {
      print('在线上传焊接数据失败: $e');
      throw e;
    }
  }

  // 🎯 处理焊接结果和状态
  Future<void> _handleWeldingResult() async {
    try {
      setState(() {
        _statusMessage = '正在检测焊接结果...';
      });

      // 模拟焊接结果检测（实际项目中应该从PLC或传感器获取）
      await Future.delayed(Duration(seconds: 2));

      // 随机生成焊接结果用于演示（实际项目中应该是真实的检测结果）
      final random = DateTime.now().millisecond % 10;
      int weldingStatus;

      if (random < 7) {
        // 70% 概率焊接成功
        weldingStatus = WeldingJointNumberService.WELDING_SUCCESS;
      } else if (random < 8) {
        // 10% 概率吸热失败
        weldingStatus = WeldingJointNumberService.HEAT_ABSORPTION_FAILED;
      } else if (random < 9) {
        // 10% 概率卷边失败
        weldingStatus = WeldingJointNumberService.CURLING_FAILED;
      } else {
        // 10% 概率冷却失败
        weldingStatus = WeldingJointNumberService.COOLING_FAILED;
      }

      // 处理焊接状态
      bool statusHandled =
          await _jointNumberService.handleWeldingResult(weldingStatus);

      if (statusHandled) {
        final statusDescription =
            _jointNumberService.getStatusDescription(weldingStatus);
        final currentJointNumber =
            await _jointNumberService.getCurrentJointNumber();

        setState(() {
          if (weldingStatus == WeldingJointNumberService.WELDING_SUCCESS) {
            _statusMessage = '焊接成功！焊口编号: $currentJointNumber';
          } else {
            _statusMessage =
                '焊接失败: $statusDescription\n焊口编号保持不变: $currentJointNumber\n请重新焊接';
          }
        });

        // 显示详细的状态信息
        _showWeldingResultDialog(weldingStatus, currentJointNumber ?? '未知');
      } else {
        setState(() {
          _statusMessage = '焊接状态处理失败';
        });
      }
    } catch (e) {
      print('处理焊接结果失败: $e');
      setState(() {
        _statusMessage = '焊接结果检测失败: $e';
      });
    }
  }

  // 显示焊接结果对话框
  void _showWeldingResultDialog(int statusCode, String jointNumber) {
    final isSuccess = statusCode == WeldingJointNumberService.WELDING_SUCCESS;
    final statusDescription =
        _jointNumberService.getStatusDescription(statusCode);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                isSuccess ? Icons.check_circle : Icons.error,
                color: isSuccess ? Colors.green : Colors.red,
              ),
              SizedBox(width: 8),
              Text(isSuccess ? '焊接成功' : '焊接失败'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('焊口编号: $jointNumber'),
              SizedBox(height: 8),
              Text('状态: $statusDescription'),
              if (!isSuccess) ...[
                SizedBox(height: 8),
                Text(
                  '焊口编号将保持不变，请重新进行焊接操作',
                  style: TextStyle(color: Colors.orange),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭对话框
                if (isSuccess) {
                  Navigator.of(context).pop(); // 返回上一页面
                }
              },
              child: Text(isSuccess ? '完成' : '重新焊接'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('焊接操作'),
      ),
      body: Column(
        children: [
          // 顶部状态显示
          Container(
            padding: EdgeInsets.all(16),
            color: _weldingCompleted
                ? Colors.green.shade100
                : Colors.blue.shade100,
            child: Row(
              children: [
                Icon(
                  _weldingCompleted ? Icons.check_circle : Icons.info_outline,
                  color: _weldingCompleted ? Colors.green : Colors.blue,
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _statusMessage,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _weldingCompleted
                          ? Colors.green.shade700
                          : Colors.blue.shade700,
                    ),
                  ),
                ),
                if (_isLoading)
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        _weldingCompleted ? Colors.green : Colors.blue,
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // 进度指示器
          Padding(
            padding: EdgeInsets.all(16),
            child: Row(
              children: List.generate(
                4,
                (index) => Expanded(
                  child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 4),
                    height: 6,
                    decoration: BoxDecoration(
                      color: index <= _currentStep
                          ? (index == 3 && _weldingCompleted
                              ? Colors.green
                              : Colors.blue)
                          : Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ),
              ),
            ),
          ),

          // 主要内容区域
          Expanded(
            child: _weldingCompleted
                ? _buildCompletedView()
                : Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (_isLoading) ...[
                          SizedBox(
                            width: 100,
                            height: 100,
                            child: CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.blue),
                            ),
                          ),
                          SizedBox(height: 24),
                          Text(
                            _statusMessage,
                            style: TextStyle(fontSize: 18),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 8),
                          Text(
                            '请稍候，焊接操作正在进行中...',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ] else if (_weldingInProgress) ...[
                          Icon(
                            Icons.flash_on,
                            size: 120,
                            color: Colors.orange,
                          ),
                          SizedBox(height: 24),
                          Text(
                            '焊接进行中',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange,
                            ),
                          ),
                          SizedBox(height: 16),
                          Text(
                            _statusMessage,
                            style: TextStyle(fontSize: 16),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 32),
                        ] else ...[
                          Icon(
                            Icons.build,
                            size: 120,
                            color: Colors.blue,
                          ),
                          SizedBox(height: 24),
                          Text(
                            '准备开始焊接',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                          SizedBox(height: 16),
                          Text(
                            _statusMessage,
                            style: TextStyle(fontSize: 16),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 32),
                        ],
                      ],
                    ),
                  ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomActionBar(),
    );
  }

  // 构建底部操作栏
  Widget _buildBottomActionBar() {
    if (_weldingCompleted) {
      // 焊接完成后显示上传按钮
      return Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.3),
              spreadRadius: 1,
              blurRadius: 5,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 数据状态指示器
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.cloud_upload, color: Colors.green, size: 16),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '焊接数据已收集完成，等待上传',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 12),

              // 上传数据按钮
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton.icon(
                  onPressed: _uploadAllData,
                  icon: Icon(Icons.cloud_upload),
                  label: Text(
                    '上传所有数据',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      // 焊接进行中显示完成焊接按钮
      return Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.3),
              spreadRadius: 1,
              blurRadius: 5,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 焊接状态指示器
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.build, color: Colors.blue, size: 16),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '焊接操作进行中，完成后请点击下方按钮',
                        style: TextStyle(
                          color: Colors.blue.shade700,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 12),

              // 完成焊接按钮
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton.icon(
                  onPressed: _completeWelding,
                  icon: Icon(Icons.check_circle),
                  label: Text(
                    '完成焊接',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  // 上传所有数据
  void _uploadAllData() {
    // 这里可以实现数据上传逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('开始上传所有焊接数据...'),
        backgroundColor: Colors.blue,
      ),
    );

    // 可以导航到数据上传页面
    Navigator.pushNamed(context, '/dataUpload');
  }

  Widget _buildCompletedView() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Icon(
              Icons.check_circle,
              size: 80,
              color: Colors.green,
            ),
          ),
          SizedBox(height: 24),
          Center(
            child: Text(
              '焊接操作已完成',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.green.shade700,
              ),
            ),
          ),
          SizedBox(height: 32),

          // 用户信息卡片
          _buildInfoCard(
            title: '操作员信息',
            icon: Icons.person,
            color: Colors.blue,
            data: _userInfo,
          ),

          SizedBox(height: 16),

          // 项目信息卡片
          _buildInfoCard(
            title: '项目信息',
            icon: Icons.work,
            color: Colors.orange,
            data: _projectInfo,
          ),

          SizedBox(height: 16),

          // 设备信息卡片
          _buildInfoCard(
            title: '蓝牙设备信息',
            icon: Icons.bluetooth,
            color: Colors.indigo,
            data: _deviceInfo,
          ),

          SizedBox(height: 16),

          // 焊机数据卡片
          _buildWeldingMachineCard(),

          SizedBox(height: 16),

          // 位置信息卡片
          _buildInfoCard(
            title: '位置信息',
            icon: Icons.location_on,
            color: Colors.green,
            data: _locationInfo,
          ),

          SizedBox(height: 16),

          // 焊接参数卡片
          _buildWeldingParametersCard(),

          SizedBox(height: 16),

          // 焊接配置信息卡片
          _buildInfoCard(
            title: '焊接配置信息',
            icon: Icons.settings,
            color: Colors.purple,
            data: _weldingConfigInfo,
          ),

          SizedBox(height: 16),

          // 设备配置信息卡片
          _buildInfoCard(
            title: '设备配置信息',
            icon: Icons.device_hub,
            color: Colors.teal,
            data: _deviceConfigInfo,
          ),

          SizedBox(height: 16),

          // GPS配置信息卡片
          _buildInfoCard(
            title: 'GPS配置信息',
            icon: Icons.gps_fixed,
            color: Colors.brown,
            data: _gpsConfigInfo,
          ),

          SizedBox(height: 16),

          // 离线模式信息卡片
          _buildInfoCard(
            title: '离线模式信息',
            icon: Icons.cloud_off,
            color: Colors.grey,
            data: _offlineModeInfo,
          ),

          SizedBox(height: 24),

          // 历史焊接数据部分
          _buildHistoricalDataSection(),
        ],
      ),
    );
  }

  // 构建信息卡片的通用方法
  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required Color color,
    required Map<String, dynamic> data,
  }) {
    // 检查是否有上传状态
    bool hasPendingUpload =
        data.containsKey('pendingUpload') && data['pendingUpload'] == true;
    String uploadStatus = data['uploadStatus']?.toString() ?? '';

    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: color,
                    ),
                  ),
                ),
                // 显示上传状态标签
                if (hasPendingUpload)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade100,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.orange.shade300),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.cloud_upload,
                            size: 14, color: Colors.orange.shade700),
                        SizedBox(width: 4),
                        Text(
                          uploadStatus,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.orange.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            Divider(),
            if (data.containsKey('error'))
              _buildErrorItem(
                  data['error'].toString(), data['message']?.toString())
            else
              ...data.entries
                  .where((entry) =>
                      entry.key != 'pendingUpload' &&
                      entry.key != 'uploadStatus' &&
                      entry.key != 'dataType')
                  .map((entry) => _buildDetailItem(
                        _formatFieldName(entry.key),
                        _formatFieldValue(entry.value),
                      )),
          ],
        ),
      ),
    );
  }

  // 构建焊机数据专用卡片
  Widget _buildWeldingMachineCard() {
    // 检查是否有上传状态
    bool hasPendingUpload = _weldingMachineData.containsKey('pendingUpload') &&
        _weldingMachineData['pendingUpload'] == true;
    String uploadStatus = _weldingMachineData['uploadStatus']?.toString() ?? '';

    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.precision_manufacturing, color: Colors.red),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '焊机数据 (160字节)',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Colors.red,
                    ),
                  ),
                ),
                // 显示上传状态标签
                if (hasPendingUpload)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade100,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.orange.shade300),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.cloud_upload,
                            size: 14, color: Colors.orange.shade700),
                        SizedBox(width: 4),
                        Text(
                          uploadStatus,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.orange.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            Divider(),
            if (_weldingMachineData.containsKey('error'))
              _buildErrorItem(_weldingMachineData['error'].toString(),
                  _weldingMachineData['message']?.toString())
            else ...[
              _buildDetailItem('数据长度',
                  _weldingMachineData['dataLength']?.toString() ?? '未知'),
              _buildDetailItem('查询状态',
                  _weldingMachineData['querySuccess'] == true ? '成功' : '失败'),
              _buildDetailItem(
                  '收集时间',
                  _formatDateTime(
                      _weldingMachineData['collectionTime']?.toString())),
              _buildDetailItem('焊接状态',
                  _weldingMachineData['weldingStatus']?.toString() ?? '未知'),

              // 优先显示解析后的数据
              if (_weldingMachineData.containsKey('parsedData') &&
                  _weldingMachineData['parsedData'] != null &&
                  _weldingMachineData['parsedData'].toString().isNotEmpty)
                _buildParsedDataFromConfig(
                    _weldingMachineData['parsedData'].toString())
              else if (_weldingMachineData.containsKey('hexData') &&
                  _weldingMachineData['hexData'] != null &&
                  _weldingMachineData['hexData'].toString().isNotEmpty)
                _buildParsed160ByteData(
                    _weldingMachineData['hexData'].toString())
              else
                _buildDetailItem('焊机数据', '暂无数据'),
            ],
          ],
        ),
      ),
    );
  }

  // 构建从配置页面获取的解析后数据显示
  Widget _buildParsedDataFromConfig(String parsedDataString) {
    try {
      // 尝试解析存储的解析数据字符串
      // 如果是Map格式的字符串，需要解析
      if (parsedDataString.startsWith('{') && parsedDataString.endsWith('}')) {
        // 这是一个Map的字符串表示，需要解析
        return _buildParsedDataFromMapString(parsedDataString);
      } else {
        // 如果是简单的文本描述，直接显示
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 8),
            Text(
              '📊 焊机数据解析结果',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Colors.red.shade700,
              ),
            ),
            SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Text(
                parsedDataString,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.blue.shade800,
                ),
              ),
            ),
          ],
        );
      }
    } catch (e) {
      return _buildDetailItem('解析数据', '显示失败: $e');
    }
  }

  // 构建从Map字符串解析的数据显示
  Widget _buildParsedDataFromMapString(String mapString) {
    try {
      // 简化处理：直接显示格式化的文本
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 8),
          Text(
            '📊 焊机数据详细解析',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
              color: Colors.red.shade700,
            ),
          ),
          SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Text(
              _formatMapStringForDisplay(mapString),
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey.shade800,
                fontFamily: 'monospace',
              ),
            ),
          ),
        ],
      );
    } catch (e) {
      return _buildDetailItem('Map解析', '格式化失败: $e');
    }
  }

  // 格式化Map字符串用于显示
  String _formatMapStringForDisplay(String mapString) {
    try {
      // 简单的格式化处理
      String formatted = mapString
          .replaceAll('{', '{\n  ')
          .replaceAll('}', '\n}')
          .replaceAll(', ', ',\n  ')
          .replaceAll(': ', ': ');

      return formatted;
    } catch (e) {
      return mapString; // 如果格式化失败，返回原始字符串
    }
  }

  // 构建160字节数据解析显示
  Widget _buildParsed160ByteData(String hexData) {
    try {
      final parsedData = _parseWeldingDataForDisplay(hexData);

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 8),
          Text(
            '📊 详细数据解析',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
              color: Colors.red.shade700,
            ),
          ),
          SizedBox(height: 8),

          // 数据信息
          if (parsedData.containsKey('dataInfo'))
            _buildDataSection('📋 数据信息', parsedData['dataInfo'], Colors.blue),

          // 基础参数
          if (parsedData.containsKey('basicParams'))
            _buildDataSection(
                '🔧 基础参数', parsedData['basicParams'], Colors.green),

          // 工艺参数
          if (parsedData.containsKey('processParams'))
            _buildDataSection(
                '⚙️ 工艺参数', parsedData['processParams'], Colors.orange),

          // 高级参数
          if (parsedData.containsKey('advancedParams'))
            _buildDataSection(
                '🔬 高级参数', parsedData['advancedParams'], Colors.indigo),

          // 状态信息
          if (parsedData.containsKey('statusInfo'))
            _buildDataSection(
                '📊 状态信息', parsedData['statusInfo'], Colors.purple),

          // 时间信息
          if (parsedData.containsKey('timeInfo'))
            _buildDataSection('⏰ 时间信息', parsedData['timeInfo'], Colors.teal),

          // 系统信息
          if (parsedData.containsKey('systemInfo'))
            _buildDataSection(
                '🖥️ 系统信息', parsedData['systemInfo'], Colors.brown),
        ],
      );
    } catch (e) {
      return _buildDetailItem('数据解析', '解析失败: $e');
    }
  }

  // 构建数据段落
  Widget _buildDataSection(
      String title, Map<String, dynamic> data, Color color) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4),
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 12,
              color: color.withOpacity(0.8),
            ),
          ),
          SizedBox(height: 4),
          ...data.entries
              .map((entry) => Padding(
                    padding: EdgeInsets.symmetric(vertical: 1),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            entry.key,
                            style: TextStyle(
                                fontSize: 11, color: Colors.grey.shade600),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            entry.value.toString(),
                            style: TextStyle(
                                fontSize: 11, fontWeight: FontWeight.w500),
                          ),
                        ),
                      ],
                    ),
                  ))
              .toList(),
        ],
      ),
    );
  }

  // 构建焊接参数卡片
  Widget _buildWeldingParametersCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.tune, color: Colors.purple),
                SizedBox(width: 8),
                Text(
                  '焊接参数',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.purple,
                  ),
                ),
              ],
            ),
            Divider(),
            ..._weldingParams.entries.map((entry) => _buildDetailItem(
                  _formatFieldName(entry.key),
                  _formatFieldValue(entry.value),
                )),
          ],
        ),
      ),
    );
  }

  // 构建错误信息项
  Widget _buildErrorItem(String error, String? message) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.error, color: Colors.red, size: 20),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  error,
                  style: TextStyle(
                    color: Colors.red.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          if (message != null && message.isNotEmpty) ...[
            SizedBox(height: 4),
            Text(
              message,
              style: TextStyle(
                color: Colors.red.shade600,
                fontSize: 12,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // 格式化字段名
  String _formatFieldName(String fieldName) {
    Map<String, String> fieldMapping = {
      'userId': '用户ID',
      'userName': '用户名',
      'loginTime': '登录时间',
      'userRole': '用户角色',
      'department': '部门',
      'employeeId': '员工编号',
      'projectId': '项目ID',
      'projectCode': '项目编号',
      'projectName': '项目名称',
      'projectAddress': '项目地址',
      'selectedTime': '选择时间',
      'status': '状态',
      'deviceId': '设备ID',
      'deviceName': '设备名称',
      'connectionTime': '连接时间',
      'isConnected': '连接状态',
      'services': '服务数量',
      'longitude': '经度',
      'latitude': '纬度',
      'altitude': '海拔',
      'accuracy': '精度',
      'timestamp': '时间戳',
      'formatted': '格式化显示',
      'currentSetpoint': '电流设定值',
      'voltageSetpoint': '电压设定值',
      'wireSpeed': '送丝速度',
      'arcLength': '弧长',
      'inductance': '电感',
      'preflowTime': '预气时间',
      'postflowTime': '后气时间',
      'slowFeedSpeed': '慢送速度',
      'querySuccess': '查询状态',
      'note': '备注',
      'error': '错误信息',
      'totalBytes': '总字节数',
      'header': '数据头',
      'dataPreview': '数据预览',
      'checksum': '校验和',
    };

    return fieldMapping[fieldName] ?? fieldName;
  }

  // 格式化字段值
  String _formatFieldValue(dynamic value) {
    if (value == null) return '未设置';
    if (value is bool) return value ? '是' : '否';
    if (value is double) {
      if (value.abs() > 1000) {
        return value.toStringAsFixed(2);
      } else {
        return value.toStringAsFixed(6);
      }
    }
    if (value is String && value.contains('T')) {
      return _formatDateTime(value);
    }
    return value.toString();
  }

  // 格式化日期时间
  String _formatDateTime(dynamic dateTimeValue) {
    if (dateTimeValue == null) return '未知时间';

    DateTime? dateTime;
    if (dateTimeValue is String) {
      if (dateTimeValue.isEmpty) return '未知时间';
      try {
        dateTime = DateTime.parse(dateTimeValue);
      } catch (e) {
        return dateTimeValue;
      }
    } else if (dateTimeValue is DateTime) {
      dateTime = dateTimeValue;
    } else {
      return dateTimeValue.toString();
    }

    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
  }

  Widget _buildHistoricalDataSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.history, color: Colors.orange),
            SizedBox(width: 8),
            Text(
              '历史焊接数据',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18,
                color: Colors.orange.shade700,
              ),
            ),
            Spacer(),
            TextButton.icon(
              onPressed: _loadHistoricalData,
              icon: Icon(Icons.refresh, size: 16),
              label: Text('刷新'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.orange,
              ),
            ),
          ],
        ),

        SizedBox(height: 12),

        if (_isLoadingHistory)
          Center(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 8),
                  Text('正在加载历史数据...'),
                ],
              ),
            ),
          )
        else if (_historicalWeldingData.isEmpty)
          Card(
            elevation: 1,
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Center(
                child: Column(
                  children: [
                    Icon(Icons.inbox, size: 48, color: Colors.grey),
                    SizedBox(height: 8),
                    Text(
                      '暂无历史焊接数据',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          )
        else
          _buildHistoricalDataList(),

        SizedBox(height: 16),

        // 查看更多按钮
        Center(
          child: ElevatedButton.icon(
            onPressed: () {
              Navigator.pushNamed(context, '/weldingDataHistory');
            },
            icon: Icon(Icons.list_alt),
            label: Text('查看完整历史记录'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHistoricalDataList() {
    // 显示最近的5条记录
    final recentData = _historicalWeldingData.take(5).toList();

    return Column(
      children: [
        // 统计信息
        Card(
          elevation: 1,
          color: Colors.orange.shade50,
          child: Padding(
            padding: EdgeInsets.all(12),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    '总计',
                    _historicalWeldingData.length.toString(),
                    Icons.storage,
                    Colors.blue,
                  ),
                ),
                Container(width: 1, height: 30, color: Colors.grey.shade300),
                Expanded(
                  child: _buildStatItem(
                    '已上传',
                    _historicalWeldingData
                        .where((data) => data.isUploaded)
                        .length
                        .toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                Container(width: 1, height: 30, color: Colors.grey.shade300),
                Expanded(
                  child: _buildStatItem(
                    '待上传',
                    _historicalWeldingData
                        .where((data) => !data.isUploaded)
                        .length
                        .toString(),
                    Icons.upload_outlined,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 12),

        // 最近的焊接记录
        Text(
          '最近焊接记录 (显示最新5条)',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
          ),
        ),

        SizedBox(height: 8),

        ...recentData.map((data) => _buildHistoricalDataCard(data)).toList(),
      ],
    );
  }

  Widget _buildStatItem(
      String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildHistoricalDataCard(OfflineWeldingData data) {
    return Card(
      margin: EdgeInsets.only(bottom: 8),
      elevation: 1,
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(Icons.build, color: Colors.blue, size: 16),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '焊口: ${data.id}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        '项目: ${data.projectId}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: data.isUploaded
                        ? Colors.green.withOpacity(0.1)
                        : Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: data.isUploaded ? Colors.green : Colors.orange,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        data.isUploaded
                            ? Icons.check_circle
                            : Icons.upload_outlined,
                        size: 10,
                        color: data.isUploaded ? Colors.green : Colors.orange,
                      ),
                      SizedBox(width: 2),
                      Text(
                        data.isUploaded ? '已上传' : '待上传',
                        style: TextStyle(
                          color: data.isUploaded ? Colors.green : Colors.orange,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.access_time, size: 12, color: Colors.grey[600]),
                SizedBox(width: 4),
                Text(
                  _formatDateTime(data.timestamp),
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(width: 12),
                Icon(Icons.person, size: 12, color: Colors.grey[600]),
                SizedBox(width: 4),
                Text(
                  data.userId,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[600],
                  ),
                ),
                if (data.imagePaths.isNotEmpty) ...[
                  SizedBox(width: 12),
                  Icon(Icons.image, size: 12, color: Colors.grey[600]),
                  SizedBox(width: 4),
                  Text(
                    '${data.imagePaths.length}张',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _loadHistoricalData() async {
    setState(() {
      _isLoadingHistory = true;
    });

    try {
      final allData = await _storageService.getAllWeldingData();
      // 按时间倒序排列（最新的在前面）
      allData.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      setState(() {
        _historicalWeldingData = allData;
        _isLoadingHistory = false;
      });
    } catch (e) {
      print('加载历史焊接数据失败: $e');
      setState(() {
        _historicalWeldingData = [];
        _isLoadingHistory = false;
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 解析160字节焊机数据（用于显示）
  Map<String, dynamic> _parseWeldingDataForDisplay(String hexData) {
    try {
      // 移除空格并转换为字节数组
      String cleanHex = hexData.replaceAll(' ', '').toUpperCase();

      if (cleanHex.length != 320) {
        return {
          'error': '数据长度错误',
          'message': '期望320字符(160字节)，实际${cleanHex.length}字符',
        };
      }

      List<int> bytes = [];
      for (int i = 0; i < cleanHex.length; i += 2) {
        bytes.add(int.parse(cleanHex.substring(i, i + 2), radix: 16));
      }

      // 解析各个参数（使用小端序）
      return {
        'dataInfo': _parseDataInfoForDisplay(bytes, hexData),
        'basicParams': _parseBasicParamsForDisplay(bytes),
        'processParams': _parseProcessParamsForDisplay(bytes),
        'advancedParams': _parseAdvancedParamsForDisplay(bytes),
        'statusInfo': _parseStatusInfoForDisplay(bytes),
        'timeInfo': _parseTimeInfoForDisplay(bytes),
        'systemInfo': _parseSystemInfoForDisplay(bytes),
      };
    } catch (e) {
      return {
        'error': '解析失败',
        'message': e.toString(),
      };
    }
  }

  // 解析基础参数（小端序）
  Map<String, dynamic> _parseBasicParamsForDisplay(List<int> bytes) {
    int dataStartIndex = 17; // 从字索引17开始 (字节索引34)

    return {
      '管材直径': '${_getWordValueLittleEndian(bytes, dataStartIndex + 0)} mm',
      '管材SDR': _getWordValueLittleEndian(bytes, dataStartIndex + 1).toString(),
      '管材厚度': '${_getWordValueLittleEndian(bytes, dataStartIndex + 2)} mm',
      '环境温度': '${_getWordValueLittleEndian(bytes, dataStartIndex + 3)} °C',
      '热板温度': '${_getWordValueLittleEndian(bytes, dataStartIndex + 4)} °C',
      '拖动压力': '${_getWordValueLittleEndian(bytes, dataStartIndex + 5)} bar',
    };
  }

  // 解析工艺参数（小端序）
  Map<String, dynamic> _parseProcessParamsForDisplay(List<int> bytes) {
    int dataStartIndex = 17;
    int processStartIndex = dataStartIndex + 6;

    return {
      '卷边设定压力':
          '${_getWordValueLittleEndian(bytes, processStartIndex + 0)} bar',
      '卷边实际压力':
          '${_getWordValueLittleEndian(bytes, processStartIndex + 1)} bar',
      '卷边设定时间': '${_getWordValueLittleEndian(bytes, processStartIndex + 2)} s',
      '卷边实际时间': '${_getWordValueLittleEndian(bytes, processStartIndex + 3)} s',
      '吸热设定压力':
          '${_getWordValueLittleEndian(bytes, processStartIndex + 4)} bar',
      '吸热实际压力':
          '${_getWordValueLittleEndian(bytes, processStartIndex + 5)} bar',
      '吸热设定时间': '${_getWordValueLittleEndian(bytes, processStartIndex + 6)} s',
      '吸热实际时间': '${_getWordValueLittleEndian(bytes, processStartIndex + 7)} s',
      '转换时间': '${_getWordValueLittleEndian(bytes, processStartIndex + 8)} s',
      '增压时间': '${_getWordValueLittleEndian(bytes, processStartIndex + 9)} s',
      '冷却设定压力':
          '${_getWordValueLittleEndian(bytes, processStartIndex + 10)} bar',
      '冷却实际压力':
          '${_getWordValueLittleEndian(bytes, processStartIndex + 11)} bar',
      '冷却时间': '${_getWordValueLittleEndian(bytes, processStartIndex + 12)} s',
    };
  }

  // 解析状态信息（小端序）
  Map<String, dynamic> _parseStatusInfoForDisplay(List<int> bytes) {
    int dataStartIndex = 17;
    int statusValue = _getWordValueLittleEndian(bytes, dataStartIndex + 20);
    return {
      '焊接状态码': statusValue,
      '焊接状态': _getWeldingStatusTextForDisplay(statusValue),
      '状态描述': _getStatusDescriptionForDisplay(statusValue),
    };
  }

  // 解析时间信息（小端序）
  Map<String, dynamic> _parseTimeInfoForDisplay(List<int> bytes) {
    return {
      '熔接开始日期': '测试数据 - 无时间信息',
      '熔接开始时间': '测试数据 - 无时间信息',
      '熔接结束日期': '测试数据 - 无时间信息',
      '熔接结束时间': '测试数据 - 无时间信息',
    };
  }

  // 从字节数组获取16位字值（小端序）
  int _getWordValueLittleEndian(List<int> bytes, int wordIndex) {
    int byteIndex = wordIndex * 2;
    if (byteIndex + 1 < bytes.length) {
      return bytes[byteIndex] | (bytes[byteIndex + 1] << 8);
    }
    return 0;
  }

  // 获取焊接状态文本
  String _getWeldingStatusTextForDisplay(int statusValue) {
    switch (statusValue) {
      case 0:
        return '待机';
      case 1:
        return '准备中';
      case 2:
        return '卷边阶段';
      case 3:
        return '吸热阶段';
      case 4:
        return '转换阶段';
      case 5:
        return '增压阶段';
      case 6:
        return '冷却阶段';
      case 7:
        return '焊接完成';
      case 8:
        return '焊接失败';
      case 9:
        return '设备故障';
      default:
        return '未知状态($statusValue)';
    }
  }

  // 获取状态描述
  String _getStatusDescriptionForDisplay(int statusValue) {
    switch (statusValue) {
      case 0:
        return '设备处于待机状态，等待操作指令';
      case 1:
        return '设备正在准备焊接，检查各项参数';
      case 2:
        return '正在进行管材端面卷边处理';
      case 3:
        return '热板加热，管材端面吸热软化';
      case 4:
        return '移除热板，准备对接';
      case 5:
        return '增加压力，确保焊接质量';
      case 6:
        return '冷却阶段，等待焊缝固化';
      case 7:
        return '焊接过程成功完成';
      case 8:
        return '焊接过程中出现问题，需要检查';
      case 9:
        return '设备出现故障，需要维修';
      default:
        return '状态码异常，请检查设备';
    }
  }

  // 解析数据信息
  Map<String, dynamic> _parseDataInfoForDisplay(
      List<int> bytes, String hexData) {
    return {
      '数据长度': '${bytes.length} 字节',
      '数据格式': '十六进制',
      '解析时间': DateTime.now().toString().substring(0, 19),
      '数据完整性': bytes.length == 160 ? '完整' : '不完整',
      '原始数据预览': bytes
          .take(20)
          .map((b) => b.toRadixString(16).padLeft(2, '0').toUpperCase())
          .join(' '),
    };
  }

  // 解析高级参数
  Map<String, dynamic> _parseAdvancedParamsForDisplay(List<int> bytes) {
    int dataStartIndex = 17;
    int advancedStartIndex = dataStartIndex + 19; // 从字索引36开始

    return {
      '焊接压力设定':
          '${_getWordValueLittleEndian(bytes, advancedStartIndex + 0)} bar',
      '焊接压力实际':
          '${_getWordValueLittleEndian(bytes, advancedStartIndex + 1)} bar',
      '焊接温度设定':
          '${_getWordValueLittleEndian(bytes, advancedStartIndex + 2)} °C',
      '焊接温度实际':
          '${_getWordValueLittleEndian(bytes, advancedStartIndex + 3)} °C',
      '焊接速度设定':
          '${_getWordValueLittleEndian(bytes, advancedStartIndex + 4)} mm/min',
      '焊接速度实际':
          '${_getWordValueLittleEndian(bytes, advancedStartIndex + 5)} mm/min',
      '预热时间': '${_getWordValueLittleEndian(bytes, advancedStartIndex + 6)} s',
      '保温时间': '${_getWordValueLittleEndian(bytes, advancedStartIndex + 7)} s',
      '冷却速率':
          '${_getWordValueLittleEndian(bytes, advancedStartIndex + 8)} °C/min',
      '质量等级': _getQualityLevel(
          _getWordValueLittleEndian(bytes, advancedStartIndex + 9)),
    };
  }

  // 解析系统信息
  Map<String, dynamic> _parseSystemInfoForDisplay(List<int> bytes) {
    int systemStartIndex = 70; // 从字索引70开始

    return {
      '系统版本':
          '${_getWordValueLittleEndian(bytes, systemStartIndex + 0)}.${_getWordValueLittleEndian(bytes, systemStartIndex + 1)}',
      '设备序列号':
          _getWordValueLittleEndian(bytes, systemStartIndex + 2).toString(),
      '校准日期': _formatCalibrationDate(
          _getWordValueLittleEndian(bytes, systemStartIndex + 3)),
      '维护计数': _getWordValueLittleEndian(bytes, systemStartIndex + 4).toString(),
      '错误代码': _getWordValueLittleEndian(bytes, systemStartIndex + 5).toString(),
      '运行时间': '${_getWordValueLittleEndian(bytes, systemStartIndex + 6)} 小时',
      '焊接次数': _getWordValueLittleEndian(bytes, systemStartIndex + 7).toString(),
      '传感器状态': _getSensorStatus(
          _getWordValueLittleEndian(bytes, systemStartIndex + 8)),
      '电源状态': _getPowerStatus(
          _getWordValueLittleEndian(bytes, systemStartIndex + 9)),
      '通信状态': _getCommunicationStatus(
          _getWordValueLittleEndian(bytes, systemStartIndex + 10)),
    };
  }

  // 获取质量等级
  String _getQualityLevel(int level) {
    switch (level) {
      case 0:
        return '标准';
      case 1:
        return '优良';
      case 2:
        return '优秀';
      case 3:
        return '完美';
      default:
        return '未知($level)';
    }
  }

  // 格式化校准日期
  String _formatCalibrationDate(int dateValue) {
    if (dateValue == 0) return '未校准';
    // 假设日期值是从2020年开始的天数
    DateTime baseDate = DateTime(2020, 1, 1);
    DateTime calibrationDate = baseDate.add(Duration(days: dateValue));
    return '${calibrationDate.year}-${calibrationDate.month.toString().padLeft(2, '0')}-${calibrationDate.day.toString().padLeft(2, '0')}';
  }

  // 获取传感器状态
  String _getSensorStatus(int status) {
    switch (status) {
      case 0:
        return '正常';
      case 1:
        return '警告';
      case 2:
        return '故障';
      default:
        return '未知($status)';
    }
  }

  // 获取电源状态
  String _getPowerStatus(int status) {
    switch (status) {
      case 0:
        return '正常';
      case 1:
        return '电压低';
      case 2:
        return '电压高';
      case 3:
        return '断电';
      default:
        return '未知($status)';
    }
  }

  // 获取通信状态
  String _getCommunicationStatus(int status) {
    switch (status) {
      case 0:
        return '正常';
      case 1:
        return '连接中';
      case 2:
        return '断开';
      case 3:
        return '错误';
      default:
        return '未知($status)';
    }
  }
}
