import 'package:flutter/material.dart';
import '../../services/user_service.dart';

class OfflineUsersScreen extends StatefulWidget {
  const OfflineUsersScreen({Key? key}) : super(key: key);

  @override
  _OfflineUsersScreenState createState() => _OfflineUsersScreenState();
}

class _OfflineUsersScreenState extends State<OfflineUsersScreen> {
  final UserService _userService = UserService();

  bool _isLoading = true;
  String _errorMessage = '';
  String _currentUserName = '';
  String _currentUserId = '';
  List<Map<String, dynamic>> _cachedUsers = [];

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // 获取当前登录用户信息
      final userId = await _userService.getUserId();
      final userName = await _userService.getUserName();

      if (userId != null) {
        _currentUserId = userId;
      }

      if (userName != null) {
        _currentUserName = userName;
      }

      // 模拟从本地加载缓存的用户数据
      await Future.delayed(Duration(milliseconds: 800));

      // 模拟数据 - 实际应用中应从本地缓存获取
      final users = [
        {
          'id': _currentUserId,
          'name': _currentUserName,
          'role': '管理员',
          'lastLogin': DateTime.now().toString(),
          'isCurrent': true
        },
        {
          'id': 'U002',
          'name': '张三',
          'role': '焊工',
          'lastLogin': '2024-06-01 09:30:22',
          'isCurrent': false
        },
        {
          'id': 'U003',
          'name': '李四',
          'role': '监理',
          'lastLogin': '2024-05-30 14:15:36',
          'isCurrent': false
        },
        {
          'id': 'U004',
          'name': '王五',
          'role': '质检员',
          'lastLogin': '2024-05-28 16:42:05',
          'isCurrent': false
        },
      ];

      setState(() {
        _cachedUsers = users;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = '加载用户数据失败: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('本地人员信息'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadUserData,
            tooltip: '刷新',
          ),
        ],
      ),
      body: Column(
        children: [
          // 提示信息
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.blue.withOpacity(0.1),
            child: Row(
              children: const [
                Icon(Icons.info_outline, color: Colors.blue),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '以下是存储在本地的人员信息，可在离线状态下查看。',
                    style: TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
          ),

          // 错误信息
          if (_errorMessage.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage,
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ),

          // 加载中指示器
          if (_isLoading)
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('正在加载用户数据...'),
                  ],
                ),
              ),
            ),

          // 用户列表
          if (!_isLoading)
            Expanded(
              child: _cachedUsers.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      itemCount: _cachedUsers.length,
                      padding: const EdgeInsets.all(16),
                      itemBuilder: (context, index) {
                        final user = _cachedUsers[index];
                        return _buildUserCard(user);
                      },
                    ),
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey.withOpacity(0.5),
          ),
          SizedBox(height: 16),
          Text(
            '没有本地人员信息',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '请先在线同步人员数据',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            icon: Icon(Icons.refresh),
            label: Text('刷新'),
            onPressed: _loadUserData,
          ),
        ],
      ),
    );
  }

  Widget _buildUserCard(Map<String, dynamic> user) {
    final bool isCurrent = user['isCurrent'] ?? false;
    final Color cardColor = isCurrent ? Colors.blue.shade50 : Colors.white;
    final String roleLabel = user['role'] ?? '未知角色';
    final Color roleColor = _getRoleColor(roleLabel);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      color: cardColor,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor:
              isCurrent ? Colors.blue.shade100 : Colors.grey.shade200,
          child: Icon(
            Icons.person,
            color: isCurrent ? Colors.blue : Colors.grey.shade700,
          ),
        ),
        title: Row(
          children: [
            Text(
              user['name'] ?? '未知用户',
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            if (isCurrent)
              Container(
                margin: EdgeInsets.only(left: 8),
                padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '当前',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 4),
            Text('ID: ${user['id'] ?? '未知ID'}'),
            SizedBox(height: 4),
            Text('最后登录: ${user['lastLogin'] ?? '未知'}'),
          ],
        ),
        trailing: Chip(
          label: Text(
            roleLabel,
            style: TextStyle(
              color: roleColor,
              fontSize: 12,
            ),
          ),
          backgroundColor: roleColor.withOpacity(0.1),
          padding: EdgeInsets.zero,
          labelPadding: EdgeInsets.symmetric(horizontal: 8),
        ),
        isThreeLine: true,
      ),
    );
  }

  Color _getRoleColor(String role) {
    switch (role) {
      case '管理员':
        return Colors.purple;
      case '焊工':
        return Colors.orange;
      case '监理':
        return Colors.blue;
      case '质检员':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
