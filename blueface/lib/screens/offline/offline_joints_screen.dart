import 'package:flutter/material.dart';
import '../../services/project_service.dart';
import '../../models/project_model.dart';

class OfflineJointsScreen extends StatefulWidget {
  const OfflineJointsScreen({Key? key}) : super(key: key);

  @override
  _OfflineJointsScreenState createState() => _OfflineJointsScreenState();
}

class _OfflineJointsScreenState extends State<OfflineJointsScreen> {
  final ProjectService _projectService = ProjectService();

  bool _isLoading = true;
  String _errorMessage = '';
  Project? _currentProject;
  List<Map<String, dynamic>> _weldJoints = [];

  @override
  void initState() {
    super.initState();
    _loadOfflineJoints();
  }

  Future<void> _loadOfflineJoints() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // 获取当前项目
      _currentProject = await _projectService.getCurrentProject();

      if (_currentProject == null) {
        setState(() {
          _errorMessage = '无法获取当前项目信息';
          _isLoading = false;
        });
        return;
      }

      // 假设这里从本地缓存获取焊口数据
      // 在实际应用中，这里应该从SQLite数据库或其他本地存储获取
      await Future.delayed(Duration(milliseconds: 800)); // 模拟加载时间

      // 模拟数据 - 实际应用中应从本地缓存获取
      final joints = [
        {
          'id': 'WJ001',
          'name': '焊口1',
          'status': '待焊接',
          'diameter': '60mm',
          'thickness': '5.0mm',
          'material': '碳钢',
          'location': 'A区-1号管道'
        },
        {
          'id': 'WJ002',
          'name': '焊口2',
          'status': '已完成',
          'diameter': '80mm',
          'thickness': '6.5mm',
          'material': '不锈钢',
          'location': 'B区-2号管道'
        },
        {
          'id': 'WJ003',
          'name': '焊口3',
          'status': '待检验',
          'diameter': '100mm',
          'thickness': '8.0mm',
          'material': '碳钢',
          'location': 'A区-3号管道'
        },
      ];

      setState(() {
        _weldJoints = joints;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = '加载焊口数据失败: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('离线焊口数据'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadOfflineJoints,
            tooltip: '刷新',
          ),
        ],
      ),
      body: Column(
        children: [
          // 项目信息
          if (_currentProject != null)
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.blue.withOpacity(0.1),
              child: Row(
                children: [
                  Icon(Icons.business, color: Colors.blue),
                  SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '当前项目: ${_currentProject!.name}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          '编号: ${_currentProject!.code}',
                          style: TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

          // 错误信息
          if (_errorMessage.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage,
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ),

          // 加载中指示器
          if (_isLoading)
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('正在加载焊口数据...'),
                  ],
                ),
              ),
            ),

          // 焊口列表
          if (!_isLoading && _errorMessage.isEmpty)
            Expanded(
              child: _weldJoints.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      itemCount: _weldJoints.length,
                      padding: const EdgeInsets.all(16),
                      itemBuilder: (context, index) {
                        final joint = _weldJoints[index];
                        return _buildJointCard(joint);
                      },
                    ),
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.construction_outlined,
            size: 64,
            color: Colors.grey.withOpacity(0.5),
          ),
          SizedBox(height: 16),
          Text(
            '没有可用的焊口数据',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '请先同步项目数据或创建新焊口',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            icon: Icon(Icons.refresh),
            label: Text('刷新'),
            onPressed: _loadOfflineJoints,
          ),
        ],
      ),
    );
  }

  Widget _buildJointCard(Map<String, dynamic> joint) {
    final statusColor = _getStatusColor(joint['status']);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          ListTile(
            leading: CircleAvatar(
              backgroundColor: statusColor.withOpacity(0.2),
              child: Icon(
                Icons.settings_input_composite,
                color: statusColor,
              ),
            ),
            title: Text(
              '${joint['name']} (${joint['id']})',
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Text(joint['location']),
            trailing: Chip(
              label: Text(
                joint['status'],
                style: TextStyle(
                  color: statusColor,
                  fontSize: 12,
                ),
              ),
              backgroundColor: statusColor.withOpacity(0.1),
            ),
          ),

          // 详情部分
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Column(
              children: [
                Divider(),
                Row(
                  children: [
                    _buildInfoItem('直径', joint['diameter']),
                    _buildInfoItem('壁厚', joint['thickness']),
                    _buildInfoItem('材料', joint['material']),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
            SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case '待焊接':
        return Colors.orange;
      case '焊接中':
        return Colors.blue;
      case '待检验':
        return Colors.purple;
      case '已完成':
        return Colors.green;
      case '不合格':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
