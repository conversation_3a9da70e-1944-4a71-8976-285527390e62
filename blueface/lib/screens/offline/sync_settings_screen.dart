import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../services/user_service.dart';

class SyncSettingsScreen extends StatefulWidget {
  const SyncSettingsScreen({Key? key}) : super(key: key);

  @override
  _SyncSettingsScreenState createState() => _SyncSettingsScreenState();
}

class _SyncSettingsScreenState extends State<SyncSettingsScreen> {
  final UserService _userService = UserService();

  bool _isLoading = false;
  String _lastSyncTime = '未知';
  bool _autoSyncEnabled = true;
  String _syncFrequency = '每天一次';
  bool _syncOnWifiOnly = true;
  bool _syncPhotosEnabled = true;
  int _maxStorageSize = 500; // MB
  List<String> _syncFrequencyOptions = ['每天一次', '每次启动时', '每小时一次', '手动同步'];

  @override
  void initState() {
    super.initState();
    _loadSyncSettings();
  }

  Future<void> _loadSyncSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 获取上次同步时间
      final lastSyncTime = await _userService.getLastSyncTime();
      if (lastSyncTime != null) {
        setState(() {
          _lastSyncTime = lastSyncTime;
        });
      }

      // 模拟从本地加载同步设置
      // 实际应用中应该从SharedPreferences或其他本地存储获取
      await Future.delayed(Duration(milliseconds: 500));

      setState(() {
        // 这里应该是从存储中加载的设置，我们使用默认值作为示例
        _autoSyncEnabled = true;
        _syncFrequency = '每天一次';
        _syncOnWifiOnly = true;
        _syncPhotosEnabled = true;
        _maxStorageSize = 500;
        _isLoading = false;
      });
    } catch (e) {
      print('加载同步设置失败: $e');
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('加载设置失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 模拟保存设置
      await Future.delayed(Duration(milliseconds: 800));

      // 实际应用中应该保存到SharedPreferences或其他本地存储

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('设置已保存'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('保存设置失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _resetSettings() async {
    final bool confirmed = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('重置设置'),
            content: Text('确定要将所有同步设置恢复默认值吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text('确定'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red,
                ),
              ),
            ],
          ),
        ) ??
        false;

    if (confirmed) {
      setState(() {
        _autoSyncEnabled = true;
        _syncFrequency = '每天一次';
        _syncOnWifiOnly = true;
        _syncPhotosEnabled = true;
        _maxStorageSize = 500;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('设置已重置为默认值'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  Future<void> _syncNow() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 显示同步进度对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => WillPopScope(
          onWillPop: () async => false,
          child: AlertDialog(
            title: Text('正在同步数据'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                LinearProgressIndicator(),
                SizedBox(height: 16),
                Text('正在同步数据，请稍候...'),
              ],
            ),
          ),
        ),
      );

      // 模拟同步过程
      await Future.delayed(Duration(seconds: 2));

      // 更新最后同步时间
      final now = DateTime.now().toString();
      await _userService.saveLastSyncTime(now);
      setState(() {
        _lastSyncTime = now;
      });

      // 关闭对话框
      Navigator.of(context).pop();

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('数据同步成功'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // 关闭对话框
      Navigator.of(context).pop();

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('同步失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  String _formatDateTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      final formatter = DateFormat('yyyy-MM-dd HH:mm:ss');
      return formatter.format(dateTime);
    } catch (e) {
      return dateTimeString;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('数据同步设置'),
        actions: [
          IconButton(
            icon: Icon(Icons.sync),
            onPressed: _isLoading ? null : _syncNow,
            tooltip: '立即同步',
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 同步状态卡片
                  Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '同步状态',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                          ),
                          Divider(),
                          _buildInfoRow(
                              '上次同步时间', _formatDateTime(_lastSyncTime)),
                          SizedBox(height: 16),
                          ElevatedButton.icon(
                            icon: Icon(Icons.sync),
                            label: Text('立即同步'),
                            onPressed: _isLoading ? null : _syncNow,
                            style: ElevatedButton.styleFrom(
                              minimumSize: Size(double.infinity, 44),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // 同步设置卡片
                  Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '同步设置',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                          ),
                          Divider(),
                          SwitchListTile(
                            title: Text('自动同步数据'),
                            subtitle: Text('允许应用在后台自动同步数据'),
                            value: _autoSyncEnabled,
                            onChanged: (value) {
                              setState(() {
                                _autoSyncEnabled = value;
                              });
                            },
                          ),
                          Divider(),
                          ListTile(
                            title: Text('同步频率'),
                            subtitle: Text(_syncFrequency),
                            trailing: Icon(Icons.arrow_forward_ios, size: 16),
                            onTap: () {
                              _showSyncFrequencyDialog();
                            },
                          ),
                          Divider(),
                          SwitchListTile(
                            title: Text('仅在WiFi下同步'),
                            subtitle: Text('仅在连接到WiFi网络时进行同步'),
                            value: _syncOnWifiOnly,
                            onChanged: (value) {
                              setState(() {
                                _syncOnWifiOnly = value;
                              });
                            },
                          ),
                          Divider(),
                          SwitchListTile(
                            title: Text('同步照片数据'),
                            subtitle: Text('包括焊接照片在内的数据同步'),
                            value: _syncPhotosEnabled,
                            onChanged: (value) {
                              setState(() {
                                _syncPhotosEnabled = value;
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                  ),

                  // 存储设置卡片
                  Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '存储设置',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                          ),
                          Divider(),
                          ListTile(
                            title: Text('最大存储空间'),
                            subtitle: Text('$_maxStorageSize MB'),
                            trailing: Icon(Icons.arrow_forward_ios, size: 16),
                            onTap: () {
                              _showStorageSizeDialog();
                            },
                          ),
                          Divider(),
                          ListTile(
                            title: Text('清除缓存数据'),
                            subtitle: Text('删除所有本地缓存的数据'),
                            trailing:
                                Icon(Icons.delete_outline, color: Colors.red),
                            onTap: () {
                              _showClearCacheDialog();
                            },
                          ),
                        ],
                      ),
                    ),
                  ),

                  // 按钮区域
                  SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _resetSettings,
                          child: Text('重置设置'),
                          style: OutlinedButton.styleFrom(
                            padding: EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _saveSettings,
                          child: Text('保存设置'),
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _showSyncFrequencyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('选择同步频率'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _syncFrequencyOptions.map((frequency) {
            return RadioListTile<String>(
              title: Text(frequency),
              value: frequency,
              groupValue: _syncFrequency,
              onChanged: (value) {
                Navigator.of(context).pop();
                if (value != null) {
                  setState(() {
                    _syncFrequency = value;
                  });
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('取消'),
          ),
        ],
      ),
    );
  }

  void _showStorageSizeDialog() {
    int tempStorageSize = _maxStorageSize;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('设置最大存储空间'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('选择离线数据的最大存储空间（MB）：'),
            SizedBox(height: 16),
            StatefulBuilder(
              builder: (context, setState) => Slider(
                value: tempStorageSize.toDouble(),
                min: 100,
                max: 2000,
                divisions: 19,
                label: '$tempStorageSize MB',
                onChanged: (value) {
                  setState(() {
                    tempStorageSize = value.round();
                  });
                },
              ),
            ),
            Text(
              '当前选择: $tempStorageSize MB',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('取消'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _maxStorageSize = tempStorageSize;
              });
              Navigator.of(context).pop();
            },
            child: Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showClearCacheDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('清除缓存数据'),
        content: Text('确定要清除所有本地缓存数据吗？此操作不可撤销，但不会影响已同步到服务器的数据。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _clearCache();
            },
            child: Text('清除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _clearCache() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 模拟清除缓存
      await Future.delayed(Duration(seconds: 1));

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('缓存数据已清除'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('清除缓存失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
