import 'package:flutter/material.dart';
import '../../services/project_service.dart';
import '../../models/project_model.dart';

class OfflineProjectsScreen extends StatefulWidget {
  @override
  _OfflineProjectsScreenState createState() => _OfflineProjectsScreenState();
}

class _OfflineProjectsScreenState extends State<OfflineProjectsScreen> {
  final ProjectService _projectService = ProjectService();

  List<Project> _cachedProjects = [];
  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadCachedProjects();
  }

  Future<void> _loadCachedProjects() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final projects = await _projectService.getCachedProjects();
      setState(() {
        _cachedProjects = projects;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = '加载缓存项目失败: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _setAsCurrentProject(Project project) async {
    try {
      await _projectService.saveCurrentProject(project);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已将"${project.name}"设置为当前项目'),
          backgroundColor: Colors.green,
        ),
      );

      // 刷新项目列表
      await _loadCachedProjects();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('设置当前项目失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('离线项目管理'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadCachedProjects,
            tooltip: '刷新',
          ),
        ],
      ),
      body: Column(
        children: [
          // 提示信息
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.blue.withOpacity(0.1),
            child: Row(
              children: const [
                Icon(Icons.info_outline, color: Colors.blue),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '离线模式下可访问以下缓存项目。要添加新项目，请连接网络并在项目选项卡中同步。',
                    style: TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
          ),

          // 错误信息显示
          if (_errorMessage.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage,
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ),

          // 加载中指示器
          if (_isLoading)
            Padding(
              padding: const EdgeInsets.all(32.0),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),

          // 项目列表
          if (!_isLoading)
            Expanded(
              child: _cachedProjects.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      itemCount: _cachedProjects.length,
                      padding: const EdgeInsets.all(16),
                      itemBuilder: (context, index) {
                        final project = _cachedProjects[index];
                        final isCurrentProject = index == 0; // 示例中假设第一个是当前项目

                        return Card(
                          margin: const EdgeInsets.only(bottom: 12),
                          clipBehavior: Clip.antiAlias,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // 项目标题栏
                              Container(
                                color: isCurrentProject
                                    ? Colors.blue.shade50
                                    : null,
                                child: ListTile(
                                  leading: CircleAvatar(
                                    backgroundColor: Colors.blue.shade100,
                                    child: Icon(
                                      Icons.business,
                                      color: Colors.blue,
                                      size: 24,
                                    ),
                                  ),
                                  title: Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          project.name,
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      if (isCurrentProject)
                                        Container(
                                          margin: EdgeInsets.only(left: 8),
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 6, vertical: 2),
                                          decoration: BoxDecoration(
                                            color: Colors.blue,
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          child: Text(
                                            '当前',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 10,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  subtitle: Text(
                                    project.address,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  trailing: PopupMenuButton(
                                    itemBuilder: (context) => [
                                      if (!isCurrentProject)
                                        PopupMenuItem(
                                          value: 'set_current',
                                          child: Row(
                                            children: [
                                              Icon(Icons.check_circle_outline,
                                                  size: 18),
                                              SizedBox(width: 8),
                                              Expanded(
                                                child: Text('设为当前项目'),
                                              ),
                                            ],
                                          ),
                                        ),
                                      PopupMenuItem(
                                        value: 'view_details',
                                        child: Row(
                                          children: [
                                            Icon(Icons.info_outline, size: 18),
                                            SizedBox(width: 8),
                                            Expanded(
                                              child: Text('查看详情'),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                    onSelected: (value) {
                                      if (value == 'set_current') {
                                        _setAsCurrentProject(project);
                                      } else if (value == 'view_details') {
                                        _showProjectDetails(context, project);
                                      }
                                    },
                                  ),
                                ),
                              ),

                              // 项目详情栏
                              Padding(
                                padding:
                                    const EdgeInsets.fromLTRB(16, 0, 16, 12),
                                child: Wrap(
                                  spacing: 8,
                                  runSpacing: 8,
                                  children: [
                                    Chip(
                                      label: Text(
                                        '编号: ${project.code}',
                                        style: const TextStyle(fontSize: 12),
                                      ),
                                      backgroundColor: Colors.grey.shade100,
                                      labelPadding: const EdgeInsets.symmetric(
                                          horizontal: 8),
                                      padding: EdgeInsets.zero,
                                      materialTapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                    ),
                                    Chip(
                                      label: Text(
                                        project.status,
                                        style: const TextStyle(fontSize: 12),
                                      ),
                                      backgroundColor:
                                          _getStatusColor(project.status),
                                      labelPadding: const EdgeInsets.symmetric(
                                          horizontal: 8),
                                      padding: EdgeInsets.zero,
                                      materialTapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder_off,
            size: 64,
            color: Colors.grey.withOpacity(0.5),
          ),
          SizedBox(height: 16),
          Text(
            '没有缓存的项目',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '在线模式下浏览项目后会自动缓存',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            icon: Icon(Icons.refresh),
            label: Text('刷新'),
            onPressed: _loadCachedProjects,
          ),
        ],
      ),
    );
  }

  void _showProjectDetails(BuildContext context, Project project) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.business, color: Colors.blue),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                project.name,
                style: const TextStyle(fontSize: 18),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailItem('项目ID', project.id),
              _buildDetailItem('项目编号', project.code),
              _buildDetailItem('项目地址', project.address),
              _buildDetailItem('施工单位', project.constructionUnit),
              _buildDetailItem('状态', project.status),
              if (project.startDate != null)
                _buildDetailItem('开始日期', project.startDate!),
              if (project.endDate != null)
                _buildDetailItem('结束日期', project.endDate!),
              if (project.description != null)
                _buildDetailItem('描述', project.description!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(fontSize: 14),
          ),
          Divider(height: 16),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case '进行中':
        return Colors.green.shade100;
      case '已完成':
        return Colors.blue.shade100;
      case '已暂停':
        return Colors.orange.shade100;
      case '已取消':
        return Colors.red.shade100;
      default:
        return Colors.grey.shade100;
    }
  }
}
