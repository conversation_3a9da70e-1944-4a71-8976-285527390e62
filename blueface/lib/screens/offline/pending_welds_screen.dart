import 'package:flutter/material.dart';
import '../../models/weld_record_model.dart';
import '../../services/weld_service.dart';
import 'package:intl/intl.dart';

class PendingWeldsScreen extends StatefulWidget {
  @override
  _PendingWeldsScreenState createState() => _PendingWeldsScreenState();
}

class _PendingWeldsScreenState extends State<PendingWeldsScreen> {
  final WeldService _weldService = WeldService();

  List<WeldRecord> _pendingRecords = [];
  bool _isLoading = false;
  bool _isUploading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadPendingRecords();
  }

  Future<void> _loadPendingRecords() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final records = await _weldService.getPendingWeldRecords();
      setState(() {
        _pendingRecords = records;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = '加载待同步焊接记录失败: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _uploadRecord(WeldRecord record) async {
    setState(() {
      _isUploading = true;
    });

    try {
      await _weldService.uploadWeldRecord(record);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('焊接记录上传成功'),
          backgroundColor: Colors.green,
        ),
      );

      // 刷新列表
      await _loadPendingRecords();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('焊接记录上传失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  Future<void> _uploadAllRecords() async {
    if (_pendingRecords.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('没有待上传的焊接记录'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isUploading = true;
    });

    try {
      int successCount = 0;
      List<String> failedRecords = [];

      for (var record in _pendingRecords) {
        try {
          await _weldService.uploadWeldRecord(record);
          successCount++;
        } catch (e) {
          failedRecords.add('${record.jointId}: $e');
        }
      }

      if (failedRecords.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('全部 $successCount 条记录上传成功'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('上传结果: $successCount 成功, ${failedRecords.length} 失败'),
            backgroundColor: Colors.orange,
          ),
        );
      }

      // 刷新列表
      await _loadPendingRecords();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('批量上传失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  Future<void> _deleteRecord(WeldRecord record) async {
    // 显示确认对话框
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('确认删除'),
        content: Text('您确定要删除焊口 ${record.jointId} 的焊接记录吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text('删除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      await _weldService.deletePendingWeldRecord(record.id);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('焊接记录已删除'),
          backgroundColor: Colors.blue,
        ),
      );

      // 刷新列表
      await _loadPendingRecords();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('删除记录失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('待上传焊接记录'),
        actions: [
          if (_pendingRecords.isNotEmpty && !_isUploading)
            IconButton(
              icon: Icon(Icons.cloud_upload),
              onPressed: _uploadAllRecords,
              tooltip: '全部上传',
            ),
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadPendingRecords,
            tooltip: '刷新',
          ),
        ],
      ),
      body: Column(
        children: [
          // 提示信息
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.blue.withOpacity(0.1),
            child: Row(
              children: const [
                Icon(Icons.info_outline, color: Colors.blue),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '以下焊接记录尚未上传到服务器。连接网络后可以手动上传这些记录。',
                    style: TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
          ),

          // 上传状态指示器
          if (_isUploading)
            Container(
              padding: const EdgeInsets.all(12),
              color: Colors.orange.withOpacity(0.1),
              child: Row(
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
                    ),
                  ),
                  SizedBox(width: 12),
                  Text(
                    '正在上传记录，请稍候...',
                    style: TextStyle(color: Colors.orange[800]),
                  ),
                ],
              ),
            ),

          // 错误信息显示
          if (_errorMessage.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage,
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ),

          // 加载中指示器
          if (_isLoading)
            Padding(
              padding: const EdgeInsets.all(32.0),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),

          // 焊接记录列表
          if (!_isLoading)
            Expanded(
              child: _pendingRecords.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      itemCount: _pendingRecords.length,
                      padding: const EdgeInsets.all(16),
                      itemBuilder: (context, index) {
                        final record = _pendingRecords[index];
                        return _buildRecordCard(record);
                      },
                    ),
            ),

          // 底部统计信息
          if (!_isLoading && _pendingRecords.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.grey.shade100,
              child: Row(
                children: [
                  Text(
                    '共 ${_pendingRecords.length} 条待上传记录',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Spacer(),
                  ElevatedButton.icon(
                    icon: Icon(Icons.cloud_upload, size: 18),
                    label: Text('全部上传'),
                    onPressed: _isUploading ? null : _uploadAllRecords,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildRecordCard(WeldRecord record) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
    final formattedDate = dateFormat.format(record.createdAt);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.orange.shade100,
              child: Icon(
                Icons.construction,
                color: Colors.orange,
                size: 24,
              ),
            ),
            title: Text(
              '焊口号: ${record.jointId}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Text(
              '创建于: $formattedDate',
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.cloud_upload,
                    color: Colors.blue,
                  ),
                  onPressed: _isUploading ? null : () => _uploadRecord(record),
                  tooltip: '上传',
                ),
                IconButton(
                  icon: Icon(
                    Icons.delete_outline,
                    color: Colors.red,
                  ),
                  onPressed: () => _deleteRecord(record),
                  tooltip: '删除',
                ),
              ],
            ),
          ),

          // 详情部分
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Divider(),
                Row(
                  children: [
                    _buildInfoChip('项目: ${record.projectName}', Icons.business),
                    SizedBox(width: 8),
                    _buildInfoChip('工序: ${record.processType}', Icons.settings),
                  ],
                ),
                SizedBox(height: 8),
                Row(
                  children: [
                    _buildInfoChip('操作员: ${record.operatorName}', Icons.person),
                    SizedBox(width: 8),
                    _buildInfoChip('设备: ${record.deviceId}', Icons.devices),
                  ],
                ),
                if (record.hasImages)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Row(
                      children: [
                        Icon(Icons.image, size: 16, color: Colors.grey[600]),
                        SizedBox(width: 4),
                        Text(
                          '包含 ${record.imageCount} 张图片',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                if (record.notes != null && record.notes!.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      '备注: ${record.notes}',
                      style: TextStyle(
                        fontSize: 13,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(String label, IconData icon) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.grey[700]),
          SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.cloud_done,
            size: 64,
            color: Colors.grey.withOpacity(0.5),
          ),
          SizedBox(height: 16),
          Text(
            '没有待上传的焊接记录',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '所有焊接记录已同步至云端',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            icon: Icon(Icons.refresh),
            label: Text('刷新'),
            onPressed: _loadPendingRecords,
          ),
        ],
      ),
    );
  }
}
