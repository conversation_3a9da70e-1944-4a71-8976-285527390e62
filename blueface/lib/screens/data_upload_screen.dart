import 'package:flutter/material.dart';
import '../services/welding_joint_number_service.dart';
import '../services/location_service.dart';

class DataUploadScreen extends StatefulWidget {
  final String? jointNumber; // 可选的焊口编号参数

  const DataUploadScreen({Key? key, this.jointNumber}) : super(key: key);

  @override
  _DataUploadScreenState createState() => _DataUploadScreenState();
}

class _DataUploadScreenState extends State<DataUploadScreen> {
  final WeldingJointNumberService _jointNumberService =
      WeldingJointNumberService();
  final LocationService _locationService = LocationService();

  String? _currentJointNumber;
  String _statusMessage = '';
  bool _isUploading = false;
  bool _isNetworkConnected = false;
  String _networkStatus = '检查中...';

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    // 获取当前焊口编号
    if (widget.jointNumber != null) {
      _currentJointNumber = widget.jointNumber;
    } else {
      _currentJointNumber = await _jointNumberService.getCurrentJointNumber();
    }

    // 检查网络状态
    await _checkNetworkStatus();

    setState(() {});
  }

  Future<void> _checkNetworkStatus() async {
    try {
      _isNetworkConnected = await _jointNumberService.isNetworkConnected();
      _networkStatus = _isNetworkConnected ? '网络已连接' : '网络未连接';
    } catch (e) {
      _networkStatus = '网络状态检查失败';
      _isNetworkConnected = false;
    }
    setState(() {});
  }

  Future<void> _uploadData() async {
    if (_currentJointNumber == null) {
      setState(() {
        _statusMessage = '错误：没有可上传的焊口编号';
      });
      return;
    }

    setState(() {
      _isUploading = true;
      _statusMessage = '正在处理数据上传...';
    });

    try {
      // 检查焊接状态
      int weldingStatus =
          await _jointNumberService.getWeldingStatus(_currentJointNumber!);
      String statusDesc =
          _jointNumberService.getStatusDescription(weldingStatus);

      if (weldingStatus != WeldingJointNumberService.WELDING_SUCCESS) {
        setState(() {
          _statusMessage = '上传失败：焊接状态不是成功状态\n当前状态：$statusDesc';
          _isUploading = false;
        });
        return;
      }

      // 执行上传流程
      bool success = await _jointNumberService
          .handleWeldingDataUpload(_currentJointNumber!);

      if (success) {
        setState(() {
          if (_isNetworkConnected) {
            _statusMessage = '✅ 联网上传成功！\n'
                '焊口编号：$_currentJointNumber\n'
                '上传状态：已同步到服务器\n'
                '信号写入：VW3202=1 已写入焊机';
          } else {
            _statusMessage = '✅ 断网存储成功！\n'
                '焊口编号：$_currentJointNumber\n'
                '存储状态：已保存到本地\n'
                '信号写入：VW3204=1 已写入焊机';
          }
        });

        // 延迟后自动返回
        Future.delayed(Duration(seconds: 3), () {
          if (mounted) {
            Navigator.pop(context);
          }
        });
      } else {
        setState(() {
          _statusMessage = '❌ 上传失败\n'
              '焊口编号：$_currentJointNumber\n'
              '错误：数据上传或信号写入失败\n'
              '请检查蓝牙连接状态';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '❌ 上传异常：$e';
      });
    }

    setState(() {
      _isUploading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('数据上传'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.orange.shade50,
              Colors.white,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              // 提示信息卡片
              Card(
                elevation: 4,
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.blue.shade50,
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 48,
                        color: Colors.blue,
                      ),
                      SizedBox(height: 16),
                      Text(
                        '焊接已完成，请进行数据上传',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade800,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 8),
                      Text(
                        '点击下方按钮将焊接数据上传到服务器或保存到本地',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(height: 24),

              // 状态信息卡片
              Card(
                elevation: 2,
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '当前状态',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 12),
                      _buildStatusRow(
                          '焊口编号', _currentJointNumber ?? '未知', Colors.blue),
                      SizedBox(height: 8),
                      _buildStatusRow('网络状态', _networkStatus,
                          _isNetworkConnected ? Colors.green : Colors.orange),
                      SizedBox(height: 8),
                      _buildStatusRow(
                          '上传模式',
                          _isNetworkConnected ? '联网上传' : '断网存储',
                          _isNetworkConnected ? Colors.green : Colors.orange),
                    ],
                  ),
                ),
              ),

              SizedBox(height: 24),

              // 上传按钮
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton.icon(
                  onPressed: _isUploading ? null : _uploadData,
                  icon: _isUploading
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Icon(
                          _isNetworkConnected ? Icons.cloud_upload : Icons.save,
                          size: 24,
                        ),
                  label: Text(
                    _isUploading
                        ? '上传中...'
                        : (_isNetworkConnected ? '上传到服务器' : '保存到本地'),
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        _isNetworkConnected ? Colors.green : Colors.orange,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),

              SizedBox(height: 16),

              // 刷新网络状态按钮
              TextButton.icon(
                onPressed: _isUploading ? null : _checkNetworkStatus,
                icon: Icon(Icons.refresh),
                label: Text('刷新网络状态'),
              ),

              SizedBox(height: 24),

              // 状态消息显示
              if (_statusMessage.isNotEmpty)
                Expanded(
                  child: Card(
                    elevation: 2,
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '操作结果',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 12),
                          Expanded(
                            child: SingleChildScrollView(
                              child: Text(
                                _statusMessage,
                                style: TextStyle(fontSize: 14),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, Color color) {
    return Row(
      children: [
        Icon(
          Icons.fiber_manual_record,
          size: 12,
          color: color,
        ),
        SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade700,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
