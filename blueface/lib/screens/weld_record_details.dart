import 'dart:io';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/weld_record.dart';

class WeldRecordDetailsScreen extends StatelessWidget {
  final WeldRecord record;

  const WeldRecordDetailsScreen({Key? key, required this.record})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('焊接记录详情'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(context),
            const SizedBox(height: 16),
            _buildImagesSection(),
            const SizedBox(height: 16),
            _buildParametersSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard(BuildContext context) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('项目名称', record.projectName),
            _buildInfoRow('焊缝编号', record.weldCode),
            _buildInfoRow(
                '操作员', '${record.operatorName} (${record.operatorId})'),
            _buildInfoRow('设备ID', record.deviceId),
            _buildInfoRow('时间', dateFormat.format(record.timeStamp)),
            _buildInfoRow('上传状态', record.isUploaded ? '已上传' : '未上传'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.black54,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImagesSection() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '记录图片',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildImageGridView(),
          ],
        ),
      ),
    );
  }

  Widget _buildImageGridView() {
    final imagePaths = [
      {'path': record.faceImagePath, 'label': '人脸照片'},
      {'path': record.deviceImagePath, 'label': '设备照片'},
      {'path': record.pipeImagePath, 'label': '焊接照片'},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
        childAspectRatio: 0.8,
      ),
      itemCount: imagePaths.length,
      itemBuilder: (context, index) {
        final item = imagePaths[index];
        return _buildImageCard(item['path']!, item['label']!);
      },
    );
  }

  Widget _buildImageCard(String path, String label) {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: path.isNotEmpty
                ? Image.file(
                    File(path),
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => const Center(
                      child: Icon(Icons.broken_image, size: 50),
                    ),
                  )
                : const Center(
                    child: Icon(Icons.image_not_supported, size: 50)),
          ),
          Container(
            padding: const EdgeInsets.all(8.0),
            color: Colors.black.withOpacity(0.7),
            child: Text(
              label,
              style: const TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildParametersSection() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '焊接参数',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...record.parameters.entries.map((entry) {
              return _buildInfoRow(entry.key, entry.value.toString());
            }).toList(),
          ],
        ),
      ),
    );
  }
}
