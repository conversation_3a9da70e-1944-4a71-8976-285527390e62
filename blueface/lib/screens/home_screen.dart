import 'package:flutter/material.dart';
import '../services/user_service.dart';
import '../services/bluetooth_service.dart';
import 'tabs/welding_tab.dart';
import 'tabs/project_tab.dart';
import 'tabs/query_tab.dart';
import 'tabs/offline_tab.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart' as fbp;

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  final UserService _userService = UserService();
  final BleService _bleService = BleService();

  // 页面列表
  late final List<Widget> _pages;

  @override
  void initState() {
    super.initState();

    _pages = [
      WeldingTab(
        bleService: _bleService,
        onSwitchToProjectTab: () {
          // 切换到项目管理标签
          setState(() {
            _selectedIndex = 1; // 项目管理标签的索引
          });
        },
      ),
      ProjectTab(bleService: _bleService),
      QueryTab(),
      OfflineTab(),
    ];

    // 监听蓝牙连接状态变化
    _bleService.connectedDeviceStream.listen((_) {
      // 当连接状态改变时，确保UI更新
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('焊接工程管理'),
        actions: [
          // 显示蓝牙状态的图标按钮
          IconButton(
            icon: Icon(
              _bleService.isConnected
                  ? Icons.bluetooth_connected
                  : Icons.bluetooth_disabled,
              color: _bleService.isConnected ? Colors.greenAccent : null,
            ),
            onPressed: () => _showBluetoothDialog(context),
            tooltip: _bleService.isConnected
                ? '已连接: ${_bleService.connectedDevice?.name}'
                : '蓝牙设备管理',
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => _confirmLogout(context),
            tooltip: '退出登录',
          ),
        ],
      ),
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.build),
            label: '焊接作业',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.folder),
            label: '项目管理',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.search),
            label: '查询中心',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.offline_bolt),
            label: '离线模式',
          ),
        ],
      ),
    );
  }

  // 显示蓝牙管理对话框
  void _showBluetoothDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) =>
          StatefulBuilder(builder: (context, setDialogState) {
        return AlertDialog(
          title: Row(
            children: const [
              Icon(Icons.bluetooth, color: Colors.blue),
              SizedBox(width: 8),
              Text('蓝牙设备管理'),
            ],
          ),
          content: Container(
            width: double.maxFinite,
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.6,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 当前连接状态
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _bleService.isConnected
                            ? Icons.bluetooth_connected
                            : Icons.bluetooth_disabled,
                        color:
                            _bleService.isConnected ? Colors.green : Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _bleService.isConnected
                              ? '已连接: ${_bleService.connectedDevice?.name}'
                              : '未连接',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                      if (_bleService.isConnected)
                        TextButton(
                          onPressed: () async {
                            // 在异步操作前获取ScaffoldMessengerState引用
                            final scaffoldMessenger =
                                ScaffoldMessenger.of(context);
                            final navigator = Navigator.of(context);

                            await _bleService.disconnectDevice();

                            // 更新对话框状态
                            setDialogState(() {});
                            // 更新主页状态
                            setState(() {});

                            // 关闭对话框
                            navigator.pop();

                            // 安全地显示消息
                            scaffoldMessenger.showSnackBar(
                              const SnackBar(content: Text('设备已断开连接')),
                            );
                          },
                          child: const Text('断开连接'),
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.red,
                          ),
                        ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // 设备扫描按钮
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      '可用设备',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    ElevatedButton(
                      onPressed: _bleService.isScanning
                          ? null
                          : () async {
                              setDialogState(() {}); // 刷新UI显示扫描状态
                              await _bleService.startScan();
                              setDialogState(() {}); // 扫描完成后刷新UI
                            },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        minimumSize: const Size(0, 0),
                      ),
                      child: Text(
                        _bleService.isScanning ? '扫描中...' : '扫描设备',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // 设备列表
                Flexible(
                  child: StreamBuilder<List<fbp.BluetoothDevice>>(
                    stream: _bleService.devicesStream,
                    initialData: _bleService.devices,
                    builder: (context, snapshot) {
                      final devices = snapshot.data ?? [];

                      if (devices.isEmpty) {
                        return Container(
                          height: 100,
                          alignment: Alignment.center,
                          child: Text(
                            _bleService.isScanning ? '正在扫描设备...' : '未发现设备',
                            style: TextStyle(color: Colors.grey),
                          ),
                        );
                      }

                      return ListView.builder(
                        shrinkWrap: true,
                        itemCount: devices.length,
                        itemBuilder: (context, index) {
                          final device = devices[index];
                          return Card(
                            margin: const EdgeInsets.only(bottom: 8),
                            child: ListTile(
                              title: Text(device.name),
                              subtitle: Text(device.id.toString()),
                              trailing: OutlinedButton(
                                onPressed: () async {
                                  // 保存状态引用
                                  final scaffoldMessenger =
                                      ScaffoldMessenger.of(context);
                                  final navigator = Navigator.of(context);

                                  navigator.pop();
                                  scaffoldMessenger.showSnackBar(
                                    SnackBar(
                                      content:
                                          Text('正在连接到设备: ${device.name}...'),
                                      duration: const Duration(seconds: 1),
                                    ),
                                  );

                                  bool success =
                                      await _bleService.connectToDevice(device);

                                  // 无论成功与否，都更新主页状态
                                  setState(() {});

                                  if (success) {
                                    scaffoldMessenger.showSnackBar(
                                      SnackBar(
                                        content:
                                            Text('已成功连接到设备: ${device.name}'),
                                        backgroundColor: Colors.green,
                                      ),
                                    );
                                  } else {
                                    scaffoldMessenger.showSnackBar(
                                      SnackBar(
                                        content:
                                            Text('连接到设备 ${device.name} 失败'),
                                        backgroundColor: Colors.red,
                                      ),
                                    );
                                  }
                                },
                                child: const Text('连接'),
                              ),
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('关闭'),
            ),
          ],
        );
      }),
    );
  }

  // 确认退出登录
  void _confirmLogout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('您确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              // 保存引用
              final navigator = Navigator.of(context);

              navigator.pop();
              await _userService.logout();
              navigator.pushReplacementNamed('/login');
            },
            child: const Text('确认'),
          ),
        ],
      ),
    );
  }
}
