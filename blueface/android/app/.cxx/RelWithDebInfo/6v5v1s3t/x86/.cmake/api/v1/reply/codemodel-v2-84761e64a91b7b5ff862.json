{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/GitHub/hanjiapp/blueface/android/app/.cxx/RelWithDebInfo/6v5v1s3t/x86", "source": "/Users/<USER>/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}