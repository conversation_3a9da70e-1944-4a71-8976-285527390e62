# 简化离线模式功能说明

## 🎯 功能概述

已成功简化离线模式，以焊口编号作为唯一标识，专注于真实的焊口数据管理。移除了复杂的离线项目管理，只保留核心的焊接数据记录和状态管理。

## 🔄 简化后的数据模型

### 核心概念：
- **焊口编号**: 作为唯一标识符，格式为 `YYYYMMDDNNN`
- **焊接记录**: 每个焊口编号对应一条完整的焊接记录
- **状态管理**: 只有成功/失败两种状态，失败则继续使用同一编号

## 📊 数据结构设计

### 1. **焊口编号管理**
```dart
// 当前焊口编号
String _currentJointNumber = '20240108001';

// 焊口编号生成规则
格式: YYYYMMDDNNN
示例: 20240108001 (2024年1月8日第1个焊口)
```

### 2. **焊接记录结构**
```dart
Map<String, dynamic> _currentWeldingRecord = {
  'jointNumber': '20240108001',           // 焊口编号
  'status': 0,                           // 状态: 0=开始, 1=成功, -1=失败
  'statusDescription': '焊接开始',        // 状态描述
  'startTime': '2024-01-08T14:30:00Z',   // 开始时间
  'completionTime': '',                   // 完成时间
  'projectId': 'PRJ001',                 // 项目ID
  'projectName': '某某工程项目',          // 项目名称
  'deviceInfo': {...},                   // 设备信息
  'weldingData': '01020304...',          // 原始焊机数据
  'parsedWeldingData': {...},            // 解析后的焊机数据
  'locationData': '纬度:39.9042...',     // GPS位置数据
};
```

### 3. **本地存储键值**
```dart
// 焊接记录存储
'welding_record_${jointNumber}' -> 完整焊接记录JSON字符串
'welding_time_${jointNumber}'   -> 焊接时间戳
'welding_status_${jointNumber}' -> 焊接状态(0/1/-1)

// 当前状态
'currentWeldingJointNumber'     -> 当前焊口编号
'hasUnfinishedWelding'          -> 是否有未完成焊接
'weldingStartTime'              -> 焊接开始时间
```

## 🔄 简化后的操作流程

### 完整的焊接流程：
```
1. 系统启动
   ↓
2. 加载/生成当前焊口编号
   ↓
3. 检查是否有未完成的焊接
   ↓
4. 如果有未完成焊接 → 继续使用当前编号
   如果没有 → 使用当前编号开始新焊接
   ↓
5. 收集焊接数据(设备信息、焊机数据、GPS、项目)
   ↓
6. 开始焊接 → 保存焊接记录
   ↓
7. 监控焊接状态
   ↓
8. 手动完成焊接
   ↓
9. 判断焊接结果:
   - 成功 → 生成新的焊口编号
   - 失败 → 继续使用当前编号
   ↓
10. 上传焊接数据
```

## 🔧 技术实现

### 1. **焊口编号生成和管理**
```dart
// 生成新的焊口编号
Future<void> _generateNewJointNumber() async {
  try {
    String newJointNumber = await _jointNumberService.generateWeldingJointNumber();
    setState(() {
      _currentJointNumber = newJointNumber;
      _statusMessage = '生成新焊口编号: $newJointNumber';
    });
    
    // 加载新焊口的焊接记录
    await _loadCurrentWeldingRecord();
  } catch (e) {
    print('生成新焊口编号失败: $e');
  }
}

// 加载当前焊接记录
Future<void> _loadCurrentWeldingRecord() async {
  try {
    final prefs = await SharedPreferences.getInstance();
    
    // 获取当前焊口编号的焊接记录
    String recordData = prefs.getString('welding_record_$_currentJointNumber') ?? '';
    
    if (recordData.isNotEmpty) {
      // 解析存储的焊接记录
      Map<String, dynamic> record = {
        'jointNumber': _currentJointNumber,
        'status': await _jointNumberService.getWeldingStatus(_currentJointNumber),
        'data': recordData,
        'timestamp': prefs.getString('welding_time_$_currentJointNumber') ?? '',
      };
      
      setState(() {
        _currentWeldingRecord = record;
      });
    } else {
      // 初始化新的焊接记录
      setState(() {
        _currentWeldingRecord = {
          'jointNumber': _currentJointNumber,
          'status': 0, // 未开始
          'data': '',
          'timestamp': '',
        };
      });
    }
  } catch (e) {
    print('加载当前焊接记录失败: $e');
  }
}
```

### 2. **焊接记录保存**
```dart
// 记录焊接开始
Future<void> _recordWeldingStart() async {
  try {
    final prefs = await SharedPreferences.getInstance();
    
    // 更新当前焊接记录
    Map<String, dynamic> weldingRecord = {
      'jointNumber': _currentJointNumber,
      'status': 0, // 开始状态
      'statusDescription': '焊接开始',
      'startTime': DateTime.now().toIso8601String(),
      'projectId': _currentProject?.id ?? '',
      'projectName': _currentProject?.name ?? '',
      'deviceInfo': _deviceInfo,
      'weldingData': _weldingData,
      'parsedWeldingData': _parsedWeldingData,
      'locationData': _locationData,
    };

    // 保存到本地存储
    await prefs.setString('welding_record_$_currentJointNumber', 
        weldingRecord.toString());
    await prefs.setString('welding_time_$_currentJointNumber', 
        DateTime.now().toIso8601String());

    setState(() {
      _currentWeldingRecord = weldingRecord;
    });
  } catch (e) {
    print('记录焊接开始失败: $e');
  }
}

// 更新当前焊接记录
Future<void> _updateCurrentWeldingRecord(int weldingResult) async {
  try {
    final prefs = await SharedPreferences.getInstance();
    String statusDescription = _jointNumberService.getStatusDescription(weldingResult);

    // 更新当前焊接记录
    _currentWeldingRecord['status'] = weldingResult;
    _currentWeldingRecord['statusDescription'] = statusDescription;
    _currentWeldingRecord['completionTime'] = DateTime.now().toIso8601String();

    // 保存到本地存储
    await prefs.setString('welding_record_$_currentJointNumber', 
        _currentWeldingRecord.toString());
    await prefs.setInt('welding_status_$_currentJointNumber', weldingResult);

    setState(() {
      // 触发界面更新
    });
  } catch (e) {
    print('更新焊接记录失败: $e');
  }
}
```

### 3. **焊接结果处理**
```dart
// 处理焊接完成
Future<void> _handleWeldingCompletion() async {
  try {
    // 假设焊接成功（实际应该根据真实状态判断）
    int weldingResult = WeldingJointNumberService.WELDING_SUCCESS;
    
    // 处理焊接结果
    bool resultHandled = await _jointNumberService.handleWeldingResult(weldingResult);
    
    if (resultHandled) {
      // 更新当前焊接记录
      await _updateCurrentWeldingRecord(weldingResult);
      
      setState(() {
        _statusMessage = '焊接完成！结果已记录';
      });
    } else {
      setState(() {
        _statusMessage = '焊接完成，但结果记录失败';
      });
    }
  } catch (e) {
    print('处理焊接完成失败: $e');
  }
}
```

## 📋 状态管理逻辑

### 焊接状态定义：
- **0**: 焊接开始/进行中
- **1**: 焊接成功
- **-1**: 焊接失败

### 焊口编号更新规则：
1. **焊接成功**: 自动生成下一个焊口编号
2. **焊接失败**: 继续使用当前焊口编号
3. **应用重启**: 检查未完成焊接，恢复状态

### 数据持久化：
- 每次焊接操作都保存到本地存储
- 使用焊口编号作为存储键值
- 支持离线操作和数据恢复

## 🎯 简化后的优势

### 1. **数据模型简化**
- ✅ 移除复杂的离线项目管理
- ✅ 以焊口编号为核心的数据组织
- ✅ 清晰的状态管理逻辑
- ✅ 简化的存储结构

### 2. **操作流程优化**
- ✅ 专注于真实焊接数据
- ✅ 自动化的编号管理
- ✅ 智能的状态恢复
- ✅ 简化的用户操作

### 3. **性能提升**
- ✅ 减少数据存储开销
- ✅ 提高数据查询效率
- ✅ 简化界面渲染逻辑
- ✅ 降低内存使用

### 4. **维护便利性**
- ✅ 清晰的代码结构
- ✅ 简化的错误处理
- ✅ 易于扩展的架构
- ✅ 便于调试和测试

## 🔮 数据上传结构

### 简化后的上传数据包：
```json
{
  "jointNumber": "20240108001",
  "timestamp": "2024-01-08T14:35:00.000Z",
  "status": 1,
  "statusDescription": "焊接成功",
  "startTime": "2024-01-08T14:30:00.000Z",
  "completionTime": "2024-01-08T14:35:00.000Z",
  "projectInfo": {
    "id": "PRJ001",
    "name": "某某工程项目"
  },
  "deviceInfo": {
    "machineNumber": "WM001",
    "weldingStandard": "ISO 13847",
    "machineType": "自动焊机"
  },
  "weldingData": {
    "raw": "01020304050607...",
    "parsed": {...}
  },
  "locationData": "纬度: 39.9042, 经度: 116.4074"
}
```

## 🎉 总结

通过简化离线模式，系统现在：

1. **专注核心功能**: 以焊口编号为中心的数据管理
2. **简化操作流程**: 移除复杂的离线项目管理
3. **提高数据质量**: 确保每个焊口的数据完整性
4. **优化用户体验**: 更直观的状态管理和操作反馈
5. **便于维护扩展**: 清晰的代码结构和数据模型

这种简化的设计更符合实际的焊接作业需求，专注于真实的焊口数据管理，提供了更好的性能和用户体验！🚀
