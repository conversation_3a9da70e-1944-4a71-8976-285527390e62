# blueface焊接工程管理应用文档

## 项目概述

blueface是一款使用Flutter开发的焊接工程管理移动应用，旨在通过数字化手段提升焊接工程的管理效率、数据准确性和工作流程标准化。应用集成了蓝牙通信、人脸识别、照片采集和数据管理等功能，为焊接工程师和管理人员提供全方位的工作支持。

## 系统架构

### 架构模式
项目采用分层架构，遵循MVC/MVVM模式，主要包含以下层次：
- 数据层(models)：定义数据结构和业务实体
- 服务层(services)：处理业务逻辑和数据操作
- 视图层(screens/widgets)：提供用户界面和交互

### 目录结构
```
lib/
├── models/         # 数据模型定义
├── screens/        # 应用页面
│   ├── tabs/       # 主页标签页内容
│   ├── offline/    # 离线功能相关页面
│   └── queries/    # 查询功能相关页面
├── services/       # 业务服务实现
├── utils/          # 工具类和辅助函数
├── widgets/        # 可复用UI组件
├── vision_detector_views/ # 人脸识别相关视图
├── app.dart        # 应用程序主体
└── main.dart       # 应用程序入口
```

## 核心功能实现

### 1. 身份验证与用户管理

#### 1.1 登录系统
```dart
// 伪代码：登录流程
class LoginScreen {
  // 用户验证流程
  Future<bool> verifyUser(String username, String password) async {
    try {
      // 调用API进行用户验证
      // 存储认证token
      // 返回验证结果
    } catch (error) {
      // 错误处理
    }
  }
  
  // 人脸识别验证
  void navigateToFaceDetection() {
    // 跳转到人脸识别页面
  }
}
```

#### 1.2 人脸识别实现
应用使用Google ML Kit进行人脸检测和活体识别，通过检测用户的眨眼和头部转动来验证真人操作，防止照片欺骗。

关键步骤：
1. 相机初始化
2. 人脸检测配置
3. 活体检测（眨眼/头部转动）
4. 照片获取与服务器验证

### 2. 蓝牙通信模块

#### 2.1 设备管理
```dart
// 伪代码：蓝牙服务
class BleService {
  // 扫描设备
  Future<void> startScan() async {
    // 初始化蓝牙
    // 搜索可用设备
    // 更新设备列表
  }
  
  // 连接设备
  Future<bool> connectToDevice(BluetoothDevice device) async {
    // 建立连接
    // 发现服务和特征
    // 配置数据通知
    // 返回连接状态
  }
  
  // 发送命令
  Future<bool> sendCommand(List<int> data) async {
    // 添加命令校验信息
    // 发送数据包
    // 等待响应
    // 返回结果
  }
}
```

#### 2.2 命令服务
该服务封装了与焊接设备通信的专用命令，包括参数设置、状态查询、操作控制等，使用CRC校验确保数据完整性。

### 3. 焊接作业管理

#### 3.1 焊接工艺流程
```dart
// 伪代码：焊接流程控制
class WeldingScreen {
  // 初始化焊接流程
  Future<void> startWelding() async {
    // 准备阶段
    // 参数配置
    // 执行焊接
    // 完成记录
  }
  
  // 参数配置
  void configureWeldingParameters() {
    // 设置焊接参数
    // 发送到设备
  }
  
  // 记录焊接结果
  Future<void> recordWeldingResult() async {
    // 保存焊接数据
    // 处理相关图片
    // 上传或本地存储
  }
}
```

#### 3.2 焊接记录模型
```dart
// 伪代码：焊接记录模型
class WeldRecord {
  String id;
  String jointId;
  String projectName;
  String projectId;
  String processType;
  String operatorName;
  String deviceId;
  DateTime createdAt;
  bool hasImages;
  int imageCount;
  String? notes;
  
  // JSON序列化和反序列化方法
  Map<String, dynamic> toJson() {...}
  factory WeldRecord.fromJson(Map<String, dynamic> json) {...}
}
```

### 4. 数据管理与同步

#### 4.1 本地存储
应用支持离线操作，数据先存储在本地文件系统，使用JSON格式保存。

```dart
// 伪代码：本地数据存储
Future<bool> saveRecords(List<WeldRecord> records) async {
  try {
    // 获取应用文档目录
    // 写入JSON格式数据
    // 返回存储结果
  } catch (error) {
    // 错误处理
    return false;
  }
}
```

#### 4.2 数据同步
```dart
// 伪代码：数据同步机制
Future<Map<String, dynamic>> uploadAllPendingRecords() async {
  // 获取所有待上传记录
  // 循环上传每条记录
  // 返回上传统计结果
}
```

### 5. 图片采集与管理

#### 5.1 设备照片和管道照片
应用提供专门的页面用于拍摄设备和管道照片，包括:
- 相机控制
- 照片预览
- 照片上传
- 与焊接记录关联

#### 5.2 照片上传服务
```dart
// 伪代码：照片上传
Future<bool> uploadPhoto(File imageFile, String recordId) async {
  try {
    // 准备多部分表单数据
    // 添加认证信息
    // 执行上传请求
    // 处理响应
    return true;
  } catch (error) {
    // 错误处理
    return false;
  }
}
```

## 用户界面设计

### 1. 主界面结构
主界面采用底部导航栏设计，分为四个主要模块：
- 焊接作业：焊接操作和记录管理
- 项目管理：项目创建和管理
- 查询中心：数据查询和统计
- 离线模式：离线功能和数据同步

### 2. 关键页面设计

#### 2.1 焊接配置页面
```dart
// 伪代码：焊接配置UI
Widget build(BuildContext context) {
  return Scaffold(
    appBar: AppBar(title: Text('焊接参数配置')),
    body: Column(
      children: [
        // 项目信息显示
        // 参数配置表单
        // 操作按钮
      ]
    )
  );
}
```

#### 2.2 焊接操作页面
该页面提供焊接过程的实时监控，包括进度指示、状态显示和操作控制。采用视觉化的方式展示焊接进程，提高用户体验。

### 3. 蓝牙设备管理界面
通过弹窗形式显示蓝牙设备管理，功能包括：
- 设备扫描
- 显示可用设备
- 连接/断开设备
- 连接状态指示

## 技术实现细节

### 1. 状态管理
应用采用基于StatefulWidget的状态管理方式，对于复杂页面使用流(Stream)进行状态通知和更新。

### 2. 异步操作处理
大量使用Future和async/await机制处理异步操作，包括：
- 网络请求
- 蓝牙通信
- 文件操作
- 相机控制

### 3. 安全性考虑
- 使用flutter_secure_storage存储敏感信息
- API请求使用token认证
- 人脸识别增强身份验证安全性

### 4. 错误处理
各服务模块都实现了完善的错误处理机制，确保应用稳定运行：
```dart
// 伪代码：错误处理示例
try {
  // 执行操作
} catch (e) {
  // 记录错误日志
  print('操作失败: $e');
  // 向用户显示友好提示
  showToast('操作无法完成，请稍后重试');
  // 返回安全的默认值或状态
}
```

## 扩展与优化方向

### 1. 性能优化
- 优化蓝牙通信效率
- 改进图片处理和上传机制
- 实现更高效的本地数据存储和查询

### 2. 功能扩展
- 增加数据分析和报表功能
- 支持多语言国际化
- 添加更多焊接工艺类型支持

### 3. 用户体验改进
- 优化UI响应速度
- 增强错误提示的友好性
- 改进离线模式下的用户体验

## 总结

blueface是一款功能全面的焊接工程管理应用，通过整合移动设备的蓝牙通信、相机功能和网络能力，为焊接工作提供数字化管理解决方案。其模块化的设计和完善的功能实现，使其能够满足现代焊接工程的管理需求，提高工作效率和数据准确性。 



经度  01 10 01 5E 00 04 08 XX XX CRC 返回 01 10 01 5E 00 04 A1 E4
纬度  01 10 01 6D 00 04 08 XX XX CRC 返回 01 10 01 6D 00 04 51 EB
海拔 01 06 01 68 XX XX CRC 返回 01 06 01 68