name: google_mlkit_face_detection
description: A Flutter plugin to use Google's ML Kit Face Detection to detect faces in an image, identify key facial features, and get the contours of detected faces.
version: 0.12.0
homepage: https://github.com/flutter-ml/google_ml_kit_flutter
repository: https://github.com/flutter-ml/google_ml_kit_flutter/tree/master/packages/google_mlkit_face_detection

environment:
  sdk: ">=2.16.0 <4.0.0"
  flutter: ">=2.5.0"

dependencies:
  flutter:
    sdk: flutter
  google_mlkit_commons: ^0.9.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  plugin:
    platforms:
      android:
        package: com.google_mlkit_face_detection
        pluginClass: GoogleMlKitFaceDetectionPlugin
      ios:
        pluginClass: GoogleMlKitFaceDetectionPlugin
